// Simple test script to verify the chat API is working
// Run this with: node test-chat-api.js

const testChatAPI = async () => {
  try {
    console.log('Testing Chat API...');
    
    const response = await fetch('http://localhost:3000/api/ai/generate-chat-dialogue', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: 'Daily Routine',
        difficulty: 'beginner',
        conversationLength: 6,
        speakers: 2,
        targetCharacters: ['起床', '吃饭']
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API Error:', errorData);
      return;
    }

    const dialogue = await response.json();
    console.log('Success! Generated dialogue:', JSON.stringify(dialogue, null, 2));
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Only run if this is the main module
if (require.main === module) {
  testChatAPI();
}

module.exports = { testChatAPI };
