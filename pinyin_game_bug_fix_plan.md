# Pinyin Game Bug Fix Plan

## Problem Description
The pinyin selection game incorrectly marks correct answers as wrong. The user reported that clicking the correct pinyin results in a "wrong" feedback. The `item.pinyin` property is confirmed to be a string, not an array of strings.

## Identified Issues

1.  **Incorrect Generation of Options in `generateOptions`**:
    *   The `flatMap` operation on `v.pinyin` (a string) in `src/features/adaptiveVocabularyGame/gameTypes/pinyinGameLogic.ts:20` results in individual characters being collected as incorrect pinyins instead of full pinyin strings.
    *   The subsequent filtering logic might not correctly exclude the exact correct pinyin from the incorrect options.

2.  **Loose Evaluation in `evaluateAnswer`**:
    *   The `includes()` method used in `src/features/adaptiveVocabularyGame/gameTypes/pinyinGameLogic.ts:32` allows partial matches (e.g., "ni3" matching "ni3hao3"), which is not the desired behavior for an exact pinyin selection.

3.  **Incorrect `getCorrectAnswerText`**:
    *   The function in `src/features/adaptiveVocabularyGame/gameTypes/pinyinGameLogic.ts:36` returns only the first character of the pinyin string, not the full correct pinyin.

## Detailed Plan

### Goal 1: Fix `generateOptions` to correctly generate pinyin options.
*   **Action:** Modify the logic to collect `incorrectPinyins` by directly mapping `v.pinyin` (which is a string) from other vocabulary items. Ensure that the filtering correctly excludes the *exact* correct pinyin string from the list of incorrect options.

### Goal 2: Fix `evaluateAnswer` to perform an exact match.
*   **Action:** Change the comparison from `item.pinyin?.includes(selectedAnswer)` to a strict equality check: `item.pinyin === selectedAnswer`. This will ensure that the selected answer must exactly match the correct pinyin string.

### Goal 3: Fix `getCorrectAnswerText` to return the full pinyin.
*   **Action:** Update the function to return the entire `item.pinyin` string instead of just its first character.

## Visualizing the Plan

```mermaid
graph TD
    A[User reports bug: Pinyin game wrong answer] --> B{Analyze pinyinGameLogic.ts};
    B --> C{Identify issues in generateOptions, evaluateAnswer, getCorrectAnswerText};
    C --> D[Goal 1: Fix generateOptions];
    C --> E[Goal 2: Fix evaluateAnswer];
    C --> F[Goal 3: Fix getCorrectAnswerText];
    D --> D1[Collect full pinyin strings for incorrect options];
    D --> D2[Filter out exact correct pinyin from incorrect options];
    E --> E1[Change evaluation to exact equality (===)];
    F --> F1[Return full item.pinyin string];
    D1 & D2 & E1 & F1 --> G{Propose plan to user};
    G -- User approves --> H[Ask to write to markdown];
    H --> I[Switch to Code Mode for implementation];