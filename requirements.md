# Master-Chinese App Requirements Specification

## 1. Introduction

### 1.1 App Vision & Mission

**Vision:**
To make learning Chinese grammar and vocabulary more engaging, personalized, and effective through the use of LLMs to generate dynamic, tailored exercises and track user progress until mastery is achieved.

**Mission:**
To provide a structured and adaptive learning pathway that helps users master Chinese grammar and vocabulary points systematically and at their own pace.

### 1.2 Scope
This application will provide a comprehensive platform for learning Chinese grammar and vocabulary through structured content organization, progress tracking, and AI-generated practice exercises.

## 2. System Architecture

### 2.1 High-Level Architecture
- **Frontend**: Next.js-based application with responsive design
- **Backend**: Serverless architecture using Google Firestore
- **Database**: Google Firestore for data storage
- **External Services**: Google Gemini API for LLM-powered exercise generation
- **Authentication**: Firebase Authentication

### 2.2 Technology Stack
- **Frontend**: Next.js, React, Shadcn UI, TanStack Query
- **Backend**: Firebase/Firestore SDK
- **Database**: Google Firestore
- **Testing**: Jest, React Testing Library, Cypress
- **Deployment**: Vercel, Firebase Hosting

## 3. Data Models

### 3.1 Firestore Collections

#### 3.1.1 Users Collection
```javascript
// users/{userId}
{
  username: String, // required, unique
  email: String, // required, unique
  createdAt: Timestamp,
  lastLogin: Timestamp,
  role: String, // 'user' or 'admin', default: 'user'
  apiKey: String, // user's Gemini API key (encrypted)
}
```

#### 3.1.2 Grammar Points Collection
```javascript
// grammarPoints/{pointId}
{
  chapterNumber: Number, // required
  chapterTitle: String, // required
  pointId: String, // required, unique, e.g., "ch1_既...又..."
  title: String, // required, e.g., "既...又..."
  explanation: String, // required
  examples: [
    {
      chinese: String, // required
      pinyin: String, // required
      english: String // required
    }
  ],
  difficulty: String, // 'beginner', 'intermediate', or 'advanced'
  relatedPoints: [String] // Array of pointIds
}
```

#### 3.1.3 Vocabulary Items Collection
```javascript
// vocabularyItems/{itemId}
{
  chapterNumber: Number, // required
  chapterTitle: String, // required
  word: String, // required
  pinyin: String, // required
  partOfSpeech: String, // required, e.g., "名" (noun), "动" (verb)
  definitions: [String], // required
  examples: [
    {
      chinese: String, // required
      pinyin: String, // required
      english: String // required
    }
  ],
  difficulty: String // 'beginner', 'intermediate', or 'advanced'
}
```

#### 3.1.4 User Progress Collection
```javascript
// userProgress/{progressId}
{
  userId: String, // required, reference to users collection
  itemType: String, // 'grammar' or 'vocabulary'
  itemId: String, // required, reference to grammarPoints or vocabularyItems
  status: String, // 'new', 'in-progress', or 'mastered', default: 'new'
  attempts: Number, // default: 0
  correctAttempts: Number, // default: 0
  lastPracticed: Timestamp,
  masteryScore: Number, // 0-100 score indicating mastery level
  notes: String
}
```

#### 3.1.5 Exercises Collection
```javascript
// exercises/{exerciseId}
{
  itemType: String, // 'grammar' or 'vocabulary'
  itemId: String, // reference to grammarPoints or vocabularyItems
  type: String, // 'fill-in-blank', 'multiple-choice', 'translation', or 'sentence-formation'
  prompt: String, // required
  options: [String], // For multiple choice
  correctAnswer: String, // required
  difficulty: Number, // required, 1-5
  explanation: String, // required
  createdAt: Timestamp
}
```

## 4. Functional Requirements

### 4.1 User Management
4.1.1. User registration and login using Firebase Authentication
4.1.2. User profile management
4.1.3. API key management for Gemini integration
4.1.4. User preferences and settings

### 4.2 Content Organization
4.2.1. Organize content into Grammar and Vocabulary sections
4.2.2. Further divide each section into Chapters and Points/Items
4.2.3. Provide search and filter functionality for content
4.2.4. Allow users to bookmark specific grammar points or vocabulary items

### 4.3 Learning Interface
4.3.1. Display grammar explanations with examples
4.3.2. Show vocabulary items with definitions, pronunciations, and example sentences
4.3.3. Implement audio playback for pronunciation examples
4.3.4. Provide visual indicators for mastery status (new, in-progress, mastered)

### 4.4 Exercise Generation
4.4.1. Generate varied exercise types using Google Gemini:
   - Fill-in-the-blank exercises
   - Multiple-choice questions
   - Translation exercises
   - Sentence formation exercises
4.4.2. Implement Gemini API integration:
```javascript
// Example Gemini API integration
async function generateExercise(grammarPoint, exerciseType, difficulty, apiKey) {
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-pro" });
  
  const prompt = buildPrompt(grammarPoint, exerciseType, difficulty);
  
  const result = await model.generateContent({
    contents: [
      {
        role: "user",
        parts: [
          {
            text: prompt
          }
        ]
      }
    ],
    generationConfig: {
      temperature: 0.7,
      maxOutputTokens: 1024,
      responseFormat: { type: "json" }
    }
  });
  
  const response = result.response;
  return JSON.parse(response.text());
}
```

### 4.5 Answer Validation
4.5.1. Implement automated validation for multiple-choice and fill-in-the-blank exercises
4.5.2. Use Gemini for validating free-form answers:
```javascript
async function validateAnswer(grammarPoint, userAnswer, correctAnswer, apiKey) {
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-pro" });
  
  const prompt = `
    Grammar point: ${grammarPoint.title}
    Explanation: ${grammarPoint.explanation}
    User's answer: ${userAnswer}
    Correct answer: ${correctAnswer}
    
    Evaluate if the user's answer correctly applies the grammar point. 
    Provide a score from 0-100 and specific feedback on any errors.
  `;
  
  const result = await model.generateContent({
    contents: [
      {
        role: "user",
        parts: [
          {
            text: prompt
          }
        ]
      }
    ],
    generationConfig: {
      temperature: 0.3,
      maxOutputTokens: 1024,
      responseFormat: { type: "json" }
    }
  });
  
  const response = result.response;
  return JSON.parse(response.text());
}
```

### 4.6 Progress Tracking
4.6.1. Track user attempts and success rates for each grammar point and vocabulary item
4.6.2. Calculate and update mastery scores based on performance
4.6.3. Provide visual progress indicators and statistics
4.6.4. Generate personalized learning recommendations based on progress

### 4.7 Content Migration
4.7.1. Develop scripts to parse existing markdown files and populate Firestore
4.7.2. Implement validation to ensure data integrity during migration
4.7.3. Create admin interface for content management

## 5. Client-Side SDK Integration

### 5.1 Firebase Authentication
```javascript
// Example Firebase Authentication integration
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from "firebase/auth";

// User registration
async function registerUser(email, password) {
  const auth = getAuth();
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error("Error registering user:", error);
    throw error;
  }
}

// User login
async function loginUser(email, password) {
  const auth = getAuth();
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error("Error logging in:", error);
    throw error;
  }
}
```

### 5.2 Firestore Data Access
```javascript
// Example Firestore data access
import { getFirestore, collection, doc, getDoc, getDocs, query, where } from "firebase/firestore";

// Get all grammar chapters
async function getGrammarChapters() {
  const db = getFirestore();
  const chaptersMap = new Map();
  
  const grammarPointsRef = collection(db, "grammarPoints");
  const querySnapshot = await getDocs(grammarPointsRef);
  
  querySnapshot.forEach((doc) => {
    const data = doc.data();
    if (!chaptersMap.has(data.chapterNumber)) {
      chaptersMap.set(data.chapterNumber, {
        chapterNumber: data.chapterNumber,
        chapterTitle: data.chapterTitle,
        points: []
      });
    }
    
    chaptersMap.get(data.chapterNumber).points.push({
      id: doc.id,
      title: data.title
    });
  });
  
  return Array.from(chaptersMap.values()).sort((a, b) => a.chapterNumber - b.chapterNumber);
}

// Get specific grammar point
async function getGrammarPoint(pointId) {
  const db = getFirestore();
  const docRef = doc(db, "grammarPoints", pointId);
  const docSnap = await getDoc(docRef);
  
  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() };
  } else {
    throw new Error("Grammar point not found");
  }
}
```

### 5.3 User Progress Management
```javascript
// Example user progress management
import { getFirestore, collection, addDoc, updateDoc, query, where, getDocs } from "firebase/firestore";

// Update user progress
async function updateUserProgress(userId, itemType, itemId, progressData) {
  const db = getFirestore();
  const progressRef = collection(db, "userProgress");
  const q = query(
    progressRef, 
    where("userId", "==", userId),
    where("itemType", "==", itemType),
    where("itemId", "==", itemId)
  );
  
  const querySnapshot = await getDocs(q);
  
  if (querySnapshot.empty) {
    // Create new progress entry
    await addDoc(progressRef, {
      userId,
      itemType,
      itemId,
      status: progressData.status || "new",
      attempts: progressData.attempts || 0,
      correctAttempts: progressData.correctAttempts || 0,
      lastPracticed: progressData.lastPracticed || new Date(),
      masteryScore: progressData.masteryScore || 0,
      notes: progressData.notes || ""
    });
  } else {
    // Update existing progress entry
    const docRef = querySnapshot.docs[0].ref;
    await updateDoc(docRef, progressData);
  }
}
```

## 6. Non-Functional Requirements

### 6.1 Performance
6.1.1. Client-side operations should be responsive with minimal loading times
6.1.2. Gemini API calls should complete within 5 seconds
6.1.3. The application should handle offline operations gracefully with data synchronization when online

### 6.2 Security
6.2.1. Implement secure storage of user Gemini API keys
6.2.2. Use Firebase Authentication for secure user management
6.2.3. Implement proper Firestore security rules to protect user data
6.2.4. Ensure client-side validation and sanitization of all user inputs

### 6.3 Reliability
6.3.1. Implement error handling and fallback mechanisms for Gemini API failures
6.3.2. Ensure data consistency with Firestore transactions where appropriate
6.3.3. Implement offline capabilities for core learning features
6.3.4. Provide clear error messages and recovery options for users

### 6.4 Usability
6.4.1. The interface should be responsive and work on mobile devices
6.4.2. The application should be accessible according to WCAG 2.1 AA standards
6.4.3. The UI should provide clear feedback for user actions
6.4.4. Loading states should be indicated for operations taking longer than 1 second

### 6.5 Scalability
6.5.1. Design the application to handle growing user base and content
6.5.2. Implement efficient Firestore queries with proper indexing
6.5.3. Use caching strategies to minimize database reads

## 7. Implementation Plan

### 7.1 Phase 1: Setup and Infrastructure
7.1.1. Set up Next.js project with Shadcn UI
7.1.2. Configure Firebase/Firestore integration
7.1.3. Implement authentication system
7.1.4. Develop data migration scripts for existing markdown content

### 7.2 Phase 2: Core Functionality
7.2.1. Implement content organization and display
7.2.2. Develop progress tracking system
7.2.3. Create basic exercise functionality
7.2.4. Implement user management features

### 7.3 Phase 3: Gemini Integration
7.3.1. Develop prompt engineering for exercise generation
7.3.2. Implement answer validation using Gemini
7.3.3. Create user API key management system
7.3.4. Develop fallback mechanisms for API failures

### 7.4 Phase 4: Testing and Refinement
7.4.1. Conduct comprehensive testing of all features
7.4.2. Optimize performance and fix identified issues
7.4.3. Gather user feedback and make improvements
7.4.4. Prepare for production deployment

## 8. Appendix

### 8.1 Glossary
- **LLM**: Large Language Model
- **Grammar Point**: A specific grammatical structure or rule in Chinese
- **Vocabulary Item**: A Chinese word or phrase to be learned
- **Mastery Score**: A numerical representation of a user's proficiency with a specific item

### 8.2 References
- Chinese Grammar Wiki: https://resources.allsetlearning.com/chinese/grammar/
- HSK Standard Course textbooks
- Chinese Pedagogy Best Practices
- Firebase Documentation: https://firebase.google.com/docs
- Google Gemini API Documentation: https://ai.google.dev/docs
