# Vocabulary Game Architecture Refactoring Plan

This document outlines a proposed architecture to improve the maintainability and extensibility of the vocabulary game system, particularly when adding new game types. The goal is to decouple game-specific logic and UI from the generic game flow managed by XState, promoting modularity, reusability, and flexibility.

## Current Challenges

The existing implementation, while using XState, still requires modifications across multiple files (e.g., the XState machine, `AdaptiveVocabularyGame` component) when a new game type is introduced. This indicates a tight coupling that hinders independent development and maintenance of game types.

## Proposed Architecture

The new architecture introduces a clear interface for game-specific logic, allowing each game type to be self-contained. The core `AdaptiveVocabularyGame` component and the XState machine will become more generic, delegating game-specific behaviors to these new modules.

```mermaid
graph TD
    A[Game Selection Page] --> B{Select Game Type};
    B --> C[Game Page (e.g., CharacterToMeaningGamePage)];
    C -- Imports and Provides Game Logic --> D[AdaptiveVocabularyGame Component];
    D -- Uses XState Machine --> E[adaptiveVocabularyGameMachine (Generic Flow)];

    subgraph Game Logic Modules (Self-contained)
        F[CharacterToMeaningLogic.ts] -- Implements IGameLogic --> D;
        G[PinyinToCharacterLogic.ts] -- Implements IGameLogic --> D;
        H[NewGameTypeLogic.ts] -- Implements IGameLogic --> D;
    end

    E -- Delegates content-specific logic to --> F;
    E -- Delegates content-specific logic to --> G;
    E -- Delegates content-specific logic to --> H;

    D -- Renders UI using generic components --> I[QuestionDisplay.tsx];
    D -- Renders UI using generic components --> J[FeedbackDisplay.tsx];

    I -- Calls methods on IGameLogic to render specific content --> F;
    J -- Calls methods on IGameLogic to render specific content --> F;
```

## Detailed Plan Steps

1.  **Define `IGameLogic` Interface:**
    *   **File:** `src/features/adaptiveVocabularyGame/gameLogic.interface.ts`
    *   **Description:** This file will define the `IGameLogic` interface. This interface will specify the methods and properties that each game type must implement. These methods will abstract away game-specific details such as how to prepare question data, how to evaluate answers, and how to render game-specific UI elements.

2.  **Create Game Logic Implementations:**
    *   **Directory:** `src/features/adaptiveVocabularyGame/gameTypes/`
    *   **Files:**
        *   `src/features/adaptiveVocabularyGame/gameTypes/meaningGameLogic.ts`
        *   `src/features/adaptiveVocabularyGame/gameTypes/pinyinGameLogic.ts`
        *   `src/features/adaptiveVocabularyGame/gameTypes/englishToHanziGameLogic.ts`
        *   (and so on for any other existing or future game types)
    *   **Description:** For each existing game type, a dedicated file will be created within the new `gameTypes` directory. Each file will export an object that implements the `IGameLogic` interface. This object will encapsulate all the specific logic for that game type, including:
        *   Functions to generate question prompts based on `VocabularyItem`.
        *   Functions to generate multiple-choice options.
        *   Functions to evaluate a selected answer against the correct answer.
        *   Potentially, methods to provide game-specific UI rendering instructions or components.

3.  **Update `adaptiveVocabularyGame.types.ts`:**
    *   **File:** `src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.types.ts`
    *   **Changes:**
        *   Modify the `AdaptiveVocabularyGameContext` interface to include a new property: `gameLogic: IGameLogic`.
        *   Remove the existing `gameType` property from the context, as its role will be superseded by the `gameLogic` object, which inherently defines the game's type and behavior.

4.  **Refactor `adaptiveVocabularyGame.logic.ts`:**
    *   **File:** `src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.logic.ts`
    *   **Changes:**
        *   Adjust the `getInitialContext` function to accept the `IGameLogic` object as an input parameter. This `IGameLogic` object will then be assigned to the `gameLogic` property within the initial context.

5.  **Refactor `adaptiveVocabularyGame.actions.ts`:**
    *   **File:** `src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.actions.ts`
    *   **Changes:**
        *   Update actions that currently rely on `context.gameType` (e.g., `prepareQuestionAction`, `evaluateAnswerAction`) to instead utilize the methods provided by `context.gameLogic`. This eliminates the need for internal `switch` statements based on `gameType` within the actions, making them more generic.

6.  **Refactor `AdaptiveVocabularyGame.tsx`:**
    *   **File:** `src/components/games/vocabulary/AdaptiveVocabularyGame.tsx`
    *   **Changes:**
        *   Modify the component's props to accept `gameLogic: IGameLogic` instead of `gameType: GameType`.
        *   Pass this `gameLogic` prop directly to the XState machine's `input` property.
        *   Remove the internal `getQuestionPrompt` function. The `QuestionDisplay` and `FeedbackDisplay` components will now directly call methods on `current.context.gameLogic` to render game-specific content (e.g., `gameLogic.getQuestionPrompt(currentItem)`).

7.  **Update Game Pages:**
    *   **Files:** `src/app/vocabulary/games/character-to-meaning/page.tsx`, `src/app/vocabulary/games/pinyin-to-character/page.tsx`, etc.
    *   **Changes:**
        *   Each game page will now import its specific `IGameLogic` implementation (e.g., `meaningGameLogic`).
        *   This imported `gameLogic` object will then be passed as the `gameLogic` prop to the `AdaptiveVocabularyGame` component.

## Benefits of this Architecture

*   **Modularity:** Each game type's logic is encapsulated in its own module, making it easier to understand, develop, and test independently.
*   **Reusability:** The `AdaptiveVocabularyGame` component and the XState machine become generic, reusable components that can power any game type that implements the `IGameLogic` interface.
*   **Extensibility:** Adding a new game type primarily involves creating a new `IGameLogic` implementation and a new game page, with minimal or no changes to existing core files.
*   **Maintainability:** Changes to one game type are less likely to affect others, reducing the risk of regressions and simplifying debugging.
*   **Reduced Coupling:** The core game flow is decoupled from the specific content and presentation logic of individual games.