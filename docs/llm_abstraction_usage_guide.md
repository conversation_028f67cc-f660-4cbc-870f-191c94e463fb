# LLM Interaction Abstraction Service: Usage Guide

This guide explains how to use and extend the `LLMAbstractionService` for interacting with Large Language Models (LLMs) within the application. The service is designed to simplify LLM interactions, provide a unified API, and allow for easy extension to new LLM providers or features.

## Core Concepts

The service is built around a few key components:

*   **`LLMAbstractionService` ([`src/lib/llm-abstraction/llm-abstraction-service.ts`](../src/lib/llm-abstraction/llm-abstraction-service.ts:1)):** The main entry point for all LLM interactions. An instance is exported as `llmService`.
*   **Configuration ([`src/lib/llm-abstraction/config.ts`](../src/lib/llm-abstraction/config.ts:1)):** Manages API keys and model details, primarily loaded from environment variables.
*   **Adapters ([`src/lib/llm-abstraction/adapters/`](../src/lib/llm-abstraction/adapters/)):** Implement provider-specific logic. Currently, a `GoogleAIAdapter` is available.
*   **Types ([`src/lib/llm-abstraction/types.ts`](../src/lib/llm-abstraction/types.ts:1)):** Defines the core interfaces like `LLMService`, `GenerationOptions`, and `StructuredOutputOptions`.

## Configuration

The service loads its configuration from environment variables. To use it, ensure the following variables are set in your environment (e.g., in a `.env.local` file for local development):

*   `GOOGLE_API_KEY`: Your API key for Google AI services (e.g., Gemini).
*   `GOOGLE_DEFAULT_MODEL_ID`: The default Google AI model to use (e.g., "gemini-1.5-flash-latest").
*   `GOOGLE_SUPPORTS_JSON_MODE`: (Required) Set to "true" if the `GOOGLE_DEFAULT_MODEL_ID` natively supports JSON output mode, or "false" otherwise. The service will use this to determine the appropriate generation strategy.

The configuration loader ([`src/lib/llm-abstraction/config.ts`](../src/lib/llm-abstraction/config.ts:1)) will throw an error at startup if essential configurations like the API key are missing.

## Using the Service

Import the `llmService` instance to interact with LLMs.

```typescript
import { llmService } from '@/lib/llm-abstraction/llm-abstraction-service'; // Adjust path as needed
import { z } from 'zod';
```

### 1. Generating Raw Text Output

Use the `generateRawText` method when you need a simple text response from the LLM (e.g., generating a topic, summarizing text).

```typescript
async function getWritingTopic(category: string): Promise<string> {
  const prompt = `Generate a single, engaging writing topic appropriate for a student learning Chinese.
The topic should be suitable for the category: "${category}".
Provide only the topic text, no extra explanations or labels.
Example: "描述你最喜欢的动物"`;

  try {
    const topic = await llmService.generateRawText(prompt, {
      temperature: 0.7,
      maxTokens: 50,
    });
    return topic.trim();
  } catch (error) {
    console.error("Error generating writing topic:", error);
    return "Default topic: 我的爱好"; // Fallback
  }
}

// Example usage:
// const topic = await getWritingTopic("日常生活");
// console.log(topic);
```

**Parameters for `generateRawText`:**

*   `prompt` (string): The prompt to send to the LLM.
*   `options?` ([`GenerationOptions`](../src/lib/llm-abstraction/types.ts:1)): Optional parameters like `temperature`, `maxTokens`, or `modelConfig` to override the default model for this specific call.

### 2. Generating Structured JSON Output

Use the `generateStructuredOutput` method when you need the LLM to return data in a specific JSON format, validated against a Zod schema.

```typescript
// Define your Zod schema for the expected output
const exerciseSchema = z.object({
  type: z.string().describe("e.g., 'fill-in-blank', 'multiple-choice'"),
  prompt: z.string().describe("The exercise prompt in both Chinese and English"),
  options: z.array(z.string()).optional().describe("Only for multiple-choice"),
  correctAnswer: z.string().describe("The correct answer"),
  explanation: z.string().describe("Explanation of the correct answer"),
});

// Define a type for the exercise based on the schema
type Exercise = z.infer<typeof exerciseSchema>;

async function createExercise(itemDetails: string): Promise<Exercise | null> {
  const prompt = `Generate a multiple-choice exercise for the following Chinese grammar point: ${itemDetails}.
Format the output as a JSON object matching the schema.`;

  try {
    const exercise = await llmService.generateStructuredOutput(prompt, {
      responseSchema: exerciseSchema,
      temperature: 0.5,
    });
    return exercise;
  } catch (error) {
    console.error("Error generating exercise:", error);
    return null; // Fallback or further error handling
  }
}

// Example usage:
// const exercise = await createExercise("虽然...但是...");
// if (exercise) {
//   console.log(exercise.prompt);
// }
```

**Parameters for `generateStructuredOutput`:**

*   `prompt` (string): The prompt to send to the LLM.
*   `options` ([`StructuredOutputOptions`](../src/lib/llm-abstraction/types.ts:1)):
    *   `responseSchema` (ZodSchema): The Zod schema to validate the LLM's JSON output against. This is mandatory.
    *   Other optional parameters from `GenerationOptions` like `temperature`, `maxTokens`, `modelConfig`.

The service handles the complexities of ensuring the LLM returns valid JSON matching your schema, including internal retries or prompt adjustments if supported by the underlying adapter. The `llmService` uses the `GOOGLE_SUPPORTS_JSON_MODE` configuration to automatically choose the best strategy for generating structured JSON, whether through native JSON mode or by processing a text-based response.

## Extending the Service

The `LLMAbstractionService` is designed to be extensible, primarily through its adapter pattern.

### Adding a New LLM Provider

1.  **Define Configuration:**
    *   Update [`src/lib/llm-abstraction/types.ts`](../src/lib/llm-abstraction/types.ts:1) to include configuration types for the new provider if needed.
    *   Modify [`src/lib/llm-abstraction/config.ts`](../src/lib/llm-abstraction/config.ts:1) to load environment variables for the new provider (e.g., `OPENAI_API_KEY`, `OPENAI_DEFAULT_MODEL_ID`).
2.  **Create a New Adapter:**
    *   In the [`src/lib/llm-abstraction/adapters/`](../src/lib/llm-abstraction/adapters/) directory, create a new adapter class (e.g., `OpenAIAdapter.ts`) that implements the `LLMAdapter` interface ([`adapter-interface.ts`](../src/lib/llm-abstraction/adapters/adapter-interface.ts:1)).
    *   This new adapter will contain the logic specific to interacting with the new provider's SDK.
3.  **Update `LLMAbstractionService` (Potentially):**
    *   Modify [`llm-abstraction-service.ts`](../src/lib/llm-abstraction/llm-abstraction-service.ts:1) to:
        *   Instantiate the new adapter.
        *   Include logic to select the appropriate adapter based on the `modelConfig` provided in `GenerationOptions` or a default setting. (This part of the service would need to be enhanced for dynamic adapter selection).

### Adding New LLM Features (e.g., Streaming, Image Input)

1.  **Update `LLMAdapter` Interface:**
    *   Add new method signatures to [`adapter-interface.ts`](../src/lib/llm-abstraction/adapters/adapter-interface.ts:1) for the new feature (e.g., `streamText(...)`, `generateImageDescription(...)`).
2.  **Implement in Concrete Adapters:**
    *   Implement these new methods in each existing adapter (`GoogleAIAdapter.ts`, and any others).
3.  **Expose via `LLMService` Interface:**
    *   Add the new methods to the `LLMService` interface in [`types.ts`](../src/lib/llm-abstraction/types.ts:1).
4.  **Implement in `LLMAbstractionService`:**
    *   Implement the new methods in [`llm-abstraction-service.ts`](../src/lib/llm-abstraction/llm-abstraction-service.ts:1), delegating to the appropriate adapter methods.

## Best Practices

*   **Clear Prompts:** Write clear, specific prompts. For `generateStructuredOutput`, explicitly mention in the prompt that a JSON output matching a certain structure is expected.
*   **Schema Design:** Design your Zod schemas carefully to reflect the exact structure you need from the LLM.
*   **Error Handling:** Always wrap calls to `llmService` methods in `try...catch` blocks to handle potential errors from API calls, network issues, or validation failures.
*   **Environment Variables:** Keep API keys and sensitive configuration in environment variables and never commit them to version control.

This guide provides a starting point for using and extending the `LLMAbstractionService`. As the application's needs evolve, this service can be further enhanced to support more advanced LLM capabilities.