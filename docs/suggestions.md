Yes, there's definitely a better way! Large single-file XState machines can become hard to navigate and maintain. The common practice is to break down the machine into logical parts, often following a feature-based or type-based separation.

Here's a suggested structure and how you can organize your code:

**Proposed Directory Structure:**

```
src/
└── machines/
    └── adaptiveVocabularyGame/
        ├── adaptiveVocabularyGame.machine.ts   // The main machine definition
        ├── adaptiveVocabularyGame.types.ts     // Context, Events, other specific types
        ├── adaptiveVocabularyGame.actions.ts   // Action implementations
        ├── adaptiveVocabularyGame.actors.ts    // Actor (service) implementations
        ├── adaptiveVocabularyGame.guards.ts    // Guard implementations
        ├── adaptiveVocabularyGame.logic.ts     // Business logic/helper functions (prepareQuestion, evaluateAnswer, etc.)
        └── index.ts                            // Exports the machine and relevant types
```

Let's break down what goes into each file:

**1. `adaptiveVocabularyGame.types.ts`**

This file will house all your TypeScript definitions related to this specific machine.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.types.ts
import { VocabularyProgress, SuperMemoGrade } from '@/types/progress';
import { VocabularyItem } from '@/types/content';
import { DoneActorEvent, ErrorActorEvent } from 'xstate';

export type { SuperMemoGrade };

// --- GameType Definition ---
export type GameType = 'meaning' | 'hanzi' | 'english_to_hanzi' | 'mixed' | 'translation' | 'pinyin' | 'definition';

// --- Context Definition ---
export interface AdaptiveVocabularyGameContext {
  gameType?: GameType;
  initialVocabulary?: VocabularyItem[];
  prioritizedVocabulary: VocabularyItem[];
  currentQuestionIndex: number;
  currentItem?: VocabularyItem;
  options: string[];
  correctAnswerText?: string;
  selectedAnswer?: string;
  isCorrect?: boolean;
  feedbackMessage?: string;
  score: number;
  finalScore?: number;
  totalQuestions: number;
  gameStartTime?: number;
  questionStartTime?: number;
  totalTimeTaken?: number;
  errorMessage?: string;
  vocabularyProgressMap: Record<string, VocabularyProgress>;
  updateVocabularyItemProgress: (itemId: string, grade: SuperMemoGrade) => Promise<void>;
  getPrioritizedVocabulary: () => VocabularyItem[];
  lastReviewResult?: { efactor: number; interval: number; repetition: number; dueDate: string; };
}

// --- Event Definitions ---
export type AdaptiveVocabularyGameEvent =
  | { type: 'LOAD_VOCABULARY'; vocabulary: VocabularyItem[]; gameType: GameType }
  | { type: 'VOCABULARY_LOADED_SUCCESS'; prioritizedVocabulary: VocabularyItem[] }
  | { type: 'VOCABULARY_EMPTY' }
  | { type: 'VOCABULARY_LOAD_FAILURE'; error: Error }
  | { type: 'ANSWER_SELECTED'; selectedOption: string }
  | { type: 'CONTINUE_AFTER_FEEDBACK' }
  | { type: 'PLAY_AGAIN' }
  | { type: 'RETRY_LOADING' };

// --- Type for actor events ---
export type VocabLoaderDoneEvent = DoneActorEvent<VocabularyItem[]>; // Removed actor name for simplicity or make it generic
export type VocabLoaderErrorEvent = ErrorActorEvent; // Removed actor name for simplicity or make it generic
```

**2. `adaptiveVocabularyGame.logic.ts`**

This file contains pure helper functions and business logic that the machine actions/actors might use.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.logic.ts
import { VocabularyItem, AdaptiveVocabularyGameContext, GameType, SuperMemoGrade } from './adaptiveVocabularyGame.types';

// Helper function for shuffling (Fisher-Yates algorithm)
export function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

// Helper function for selecting a mix of items based on priority
export const selectMixedVocabulary = (prioritizedVocabulary: VocabularyItem[]): VocabularyItem[] => {
  const numItemsForGame = Math.min(prioritizedVocabulary.length, 10);
  return shuffleArray(prioritizedVocabulary).slice(0, numItemsForGame); // Shuffle before slicing
};

export const prepareQuestion = (
    context: Pick<AdaptiveVocabularyGameContext, 'prioritizedVocabulary' | 'currentQuestionIndex' | 'gameType'>
): Partial<Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'options' | 'correctAnswerText' | 'questionStartTime'>> => {
  if (!context.prioritizedVocabulary || context.prioritizedVocabulary.length === 0 || context.currentQuestionIndex >= context.prioritizedVocabulary.length) return {};
  const currentItem = context.prioritizedVocabulary[context.currentQuestionIndex];
  if (!currentItem) return {};

  let correctAnswer: string;
  let options: string[] = [];
  let optionSourceAttribute: keyof VocabularyItem | 'definitions_array' = 'word';

  switch (context.gameType) {
    case 'hanzi':
      correctAnswer = currentItem.word || '';
      optionSourceAttribute = 'word';
      break;
    case 'english_to_hanzi':
      correctAnswer = currentItem.word || '';
      optionSourceAttribute = 'word';
      break;
    case 'pinyin':
      correctAnswer = currentItem.pinyin;
      optionSourceAttribute = 'pinyin';
      break;
    case 'meaning':
    case 'definition':
    case 'translation':
    default:
      correctAnswer = (currentItem.definitions && currentItem.definitions.length > 0) ? currentItem.definitions[0] : '';
      optionSourceAttribute = 'definitions_array';
      break;
  }

  options.push(correctAnswer);

  const distractors = context.prioritizedVocabulary
    .filter(item => item.id !== currentItem.id)
    .map(item => {
      if (optionSourceAttribute === 'definitions_array') {
        return item.definitions && item.definitions.length > 0 ? item.definitions[0] : null;
      }
      return item[optionSourceAttribute as keyof VocabularyItem] as string | null;
    })
    .filter(opt => opt !== null && opt !== correctAnswer && !options.includes(opt as string)) as string[];

  const shuffledDistractors = shuffleArray(distractors);
  for (let i = 0; i < 3 && i < shuffledDistractors.length; i++) {
    if (options.length < 4) {
      options.push(shuffledDistractors[i]);
    }
  }

  const placeholderOptions = ["选项A", "选项B", "选项C", "选项D"];
  let placeholderIndex = 0;
  while (options.length < 4 && placeholderIndex < placeholderOptions.length) {
    if (!options.includes(placeholderOptions[placeholderIndex])) {
        options.push(placeholderOptions[placeholderIndex]);
    }
    placeholderIndex++;
  }
  options = shuffleArray(options.slice(0, 4));

  return { currentItem, options, correctAnswerText: correctAnswer, questionStartTime: Date.now() };
};

export const evaluateAnswer = (
  context: Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'correctAnswerText' | 'score'>,
  selectedOption: string
): Partial<Pick<AdaptiveVocabularyGameContext, 'selectedAnswer' | 'isCorrect' | 'feedbackMessage' | 'score'>> => {
  if (!context.currentItem || typeof context.correctAnswerText === 'undefined') return {};
  const isCorrect = selectedOption === context.correctAnswerText;
  const scoreIncrement = isCorrect ? 10 : 0;
  return {
    selectedAnswer: selectedOption,
    isCorrect,
    feedbackMessage: isCorrect ? 'Correct!' : `Incorrect. The correct answer was: "${context.correctAnswerText}"`,
    score: context.score + scoreIncrement,
  };
};

export const updateItemProgressLogic = async (
  context: Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'isCorrect' | 'updateVocabularyItemProgress' | 'vocabularyProgressMap'>
) => {
  if (context.currentItem && typeof context.isCorrect === 'boolean' && context.updateVocabularyItemProgress) {
    const grade: SuperMemoGrade = context.isCorrect ? 5 : 0;
    await context.updateVocabularyItemProgress(context.currentItem.id, grade);
    const updatedProgress = context.vocabularyProgressMap[context.currentItem.id]; // This map might not be updated yet
    if (updatedProgress) { // To get the *actual* updated progress, it might need to be refetched or returned by updateVocabularyItemProgress
      return {
        efactor: updatedProgress.efactor,
        interval: updatedProgress.interval,
        repetition: updatedProgress.repetition,
        dueDate: updatedProgress.dueDate,
      };
    }
  }
  return undefined;
};

export const resetGameContextFields = (): Partial<AdaptiveVocabularyGameContext> => ({
  prioritizedVocabulary: [],
  currentQuestionIndex: 0,
  currentItem: undefined,
  options: [],
  correctAnswerText: undefined,
  selectedAnswer: undefined,
  isCorrect: undefined,
  feedbackMessage: undefined,
  score: 0,
  finalScore: undefined,
  totalQuestions: 0,
  gameStartTime: undefined,
  questionStartTime: undefined,
  totalTimeTaken: undefined,
  errorMessage: undefined,
  lastReviewResult: undefined,
});

export const getInitialContext = (): AdaptiveVocabularyGameContext => ({
  prioritizedVocabulary: [],
  currentQuestionIndex: 0,
  options: [],
  score: 0,
  finalScore: undefined,
  totalQuestions: 0,
  vocabularyProgressMap: {},
  updateVocabularyItemProgress: async () => { console.warn('updateVocabularyItemProgress not implemented'); },
  getPrioritizedVocabulary: () => { console.warn('getPrioritizedVocabulary not implemented'); return []; },
  lastReviewResult: undefined,
});
```
*Note: I've refined the input types for `prepareQuestion` and `evaluateAnswer` to only pick necessary fields from the context, improving testability.*

**3. `adaptiveVocabularyGame.actions.ts`**

Here, you define the implementations for your machine's actions.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.actions.ts
import { assign, fromPromise } from 'xstate';
import {
  AdaptiveVocabularyGameContext,
  AdaptiveVocabularyGameEvent,
  VocabLoaderDoneEvent,
  VocabLoaderErrorEvent,
  VocabularyItem
} from './adaptiveVocabularyGame.types';
import {
  prepareQuestion,
  evaluateAnswer,
  resetGameContextFields,
  updateItemProgressLogic
} from './adaptiveVocabularyGame.logic';

export const assignInitialLoadData = assign<AdaptiveVocabularyGameContext, Extract<AdaptiveVocabularyGameEvent, { type: 'LOAD_VOCABULARY' }>>(
  ({ event }) => ({ // context is implicitly spread by assign if not returned fully
    initialVocabulary: event.vocabulary,
    gameType: event.gameType,
    ...resetGameContextFields(), // Apply reset fields
    score: 0, // Ensure score is reset
    totalQuestions: 0, // Ensure totalQuestions is reset
  })
);

export const assignLoadedVocabularyAndReset = assign<AdaptiveVocabularyGameContext, VocabLoaderDoneEvent>(
  ({ event }) => {
    const loadedVocab = event.output;
    return {
      prioritizedVocabulary: loadedVocab,
      totalQuestions: loadedVocab.length,
      currentQuestionIndex: 0,
      score: 0,
      finalScore: undefined,
      gameStartTime: Date.now(),
      errorMessage: undefined,
      currentItem: undefined,
      options: [],
      correctAnswerText: undefined,
      selectedAnswer: undefined,
      isCorrect: undefined,
      feedbackMessage: undefined,
      questionStartTime: undefined,
    };
  }
);

export const prepareQuestionAction = assign<AdaptiveVocabularyGameContext>(
    (context) => prepareQuestion(context)
);

export const evaluateAnswerAction = assign<AdaptiveVocabularyGameContext, Extract<AdaptiveVocabularyGameEvent, { type: 'ANSWER_SELECTED' }>>(
  ({ context, event }) => evaluateAnswer(context, event.selectedOption)
);

export const updateItemProgressAction = assign<AdaptiveVocabularyGameContext>({
  lastReviewResult: fromPromise(async ({ context }) => {
    return await updateItemProgressLogic(context);
  }),
});

export const incrementQuestionIndexAction = assign<AdaptiveVocabularyGameContext>({
  currentQuestionIndex: ({ context }) => context.currentQuestionIndex + 1,
});

export const calculateTotalTimeAndFinalScoreAction = assign<AdaptiveVocabularyGameContext>({
  totalTimeTaken: ({ context }) => (context.gameStartTime ? Date.now() - context.gameStartTime : undefined),
  finalScore: ({ context }) => context.score,
});

export const resetGameContextAction = assign<AdaptiveVocabularyGameContext>(
    () => resetGameContextFields() // context here refers to the old context, so we just return new fields
);

export const assignErrorMessageFromError = assign<AdaptiveVocabularyGameContext, VocabLoaderErrorEvent>(
  ({ event }) => {
    const errorData = event.error;
    return {
      errorMessage: errorData instanceof Error ? errorData.message : 'An unknown error occurred during vocabulary loading.',
    };
  }
);

export const clearErrorMessageAction = assign<AdaptiveVocabularyGameContext>({
    errorMessage: undefined
});
```

**4. `adaptiveVocabularyGame.actors.ts`**

Define your invoked actors (services) here.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.actors.ts
import { fromPromise } from 'xstate';
import { VocabularyItem } from '@/types/content'; // Or from ./adaptiveVocabularyGame.types
import { selectMixedVocabulary } from './adaptiveVocabularyGame.logic';
import { AdaptiveVocabularyGameContext } from './adaptiveVocabularyGame.types';

type InvokeGetPrioritizedVocabularyInput = {
    // initialVocabulary?: VocabularyItem[]; // Not used by selectMixedVocabulary directly if getPrioritizedVocabulary is used
    // gameType?: GameType; // Not used by selectMixedVocabulary directly
    getPrioritizedVocabulary: () => VocabularyItem[];
};

export const invokeGetPrioritizedVocabulary = fromPromise(
  async ({ input }: { input: InvokeGetPrioritizedVocabularyInput }): Promise<VocabularyItem[]> => {
    const prioritized = input.getPrioritizedVocabulary(); // Use the function from context
    const selectedItems = selectMixedVocabulary(prioritized);
    console.log(`[XState Actor] Selected ${selectedItems.length} items for the game session.`);
    if (!selectedItems) { // Should not happen if selectMixedVocabulary always returns an array
        return [];
    }
    return selectedItems;
  }
);
```

**5. `adaptiveVocabularyGame.guards.ts`**

Define your guard implementations.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.guards.ts
import { AdaptiveVocabularyGameContext, VocabLoaderDoneEvent } from './adaptiveVocabularyGame.types';

export const isVocabularyEmptyOnLoad = (
    _context: AdaptiveVocabularyGameContext, // context might not be needed if checking event output
    event: VocabLoaderDoneEvent // Assuming event is of VocabLoaderDoneEvent type
) => {
  const output = event.output;
  return !output || output.length === 0;
};

export const hasMoreQuestions = (context: AdaptiveVocabularyGameContext) =>
  context.currentQuestionIndex < context.totalQuestions - 1;

```

**6. `adaptiveVocabularyGame.machine.ts`**

This is the core machine definition, now much cleaner as it references implementations from other files.

```typescript
// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.machine.ts
import { createMachine, assign } from 'xstate';
import {
  AdaptiveVocabularyGameContext,
  AdaptiveVocabularyGameEvent,
  VocabLoaderDoneEvent,
  VocabLoaderErrorEvent,
  VocabularyItem
} from './adaptiveVocabularyGame.types';
import * as actions from './adaptiveVocabularyGame.actions';
import * as actors from './adaptiveVocabularyGame.actors';
import * as guards from './adaptiveVocabularyGame.guards';
import { getInitialContext } from './adaptiveVocabularyGame.logic';

export const adaptiveVocabularyGameMachine = createMachine(
  {
    id: 'adaptiveVocabularyGame',
    types: {
      context: {} as AdaptiveVocabularyGameContext,
      events: {} as AdaptiveVocabularyGameEvent, // Union with actor events handled by XState internally based on invoke
      // For more specific event typing in actions/guards, use type assertions or narrow within the implementation
    },
    context: getInitialContext(),
    initial: 'initializing',
    states: {
      initializing: {
        on: {
          LOAD_VOCABULARY: {
            target: 'loadingVocabulary',
            actions: 'assignInitialLoadData',
          },
        },
      },
      loadingVocabulary: {
        invoke: {
          id: 'vocabLoader',
          src: 'invokeGetPrioritizedVocabulary',
          input: ({ context }: { context: AdaptiveVocabularyGameContext }) => ({
            // initialVocabulary: context.initialVocabulary, // Pass if needed by actor
            // gameType: context.gameType, // Pass if needed by actor
            getPrioritizedVocabulary: context.getPrioritizedVocabulary, // Pass the function
          }),
          onDone: [
            {
              guard: 'isVocabularyEmptyOnLoad', // Uses the guard defined with this string name
              target: 'gameEmpty',
              actions: assign({ prioritizedVocabulary: [] }) // Simple inline assign if specific
            },
            {
              target: 'presentingQuestion',
              actions: ['assignLoadedVocabularyAndReset', 'prepareQuestionAction']
            },
          ],
          onError: {
            target: 'error',
            actions: 'assignErrorMessageFromError'
          },
        },
      },
      presentingQuestion: {
        on: {
          ANSWER_SELECTED: {
            target: 'showingFeedback',
            actions: ['evaluateAnswerAction', 'updateItemProgressAction']
          },
        },
      },
      showingFeedback: {
        on: {
          CONTINUE_AFTER_FEEDBACK: [
            {
              target: 'presentingQuestion',
              guard: 'hasMoreQuestions',
              actions: ['incrementQuestionIndexAction', 'prepareQuestionAction']
            },
            {
              target: 'gameComplete',
              actions: 'calculateTotalTimeAndFinalScoreAction'
            },
          ],
        },
      },
      gameComplete: {
        on: {
          PLAY_AGAIN: {
            target: 'initializing', // Or 'loadingVocabulary' if you want to re-use existing initialVocabulary
            actions: 'resetGameContextAction'
          }
        },
      },
      gameEmpty: { type: 'final' },
      error: {
        on: {
          RETRY_LOADING: {
            target: 'loadingVocabulary', // Retry loading
            actions: 'clearErrorMessageAction' // Clear previous error
          }
        },
      },
    },
  },
  {
    actions,
    actors,
    guards,
  }
);
```

**7. `index.ts`**

An optional barrel file to make importing easier.

```typescript
// src/features/adaptiveVocabularyGame/index.ts
export { adaptiveVocabularyGameMachine } from './adaptiveVocabularyGame.machine';
export * from './adaptiveVocabularyGame.types'; // Export types if they need to be consumed elsewhere
```
Then you can import like:
`import { adaptiveVocabularyGameMachine, AdaptiveVocabularyGameContext } from '@/features/adaptiveVocabularyGame';`

**Key Benefits of This Structure:**

1.  **Readability:** The main machine definition (`.machine.ts`) becomes much shorter and focuses on the state transitions and flow, rather than implementation details.
2.  **Maintainability:** Easier to find and modify specific pieces of logic (an action, a guard, a type) without wading through a massive file.
3.  **Testability:** Helper functions in `.logic.ts`, and even individual actions/guards, can be unit-tested more easily in isolation.
4.  **Scalability:** As your machine grows, this structure accommodates new states, actions, and logic more gracefully.
5.  **Collaboration:** Different team members can work on different parts (e.g., one on types, another on actions) with fewer merge conflicts.

**Important Considerations:**

*   **Typing `event` in implementations:**
    *   For actions: `assign<TContext, TEvent>((context, event) => { ... })` where `TEvent` is the specific event type (e.g., `Extract<AdaptiveVocabularyGameEvent, { type: 'ANSWER_SELECTED' }>`).
    *   For guards: `(context: TContext, event: TEvent, meta: GuardMeta<...>) => { ... }`.
*   **`input` for Actors:** Ensure the `input` field in your `invoke` configuration correctly passes what the actor function expects. The actor receives an object `{ input: yourInputData }`.
*   **Naming:** Use consistent and descriptive names for your files and exported members.
*   **Circular Dependencies:** Be mindful of how you import. Generally, `.logic.ts` shouldn't import from `.actions.ts` or `.machine.ts`. Types can be imported widely.

This refactoring makes your XState setup much more robust and developer-friendly.