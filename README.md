# Chinese Language Learning Platform

## Overview
An interactive platform for learning Chinese, featuring vocabulary and grammar exercises, AI-assisted learning tools, and progress tracking. Currently in active development - some features may be unstable.

## Key Features
- **Vocabulary Practice**: Character recognition, pinyin matching, writing practice
- **Grammar Exercises**: Contextual grammar practice with instant feedback
- **AI-Powered Learning**: Adaptive exercises, chat-based practice, translation games
- **Progress Tracking**: Monitor your vocabulary and grammar mastery over time
- **Gamified Learning**: Story adventures, matching games, and interactive challenges

## Current Status
⚠️ **Development Notice**: This application is currently in active development. Some features may be unstable, incomplete, or subject to significant changes. It can't be used in a production environments at this time.

## Getting Started
1. Install dependencies:
```bash
pnpm install
```

2. Set up environment variables (see `.env.example`)

3. Run the development server:
```bash
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Contributing
Contributions are welcome! Please see our [Contribution Guidelines](CONTRIBUTING.md) for details.

## Roadmap
- [ ] Stabilize vocabulary game implementations
- [ ] Implement comprehensive testing
- [ ] Add user authentication persistence
- [ ] Develop mobile-responsive UI
