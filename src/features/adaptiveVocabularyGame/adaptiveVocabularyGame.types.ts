// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.types.ts
import { VocabularyProgress, SuperMemoGrade } from '../../types/progress';
import { VocabularyItem } from '../../types/content';
import { DoneActorEvent, ErrorActorEvent } from 'xstate';
import { IGameLogic } from './gameLogic.interface'; // Import the new interface

export type { SuperMemoGrade, VocabularyItem };

// --- GameType Definition ---
// This type will eventually be removed or simplified as gameLogic takes over
export type GameType = 'meaning' | 'hanzi' | 'english_to_hanzi' | 'mixed' | 'translation' | 'pinyin' | 'definition' | 'matching' | 'translation-chat';

// --- Context Definition ---
export interface AdaptiveVocabularyGameContext {
  gameLogic: IGameLogic | null; // Use IGameLogic instead of GameType
  initialVocabulary?: VocabularyItem[];
  prioritizedVocabulary: VocabularyItem[];
  currentQuestionIndex: number;
  currentItem?: VocabularyItem;
  options: string[];
  correctAnswerText?: string;
  selectedAnswer?: string;
  isCorrect?: boolean;
  feedbackMessage?: string;
  score: number;
  finalScore?: number;
  totalQuestions: number;
  gameStartTime?: number;
  questionStartTime?: number;
  totalTimeTaken?: number;
  errorMessage?: string;
  vocabularyProgressMap: Record<string, VocabularyProgress>;
  updateVocabularyItemProgress: (itemId: string, grade: SuperMemoGrade) => Promise<void>;
  getPrioritizedVocabulary: (initialVocabulary: VocabularyItem[], context: AdaptiveVocabularyGameContext) => VocabularyItem[];
  lastReviewResult?: { efactor: number; interval: number; repetition: number; dueDate: string; };
}

// --- Event Definitions ---
export type AdaptiveVocabularyGameEvent =
  | { type: 'LOAD_VOCABULARY'; vocabulary: VocabularyItem[]; gameLogic: IGameLogic } // Update event to use gameLogic
  | { type: 'VOCABULARY_LOADED_SUCCESS'; prioritizedVocabulary: VocabularyItem[] }
  | { type: 'VOCABULARY_EMPTY' }
  | { type: 'VOCABULARY_LOAD_FAILURE'; error: Error }
  | { type: 'ANSWER_SELECTED'; selectedOption: string }
  | { type: 'CONTINUE_AFTER_FEEDBACK' }
  | { type: 'PLAY_AGAIN' }
  | { type: 'RETRY_LOADING' };

// --- Type for actor events ---
export type VocabLoaderDoneEvent = DoneActorEvent<VocabularyItem[]>; // Removed actor name for simplicity or make it generic
export type VocabLoaderErrorEvent = ErrorActorEvent; // Removed actor name for simplicity or make it generic
