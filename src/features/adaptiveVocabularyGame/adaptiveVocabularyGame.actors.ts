// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.actors.ts
import { fromPromise } from 'xstate';
import { VocabularyItem } from '../../types/content'; // Corrected import path
import { AdaptiveVocabularyGameContext } from './adaptiveVocabularyGame.types';

type InvokeGetPrioritizedVocabularyInput = {
    initialVocabulary: VocabularyItem[];
    context: AdaptiveVocabularyGameContext;
    getPrioritizedVocabulary: (initialVocabulary: VocabularyItem[], context: AdaptiveVocabularyGameContext) => VocabularyItem[];
};

export const invokeGetPrioritizedVocabulary = fromPromise(
  async ({ input }: { input: InvokeGetPrioritizedVocabularyInput }): Promise<VocabularyItem[]> => {
    console.log('[XState Actor] invokeGetPrioritizedVocabulary input.initialVocabulary:', JSON.parse(JSON.stringify(input.initialVocabulary)));
    // The getPrioritizedVocabulary function from context should already return the selected items
    const selectedItems = input.getPrioritizedVocabulary(input.initialVocabulary, input.context);
    console.log('[XState Actor] invokeGetPrioritizedVocabulary prioritized items:', JSON.parse(JSON.stringify(selectedItems)));
    console.log(`[XState Actor] Selected ${selectedItems.length} items for the game session. Selected items:`, JSON.parse(JSON.stringify(selectedItems)));
    if (!selectedItems) {
        return [];
    }
    return selectedItems;
  }
);