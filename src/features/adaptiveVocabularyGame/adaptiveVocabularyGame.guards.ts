// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.guards.ts
import { AdaptiveVocabularyGameContext, VocabLoaderDoneEvent } from './adaptiveVocabularyGame.types';

export const isVocabularyEmptyOnLoad = (
    _context: AdaptiveVocabularyGameContext, // context might not be needed if checking event output
    event: VocabLoaderDoneEvent // Assuming event is of VocabLoaderDoneEvent type
) => {
  // Defensive check for event and event.output
  if (!event || !event.output) {
    return true; // Treat as empty if output is missing
  }
  const output = event.output;
  return output.length === 0;
};

export const hasMoreQuestions = ({ context: actualContext }: { context: AdaptiveVocabularyGameContext }) => {
  // The argument to a guard is an object like { context, event }
  // So we destructure to get the actual context object.
  console.log('[Guard] hasMoreQuestions - Full actualContext:', JSON.parse(JSON.stringify(actualContext)));
  console.log(
    '[Guard] hasMoreQuestions: currentQuestionIndex =',
    actualContext.currentQuestionIndex,
    ', totalQuestions =',
    actualContext.totalQuestions,
    ', condition =',
    actualContext.currentQuestionIndex < actualContext.totalQuestions - 1
  );
  return actualContext.currentQuestionIndex < actualContext.totalQuestions - 1;
}