// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.logic.ts
import { AdaptiveVocabularyGameContext, SuperMemoGrade } from './adaptiveVocabularyGame.types';
import { VocabularyItem } from '@/types/content'; // Import VocabularyItem from content types
import { shuffleArray } from '@/lib/utils';

export const prepareQuestion = (
    context: AdaptiveVocabularyGameContext // Use full context
): Partial<Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'options' | 'correctAnswerText' | 'questionStartTime'>> => {
  if (!context.prioritizedVocabulary || context.prioritizedVocabulary.length === 0 || context.currentQuestionIndex >= context.prioritizedVocabulary.length) return {};
  const currentItem = context.prioritizedVocabulary[context.currentQuestionIndex];
  if (!currentItem || !context.gameLogic) return {};

  const options = context.gameLogic.generateOptions(currentItem, context.prioritizedVocabulary, context);
  const correctAnswerText = context.gameLogic.getCorrectAnswerText(currentItem, context);
 
  return { currentItem, options, correctAnswerText, questionStartTime: Date.now() };
};

export const evaluateAnswer = (
  context: Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'correctAnswerText' | 'score'>,
  selectedOption: string
): Partial<Pick<AdaptiveVocabularyGameContext, 'selectedAnswer' | 'isCorrect' | 'feedbackMessage' | 'score'>> => {
  if (!context.currentItem || typeof context.correctAnswerText === 'undefined') return {};
  const isCorrect = selectedOption === context.correctAnswerText;
  const scoreIncrement = isCorrect ? 10 : 0;
  return {
    selectedAnswer: selectedOption,
    isCorrect,
    feedbackMessage: isCorrect ? 'Correct!' : `Incorrect. The correct answer was: "${context.correctAnswerText}"`,
    score: context.score + scoreIncrement,
  };
};

export const updateItemProgressLogic = async (
  context: Pick<AdaptiveVocabularyGameContext, 'currentItem' | 'isCorrect' | 'updateVocabularyItemProgress' | 'vocabularyProgressMap'>
) => {
  console.log('[updateItemProgressLogic] STARTING. context.currentItem:', JSON.parse(JSON.stringify(context.currentItem)));
  console.log('[updateItemProgressLogic] context.isCorrect:', context.isCorrect);
  console.log('[updateItemProgressLogic] context.updateVocabularyItemProgress exists:', !!context.updateVocabularyItemProgress);
  console.log('[updateItemProgressLogic] context.vocabularyProgressMap:', JSON.parse(JSON.stringify(context.vocabularyProgressMap)));

  if (context.currentItem && typeof context.isCorrect === 'boolean' && context.updateVocabularyItemProgress) {
    const grade: SuperMemoGrade = context.isCorrect ? 5 : 0;
    console.log(`[updateItemProgressLogic] Calling updateVocabularyItemProgress for item ID: ${context.currentItem.id} with grade: ${grade}`);
    await context.updateVocabularyItemProgress(context.currentItem.id, grade);
    
    // The vocabularyProgressMap might not be updated immediately after the async call.
    // For debugging, we'll check what's currently in the map.
    const updatedProgress = context.vocabularyProgressMap[context.currentItem.id];
    console.log('[updateItemProgressLogic] updatedProgress from map (after await):', JSON.parse(JSON.stringify(updatedProgress)));

    if (updatedProgress) {
      console.log('[updateItemProgressLogic] Returning updated progress:', JSON.parse(JSON.stringify({
        efactor: updatedProgress.efactor,
        interval: updatedProgress.interval,
        repetition: updatedProgress.repetition,
        dueDate: updatedProgress.dueDate,
      })));
      return {
        efactor: updatedProgress.efactor,
        interval: updatedProgress.interval,
        repetition: updatedProgress.repetition,
        dueDate: updatedProgress.dueDate,
      };
    } else {
      console.warn('[updateItemProgressLogic] updatedProgress is undefined after updateVocabularyItemProgress call. Map might not be reactive or update failed.');
    }
  } else {
    console.warn('[updateItemProgressLogic] Pre-conditions not met: currentItem, isCorrect, or updateVocabularyItemProgress is missing.');
  }
  console.log('[updateItemProgressLogic] Returning undefined.');
  return undefined;
};

export const resetGameContextFields = (): Partial<AdaptiveVocabularyGameContext> => ({
  prioritizedVocabulary: [],
  currentQuestionIndex: 0,
  currentItem: undefined,
  options: [],
  correctAnswerText: undefined,
  selectedAnswer: undefined,
  isCorrect: undefined,
  feedbackMessage: undefined,
  score: 0,
  finalScore: undefined,
  totalQuestions: 0,
  gameStartTime: undefined,
  questionStartTime: undefined,
  totalTimeTaken: undefined,
  errorMessage: undefined,
  lastReviewResult: undefined,
});

export const getInitialContext = (): AdaptiveVocabularyGameContext => ({
  gameLogic: null, // Initialize gameLogic to null
  prioritizedVocabulary: [],
  currentQuestionIndex: 0,
  options: [],
  score: 0,
  finalScore: undefined,
  totalQuestions: 0,
  vocabularyProgressMap: {},
  updateVocabularyItemProgress: async () => { console.warn('updateVocabularyItemProgress not implemented'); },
  getPrioritizedVocabulary: (initialVocabulary: VocabularyItem[]) => {
    // Shuffle the initial vocabulary and then select the first 10 items.
    const shuffledVocabulary = shuffleArray(initialVocabulary);
    return shuffledVocabulary;
  },
  lastReviewResult: undefined,
});
