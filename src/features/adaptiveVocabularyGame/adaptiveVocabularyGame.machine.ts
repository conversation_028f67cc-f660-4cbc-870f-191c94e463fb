import { createMachine } from 'xstate';
import {
  AdaptiveVocabularyGameContext,
  AdaptiveVocabularyGameEvent,
} from './adaptiveVocabularyGame.types';
import * as actions from './adaptiveVocabularyGame.actions';
import * as actors from './adaptiveVocabularyGame.actors';
import * as guards from './adaptiveVocabularyGame.guards';
import { getInitialContext } from './adaptiveVocabularyGame.logic';

export const adaptiveVocabularyGameMachine = createMachine(
  {
    id: 'adaptiveVocabularyGame',
    types: {
      context: {} as AdaptiveVocabularyGameContext,
      events: {} as AdaptiveVocabularyGameEvent, // Union with actor events handled by XState internally based on invoke
      // For more specific event typing in actions/guards, use type assertions or narrow within the implementation
    },
    context: getInitialContext(),
    initial: 'initializing',
    states: {
      initializing: {
        on: {
          LOAD_VOCABULARY: {
            target: 'loadingVocabulary',
            actions: 'assignInitialLoadData',
          },
        },
      },
      loadingVocabulary: {
        invoke: {
          id: 'vocabLoader',
          src: 'invokeGetPrioritizedVocabulary',
          input: ({ context }: { context: AdaptiveVocabularyGameContext }) => ({
            initialVocabulary: context.initialVocabulary,
            context: context,
            getPrioritizedVocabulary: context.getPrioritizedVocabulary,
          }),
          onDone: [
            {
              target: 'presentingQuestion',
              actions: ['assignLoadedVocabularyAndReset', 'prepareQuestionAction']
            },
          ],
          onError: {
            target: 'error',
            actions: 'assignErrorMessageFromError'
          },
        },
      },
      presentingQuestion: {
        always: 'questionDisplayed',
      },
      questionDisplayed: {
        // Removed temporary _logQuestionDisplayedContext entry action
        on: {
          ANSWER_SELECTED: {
            target: 'showingFeedback',
            actions: ['evaluateAnswerAction', 'updateItemProgressAction']
          },
        },
      },
      showingFeedback: {
        entry: ['logShowingFeedbackEntryContext'], // Reference action by name
        on: {
          CONTINUE_AFTER_FEEDBACK: [
            {
              target: 'presentingQuestion',
              guard: 'hasMoreQuestions',
              actions: ['incrementQuestionIndexAction', 'prepareQuestionAction']
            },
            {
              target: 'gameComplete',
              actions: 'calculateTotalTimeAndFinalScoreAction'
            },
          ],
        },
      },
      gameComplete: {
        on: {
          PLAY_AGAIN: {
            target: 'initializing', // Or 'loadingVocabulary' if you want to re-use existing initialVocabulary
            actions: 'resetGameContextAction'
          }
        },
      },
      gameEmpty: { type: 'final' },
      error: {
        on: {
          RETRY_LOADING: {
            target: 'loadingVocabulary', // Retry loading
            actions: 'clearErrorMessageAction' // Clear previous error
          }
        },
      },
    },
  },
  {
    actions: actions,
    actors: actors,
    guards: guards,
  }
);