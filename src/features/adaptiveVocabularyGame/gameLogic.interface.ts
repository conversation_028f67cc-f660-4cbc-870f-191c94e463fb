import { VocabularyItem } from '@/types/content';
import { AdaptiveVocabularyGameContext } from './adaptiveVocabularyGame.types';

export interface IGameLogic {
  gameType: string; // A unique identifier for the game type
  getQuestionPrompt(item: VocabularyItem, context: AdaptiveVocabularyGameContext): React.ReactNode;
  generateOptions(item: VocabularyItem, allVocabulary: VocabularyItem[], context: AdaptiveVocabularyGameContext): string[];
  evaluateAnswer(item: VocabularyItem, selectedAnswer: string, context: AdaptiveVocabularyGameContext): boolean;
  getCorrectAnswerText(item: VocabularyItem, context: AdaptiveVocabularyGameContext): string;
  // Add any other methods that are specific to a game type's logic or UI rendering
}