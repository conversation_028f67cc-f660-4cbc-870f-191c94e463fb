import { IGameLogic } from '../gameLogic.interface';
import { VocabularyItem } from '@/types/content';
import { shuffleArray } from '@/lib/utils';

export const meaningGameLogic: IGameLogic = {
  gameType: 'meaning',

  getQuestionPrompt(item: VocabularyItem): React.ReactNode {
    return item.word; // Show <PERSON>zi
  },

  generateOptions(item: VocabularyItem, allVocabulary: VocabularyItem[]): string[] {
    if (!item.definitions || item.definitions.length === 0) {
      return []; // Or handle this error case appropriately
    }
    const correctMeaning = item.definitions[0];
    
    // Filter out the current item and then flatMap definitions, ensuring they are strings
    const incorrectMeanings = allVocabulary
      .filter(v => v.id !== item.id)
      .flatMap(v => v.definitions || [])
      .filter((def): def is string => def !== correctMeaning && def !== undefined); // Type guard to ensure string[]

    const uniqueIncorrectMeanings = Array.from(new Set(incorrectMeanings));

    const selectedIncorrectMeanings = shuffleArray(uniqueIncorrectMeanings).slice(0, 3);
    const options = shuffleArray([...selectedIncorrectMeanings, correctMeaning]).filter((opt): opt is string => opt !== undefined); // Ensure options are strings
    return options;
  },

  evaluateAnswer(item: VocabularyItem, selectedAnswer: string): boolean {
    return item.definitions?.includes(selectedAnswer) || false;
  },

  getCorrectAnswerText(item: VocabularyItem): string {
    return item.definitions?.[0] || '';
  },
};