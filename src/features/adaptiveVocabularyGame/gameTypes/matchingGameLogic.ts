import { VocabularyItem } from '@/types/content';
import { courseData } from '@/lib/course-data';
import { shuffleArray } from '@/lib/utils';
import { VocabularyProgress } from '@/types/progress'; // Import VocabularyProgress
import { initializeProgress, getPriorityScore } from '@/lib/spaced-repetition'; // Import spaced repetition logic

export type MatchingGameType = 'word-pinyin' | 'word-definitions' | 'pinyin-definitions';

export interface MatchingGameRoundState {
  questions: VocabularyItem[];
  options: { id: string; value: string; type: 'word' | 'pinyin' | 'definitions' }[];
  matchedPairs: string[];
  selected: { id: string; value: string; type: 'word' | 'pinyin' | 'definitions' } | null;
  timer: number;
  initialTimer: number; // Add initialTimer to state
  roundCompleted: boolean;
  correctMatches: number;
  incorrectAttempts: number;
  gameType: MatchingGameType;
}

// Configuration for adaptive timer
const BASE_TIME_PER_PAIR_SECONDS = 8; // Base time for each pair
const PRIORITY_ADJUSTMENT_FACTOR = 0.5; // How much priority score affects time (higher factor = more time for harder items)
const MIN_TIMER_SECONDS = 20; // Minimum time for a round

export async function initializeMatchingGameRound(
  currentLevel: number,
  gameType: MatchingGameType,
  previousVocabulary: VocabularyItem[] = [],
  vocabularyProgressMap: Record<string, VocabularyProgress> // New parameter
): Promise<MatchingGameRoundState> {
  const numNewCharacters = 10 + (currentLevel - 1) * 5;
  const numReinforceCharacters = Math.floor(previousVocabulary.length * 0.1);

  const allVocabulary = await courseData.getAllVocabularyItems();
  if (!allVocabulary || allVocabulary.length === 0) {
    throw new Error('No vocabulary items found to start the game.');
  }

  const newCharacters = allVocabulary
    .filter(item => !previousVocabulary.some(prev => prev.id === item.id))
    .sort(() => 0.5 - Math.random())
    .slice(0, numNewCharacters);

  const reinforceCharacters = shuffleArray(previousVocabulary).slice(0, numReinforceCharacters);

  const selectedVocabulary = shuffleArray([...newCharacters, ...reinforceCharacters]);

  const questions: VocabularyItem[] = selectedVocabulary.map(item => ({
    ...item,
    id: item.id!,
  }));

  const options: { id: string; value: string; type: 'word' | 'pinyin' | 'definitions' }[] = [];
  questions.forEach(q => {
    const word = q.word || "";
    const pinyin = q.pinyin || "";
    const definitions = q.definitions?.join(", ") ?? "";

    if (gameType === 'word-pinyin') {
      if (word) options.push({ id: q.id, value: word, type: 'word' });
      if (pinyin) options.push({ id: q.id, value: pinyin, type: 'pinyin' });
    } else if (gameType === 'word-definitions') {
      if (word) options.push({ id: q.id, value: word, type: 'word' });
      if (definitions) options.push({ id: q.id, value: definitions, type: 'definitions' });
    } else if (gameType === 'pinyin-definitions') {
      if (pinyin) options.push({ id: q.id, value: pinyin, type: 'pinyin' });
      if (definitions) options.push({ id: q.id, value: definitions, type: 'definitions' });
    }
  });

  const filteredQuestions = questions.filter(q => {
    if (gameType === 'word-pinyin') return (q.word && q.pinyin);
    if (gameType === 'word-definitions') return (q.word && q.definitions && q.definitions.length > 0);
    if (gameType === 'pinyin-definitions') return (q.pinyin && q.definitions && q.definitions.length > 0);
    return false;
  });

  // Calculate adaptive timer
  const numPairs = filteredQuestions.length;
  const baseTime = BASE_TIME_PER_PAIR_SECONDS * numPairs;
  let adaptiveAdjustment = 0;
  let finalCalculatedTimer = baseTime;

  if (numPairs > 0) {
    const totalMasteryScore = filteredQuestions.reduce((sum, q) => {
      const progress = vocabularyProgressMap[q.id!] || initializeProgress(q.id!);
      return sum + getPriorityScore(progress);
    }, 0);
    const averagePriorityScore = totalMasteryScore / numPairs; // Renamed for clarity

    // Adjustment: higher priority score (lower mastery) should add more time
    adaptiveAdjustment = averagePriorityScore * PRIORITY_ADJUSTMENT_FACTOR;
    finalCalculatedTimer = baseTime + adaptiveAdjustment;
  }

  // Ensure timer doesn't go below a minimum
  const finalTimer = Math.max(MIN_TIMER_SECONDS, Math.round(finalCalculatedTimer));

  return {
    questions: filteredQuestions,
    options: shuffleArray(options),
    matchedPairs: [],
    selected: null,
    timer: finalTimer,
    initialTimer: finalTimer,
    roundCompleted: false,
    correctMatches: 0,
    incorrectAttempts: 0,
    gameType: gameType,
  };
}

export function handleMatchingAttempt(
  state: MatchingGameRoundState,
  clickedOption: { id: string; value: string; type: 'word' | 'pinyin' | 'definitions' }
): MatchingGameRoundState {
  const newState = { ...state };

  if (newState.selected === null) {
    newState.selected = clickedOption;
  } else {
    // Check for match based on selected game type
    const isSameId = newState.selected.id === clickedOption.id;
    const isDifferentType = newState.selected.type !== clickedOption.type;

    let isValidMatch = false;
    if (isSameId && isDifferentType) {
      // Further validate based on gameType
      if (state.gameType === 'word-pinyin') {
        isValidMatch = (newState.selected.type === 'word' && clickedOption.type === 'pinyin') ||
                       (newState.selected.type === 'pinyin' && clickedOption.type === 'word');
      } else if (state.gameType === 'word-definitions') {
        isValidMatch = (newState.selected.type === 'word' && clickedOption.type === 'definitions') ||
                       (newState.selected.type === 'definitions' && clickedOption.type === 'word');
      } else if (state.gameType === 'pinyin-definitions') {
        isValidMatch = (newState.selected.type === 'pinyin' && clickedOption.type === 'definitions') ||
                       (newState.selected.type === 'definitions' && clickedOption.type === 'pinyin');
      }
    }

    if (isValidMatch) {
      newState.matchedPairs.push(newState.selected.id);
      newState.correctMatches++;
      newState.selected = null;
    } else {
      newState.incorrectAttempts++;
      newState.selected = null;
    }
  }

  // Round is completed if all questions are matched OR timer runs out
  newState.roundCompleted = newState.matchedPairs.length === newState.questions.length;

  return newState;
}

export function updateTimer(state: MatchingGameRoundState): MatchingGameRoundState {
  const newTimer = state.timer - 1;
  return {
    ...state,
    timer: newTimer,
    roundCompleted: state.roundCompleted || newTimer <= 0, // Round ends if timer runs out
  };
}

export function checkRoundCompletion(state: MatchingGameRoundState): boolean {
  return state.matchedPairs.length === state.questions.length || state.timer <= 0;
}
