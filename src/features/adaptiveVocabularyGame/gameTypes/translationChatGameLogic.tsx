import React from 'react';
import { IGameLogic } from '../gameLogic.interface';
import { VocabularyItem, GrammarPoint } from '@/types/content';
import { AdaptiveVocabularyGameContext } from '../adaptiveVocabularyGame.types';
import {
  TranslationChatQuestionSchema,
  TranslationChatEvaluationSchema,
  TranslationChatGameSummarySchema,
  ChatTurnSchema,
} from '@/lib/ai-service';
import { z } from 'zod';

// Define a type for a chat turn specific to this game, including the question and evaluation
export type TranslationChatTurn = z.infer<typeof ChatTurnSchema> & {
  question?: z.infer<typeof TranslationChatQuestionSchema>;
  evaluation?: z.infer<typeof TranslationChatEvaluationSchema>;
};

// Extend the game context to include chat-specific state
// This context will be managed by the component using this game logic, not by the gameLogic itself.
export interface TranslationChatGameContext extends AdaptiveVocabularyGameContext {
  chatHistory: TranslationChatTurn[];
  currentChineseSentence?: string; // The sentence currently being translated
  gameSummary?: z.infer<typeof TranslationChatGameSummarySchema>;
  isLoadingAIResponse: boolean;
  aiError: string | null;
  // These will be passed from the component, not managed by gameLogic directly
  // allVocabularyItems: VocabularyItem[]; // Removed as it's not part of AdaptiveVocabularyGameContext
  // allGrammarPoints: GrammarPoint[]; // Removed as it's not part of AdaptiveVocabularyGameContext
  // category: string; // Removed as it's not part of AdaptiveVocabularyGameContext
}

export const translationChatGameLogic: IGameLogic = {
  gameType: 'translation-chat',

  getQuestionPrompt(item: VocabularyItem, context: AdaptiveVocabularyGameContext): React.ReactNode {
    const translationContext = context as TranslationChatGameContext;
    if (translationContext.currentChineseSentence) {
      return (
        <div className="text-center text-xl font-semibold text-gray-800">
          Translate this sentence:
          <p className="mt-2 text-3xl text-blue-700">{translationContext.currentChineseSentence}</p>
        </div>
      );
    }
    return <p>Loading new sentence...</p>;
  },

  generateOptions(_item: VocabularyItem, _allVocabulary: VocabularyItem[], _context: AdaptiveVocabularyGameContext): string[] {
    // This game mode does not use predefined options, as it's a free-form translation.
    return [];
  },

  evaluateAnswer(_item: VocabularyItem, _selectedAnswer: string, context: AdaptiveVocabularyGameContext): boolean {
    // For this game, the AI service handles the evaluation and updates the chat history.
    // This method is primarily for the AdaptiveVocabularyGame machine's internal flow.
    // We'll assume the answer is "correct" from the machine's perspective if the AI processed it.
    // The actual correctness feedback is in the AI's evaluation.
    console.warn("evaluateAnswer in translationChatGameLogic is a placeholder. AI service handles detailed evaluation.");
    // A simple heuristic: if the user provided any answer, consider it an "attempt" for the machine.
    // The real score is in the AI's evaluation.
    const translationContext = context as TranslationChatGameContext;
    const lastUserTurn = translationContext.chatHistory.findLast(turn => turn.role === 'user');
    return lastUserTurn?.evaluation?.score ? lastUserTurn.evaluation.score >= 70 : false; // Consider >=70 as "correct" for machine's internal tracking
  },

  getCorrectAnswerText(_item: VocabularyItem, context: AdaptiveVocabularyGameContext): string {
    // The "correct answer" for display comes from the AI's feedback on the user's translation.
    const translationContext = context as TranslationChatGameContext;
    const lastUserTurn = translationContext.chatHistory.findLast(turn => turn.role === 'user');
    if (lastUserTurn?.evaluation?.feedback) {
      // The AI's feedback should ideally contain the correct translation.
      // For now, we'll just display the feedback.
      return `AI Feedback: ${lastUserTurn.evaluation.feedback}`;
    }
    return "AI feedback not yet available.";
  },
};
