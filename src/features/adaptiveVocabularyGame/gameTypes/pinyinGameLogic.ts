import { IGameLogic } from '../gameLogic.interface';
import { VocabularyItem } from '@/types/content';
import { shuffleArray } from '@/lib/utils';

export const pinyinGameLogic: IGameLogic = {
  gameType: 'pinyin',

  getQuestionPrompt(item: VocabularyItem): React.ReactNode {
    return item.word; // Show Hanzi
  },

  generateOptions(item: VocabularyItem, allVocabulary: VocabularyItem[]): string[] {
    if (!item.pinyin || item.pinyin.length === 0) {
      return [];
    }
    // Select a random pinyin from the item's pinyin array as the correct answer
    const correctPinyin = item.pinyin;
    const incorrectPinyins = allVocabulary
      .filter(v => v.id !== item.id && v.pinyin)
      .map(v => v.pinyin as string) // Map to string directly
      .filter(pinyin => pinyin !== correctPinyin); // Filter out the exact correct pinyin

    const uniqueIncorrectPinyins = Array.from(new Set(incorrectPinyins));

    const selectedIncorrectPinyins = shuffleArray(uniqueIncorrectPinyins).slice(0, 3);
    const options = shuffleArray([...selectedIncorrectPinyins, correctPinyin]);
    return options;
  },

  evaluateAnswer(item: VocabularyItem, selectedAnswer: string): boolean {
    console.log(`selected: ${selectedAnswer}, real: ${item.pinyin}`);
    return item.pinyin === selectedAnswer;
  },

  getCorrectAnswerText(item: VocabularyItem): string {
    return item.pinyin || '';
  },
};