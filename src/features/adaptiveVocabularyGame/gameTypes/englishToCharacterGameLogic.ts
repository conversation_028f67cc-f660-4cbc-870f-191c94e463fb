import { IGameLogic } from '../gameLogic.interface';
import { VocabularyItem } from '@/types/content';
import { shuffleArray } from '@/lib/utils';

export const englishToCharacterGameLogic: IGameLogic = {
  gameType: 'english_to_character',

  getQuestionPrompt(item: VocabularyItem): React.ReactNode {
    return item.definitions?.[0] || 'Definition not available'; // Show English definition
  },

  generateOptions(item: VocabularyItem, allVocabulary: VocabularyItem[]): string[] {
    if (!item.word) {
      return [];
    }
    const correctCharacter = item.word;
    const incorrectCharacters = allVocabulary
      .filter(v => v.id !== item.id)
      .map(v => v.word)
      .filter((word): word is string => word !== correctCharacter && word !== undefined);

    const uniqueIncorrectCharacters = Array.from(new Set(incorrectCharacters));

    const selectedIncorrectCharacters = shuffleArray(uniqueIncorrectCharacters).slice(0, 3);
    const options = shuffleArray([...selectedIncorrectCharacters, correctCharacter]).filter((opt): opt is string => opt !== undefined);
    return options;
  },

  evaluateAnswer(item: VocabularyItem, selectedAnswer: string): boolean {
    return item.word === selectedAnswer;
  },

  getCorrectAnswerText(item: VocabularyItem): string {
    return item.word || '';
  },
};