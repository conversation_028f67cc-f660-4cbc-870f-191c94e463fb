// src/features/adaptiveVocabularyGame/adaptiveVocabularyGame.actions.ts
import { assign, fromPromise } from 'xstate';
import {
  AdaptiveVocabularyGameContext,
  AdaptiveVocabularyGameEvent,
  VocabLoaderDoneEvent,
  VocabLoaderErrorEvent,
} from './adaptiveVocabularyGame.types';
import {
  prepareQuestion,
  evaluateAnswer,
  resetGameContextFields,
  updateItemProgressLogic
} from './adaptiveVocabularyGame.logic';

export const assignInitialLoadData = assign(
  ({ event }: { event: Extract<AdaptiveVocabularyGameEvent, { type: 'LOAD_VOCABULARY' }> }) => ({
    initialVocabulary: event.vocabulary,
    gameLogic: event.gameLogic, // Assign gameLogic from the event
    ...resetGameContextFields(),
    score: 0,
    totalQuestions: 0,
  })
);

export const assignLoadedVocabularyAndReset = assign(
  ({ event }: { event: VocabLoaderDoneEvent }) => {
    console.log('[assignLoadedVocabularyAndReset] event.output:', JSON.parse(JSON.stringify(event.output)));
    return {
      prioritizedVocabulary: event.output,
      totalQuestions: event.output.length,
      currentQuestionIndex: 0,
      score: 0,
      finalScore: undefined,
      gameStartTime: Date.now(),
      errorMessage: undefined,
      currentItem: undefined,
      options: [],
      correctAnswerText: undefined,
      selectedAnswer: undefined,
      isCorrect: undefined,
      feedbackMessage: undefined,
      questionStartTime: undefined,
    };
  }
);

export const prepareQuestionAction = assign(
  ({ context }: { context: AdaptiveVocabularyGameContext }) => {
    console.log('[prepareQuestionAction] STARTING', JSON.parse(JSON.stringify(context)));
    console.log('[prepareQuestionAction] context.currentQuestionIndex:', context.currentQuestionIndex);

    const result = prepareQuestion(context);
    
    console.log('[prepareQuestionAction] currentItem:', JSON.parse(JSON.stringify(result.currentItem)));
    console.log('[prepareQuestionAction] generated options:', JSON.parse(JSON.stringify(result.options)));
    console.log('[prepareQuestionAction] COMPLETED, context should be updated now');
    
    return result;
  }
);

export const evaluateAnswerAction = assign(
  ({ context, event }: { context: AdaptiveVocabularyGameContext, event: Extract<AdaptiveVocabularyGameEvent, { type: 'ANSWER_SELECTED' }> }) => {
    console.log('[evaluateAnswerAction] Before evaluation: currentQuestionIndex =', context.currentQuestionIndex, ', totalQuestions =', context.totalQuestions);
    return evaluateAnswer(context, event.selectedOption);
  }
);

export const updateItemProgressAction = assign({
  lastReviewResult: fromPromise(async ({ input }: { input: { context: AdaptiveVocabularyGameContext } }) => {
    // console.log( // Original detailed logging can be restored if needed for further debugging
    //   '[updateItemProgressAction] Entered fromPromise. currentQuestionIndex =',
    //   input.context.currentQuestionIndex,
    //   ', totalQuestions =',
    //   input.context.totalQuestions
    // );
    // console.log('[updateItemProgressAction] STARTING. Context (full):', JSON.parse(JSON.stringify(input.context)));
    try {
      const result = await updateItemProgressLogic(input.context);
      // console.log('[updateItemProgressAction] updateItemProgressLogic result:', JSON.parse(JSON.stringify(result)));
      return result;
    } catch (error) {
      console.error('[updateItemProgressAction] Error within fromPromise logic:', error);
      return { error: true, message: (error as Error).message || 'Unknown error in updateItemProgressLogic' };
    }
  }),
});

export const incrementQuestionIndexAction = assign({
  currentQuestionIndex: ({ context }: { context: AdaptiveVocabularyGameContext }) => context.currentQuestionIndex + 1,
});

export const calculateTotalTimeAndFinalScoreAction = assign({
  totalTimeTaken: ({ context }: { context: AdaptiveVocabularyGameContext }) => (context.gameStartTime ? Date.now() - context.gameStartTime : undefined),
  finalScore: ({ context }: { context: AdaptiveVocabularyGameContext }) => context.score,
});

export const resetGameContextAction = assign(
    () => resetGameContextFields()
);

export const assignErrorMessageFromError = assign(
  ({ event }: { event: VocabLoaderErrorEvent }) => {
    const errorData = event.error;
    return {
      errorMessage: errorData instanceof Error ? errorData.message : 'An unknown error occurred during vocabulary loading.',
    };
  }
);

export const clearErrorMessageAction = assign({
    errorMessage: undefined
});

export const logShowingFeedbackEntryContext = assign(
  ({ context }: { context: AdaptiveVocabularyGameContext }) => {
    console.log(
      '[logShowingFeedbackEntryContext] Context: currentQuestionIndex =',
      context.currentQuestionIndex,
      ', totalQuestions =',
      context.totalQuestions
    );
    return context; // Return context to ensure no changes
  }
);