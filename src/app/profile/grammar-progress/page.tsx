"use client";

import React, { use<PERSON><PERSON><PERSON>, useState, useEffect } from 'react';
import { useGrammarProgress } from '@/hooks/useGrammarProgress';
import { getGrammarMasteryLevel } from '@/lib/spaced-repetition';
import MasteryLevelDisplay from '@/components/progress/MasteryLevelDisplay';
import GrammarExercisePerformanceDisplay from '@/components/progress/GrammarExercisePerformanceDisplay';
import SelfAssessmentInput from '@/components/progress/SelfAssessmentInput';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { GrammarPoint } from '@/types/content';
import { localDB, STORES } from '@/lib/localdb';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { GrammarProgress } from '@/types/progress'; // Keep this for type definition in combinedData

type CombinedGrammarData = GrammarPoint & {
  progress?: GrammarProgress;
  overallMastery: number;
  mastery: {
    level: ReturnType<typeof getGrammarMasteryLevel>;
    progress: number;
  };
};

type SortKey = 'ruleName' | 'overallMastery' | 'lastPracticedDate';

const GrammarProgressPage: React.FC = () => {
  const [grammarItems, setGrammarItems] = useState<GrammarPoint[]>([]);
  const [loadingItems, setLoadingItems] = useState(true);
  const [errorItems, setErrorItems] = useState<Error | null>(null);

  useEffect(() => {
    const fetchGrammarItems = async () => {
      try {
        const items = await localDB.getAll<GrammarPoint>(STORES.GRAMMAR);
        setGrammarItems(items);
      } catch (err) {
        setErrorItems(err as Error);
      } finally {
        setLoadingItems(false);
      }
    };
    fetchGrammarItems();
  }, []);

  const { progress: grammarProgressMap, isLoading: loadingProgress, updateGrammarSelfAssessment } = useGrammarProgress();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortKey>('lastPracticedDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedGrammarItem, setSelectedGrammarItem] = useState<CombinedGrammarData | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const combinedData = useMemo(() => {
    if (loadingItems || loadingProgress) return [];

    return grammarItems.map(item => {
      const progress = grammarProgressMap[item.id];
      const overallMastery = progress?.overallMastery ?? 0;
      const masteryLevel = getGrammarMasteryLevel(overallMastery);
      
      return {
        ...item,
        progress,
        overallMastery,
        mastery: {
          level: masteryLevel,
          progress: overallMastery * 100, // Convert 0-1 to 0-100 for progress bar
        },
      };
    });
  }, [grammarItems, grammarProgressMap, loadingItems, loadingProgress]);

  const filteredAndSortedData = useMemo(() => {
    const filtered = combinedData.filter(item =>
      item.ruleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.explanation.toLowerCase().includes(searchTerm.toLowerCase()) // Assuming explanation exists
    );

    filtered.sort((a, b) => {
      let valA: string | number | undefined;
      let valB: string | number | undefined;

      switch (sortBy) {
        case 'ruleName':
          valA = a.ruleName;
          valB = b.ruleName;
          break;
        case 'overallMastery':
          valA = a.overallMastery;
          valB = b.overallMastery;
          break;
        case 'lastPracticedDate':
          valA = a.progress?.lastPracticedDate ? new Date(a.progress.lastPracticedDate).getTime() : 0;
          valB = b.progress?.lastPracticedDate ? new Date(b.progress.lastPracticedDate).getTime() : 0;
          break;
        default:
          valA = 0;
          valB = 0;
      }

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortOrder === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
      }
      if (typeof valA === 'number' && typeof valB === 'number') {
        return sortOrder === 'asc' ? valA - valB : valB - valA;
      }
      return 0;
    });

    return filtered;
  }, [combinedData, searchTerm, sortBy, sortOrder]);

  const handleSelfAssessmentUpdate = async (score: number) => {
    if (selectedGrammarItem) {
      await updateGrammarSelfAssessment(selectedGrammarItem.id, score);
      setIsDialogOpen(false); // Close dialog after update
    }
  };

  const openDetailDialog = (item: CombinedGrammarData) => {
    setSelectedGrammarItem(item);
    setIsDialogOpen(true);
  };

  if (loadingItems || loadingProgress) return <div className="container mx-auto p-4">Loading grammar progress...</div>;
  if (errorItems) return <div className="container mx-auto p-4 text-red-500">Error loading grammar items: {errorItems.message}</div>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Grammar Progress Dashboard</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filter & Sort</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="search">Search</Label>
            <Input
              id="search"
              type="text"
              placeholder="Search rule name or explanation..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="sortBy">Sort By</Label>
            <select
              id="sortBy"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortKey)}
            >
              <option value="lastPracticedDate">Last Practiced</option>
              <option value="overallMastery">Mastery Level</option>
              <option value="ruleName">Rule Name</option>
            </select>
          </div>
          <div>
            <Label htmlFor="sortOrder">Sort Order</Label>
            <select
              id="sortOrder"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Your Grammar Rules</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rule Name</TableHead>
                <TableHead>Mastery Level</TableHead>
                <TableHead>Last Practiced</TableHead>
                <TableHead>Self-Assessment</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground">
                    No grammar rules found or matching your search.
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-bold">{item.ruleName}</TableCell>
                    <TableCell>
                      <MasteryLevelDisplay
                        level={item.mastery.level}
                        progress={item.mastery.progress}
                      />
                    </TableCell>
                    <TableCell>
                      {item.progress?.lastPracticedDate
                        ? new Date(item.progress.lastPracticedDate).toLocaleDateString()
                        : 'N/A'}
                    </TableCell>
                    <TableCell>
                      {item.progress?.selfAssessmentScore != null
                        ? item.progress.selfAssessmentScore
                        : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" onClick={() => openDetailDialog(item)}>
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {selectedGrammarItem && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedGrammarItem.ruleName} Progress Details</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <p className="text-sm text-muted-foreground">
                Overall Mastery: <MasteryLevelDisplay level={selectedGrammarItem.mastery.level} progress={selectedGrammarItem.mastery.progress} />
              </p>
              <GrammarExercisePerformanceDisplay performance={selectedGrammarItem.progress?.exercisePerformance || {}} />
              <SelfAssessmentInput
                currentScore={selectedGrammarItem.progress?.selfAssessmentScore ?? null}
                onUpdate={handleSelfAssessmentUpdate}
                min={1}
                max={5}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default GrammarProgressPage;