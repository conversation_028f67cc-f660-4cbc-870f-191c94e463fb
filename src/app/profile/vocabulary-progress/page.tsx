"use client";

import React, { useMemo, useState, useEffect } from 'react';
import { useVocabularyProgress } from '@/hooks/useVocabularyProgress';
import { getVocabularyMasteryLevel } from '@/lib/spaced-repetition';
import MasteryLevelDisplay from '@/components/progress/MasteryLevelDisplay';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { VocabularyItem } from '@/types/content';
import { format } from 'date-fns';
import { localDB, STORES } from '@/lib/localdb';

type SortKey = 'character' | 'pinyin' | 'meaning' | 'mastery' | 'dueDate';

const VocabularyProgressPage: React.FC = () => {
  const [vocabularyItems, setVocabularyItems] = useState<VocabularyItem[]>([]);
  const [loadingItems, setLoadingItems] = useState(true);
  const [errorItems, setErrorItems] = useState<Error | null>(null);

  useEffect(() => {
    const fetchVocabularyItems = async () => {
      try {
        const items = await localDB.getAll<VocabularyItem>(STORES.VOCABULARY);
        setVocabularyItems(items);
      } catch (err) {
        setErrorItems(err as Error);
      } finally {
        setLoadingItems(false);
      }
    };
    fetchVocabularyItems();
  }, []);

  const { progress: vocabularyProgressMap, isLoading: loadingProgress } = useVocabularyProgress(vocabularyItems);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortKey>('dueDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const combinedData = useMemo(() => {
    if (loadingItems || loadingProgress) return [];

    return vocabularyItems.map(item => {
      const progress = vocabularyProgressMap[item.id];
      const masteryLevel = progress ? getVocabularyMasteryLevel(progress) : 'Novice';
      
      // Calculate a numerical progress for the progress bar
      let masteryProgress = 0;
      if (progress) {
        // Simple mapping for demonstration, can be refined
        if (masteryLevel === 'Novice') masteryProgress = 10;
        else if (masteryLevel === 'Beginner') masteryProgress = 30;
        else if (masteryLevel === 'Intermediate') masteryProgress = 50;
        else if (masteryLevel === 'Advanced') masteryProgress = 70;
        else if (masteryLevel === 'Mastered') masteryProgress = 90;
        
        // Further refine based on efactor within a level
        // Scale efactor (1.3 to 2.5) to a small range (e.g., 0-10) and add to base progress
        masteryProgress += ((progress.efactor - 1.3) / (2.5 - 1.3)) * 10; 
        masteryProgress = Math.min(100, Math.max(0, masteryProgress)); // Clamp between 0 and 100
      }

      return {
        ...item,
        progress,
        mastery: {
          level: masteryLevel,
          progress: masteryProgress,
        },
      };
    });
  }, [vocabularyItems, vocabularyProgressMap, loadingItems, loadingProgress]);

  const filteredAndSortedData = useMemo(() => {
    const filtered = combinedData.filter(item =>
      item.character.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.pinyin.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.meaning.toLowerCase().includes(searchTerm.toLowerCase())
    );

    filtered.sort((a, b) => {
      let valA: string | number | undefined;
      let valB: string | number | undefined;

      switch (sortBy) {
        case 'character':
          valA = a.character;
          valB = b.character;
          break;
        case 'pinyin':
          valA = a.pinyin;
          valB = b.pinyin;
          break;
        case 'meaning':
          valA = a.meaning;
          valB = b.meaning;
          break;
        case 'mastery':
          valA = a.mastery.progress;
          valB = b.mastery.progress;
          break;
        case 'dueDate':
          valA = a.progress?.dueDate ? new Date(a.progress.dueDate).getTime() : 0;
          valB = b.progress?.dueDate ? new Date(b.progress.dueDate).getTime() : 0;
          break;
        default:
          valA = 0;
          valB = 0;
      }

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortOrder === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
      }
      if (typeof valA === 'number' && typeof valB === 'number') {
        return sortOrder === 'asc' ? valA - valB : valB - valA;
      }
      return 0;
    });

    return filtered;
  }, [combinedData, searchTerm, sortBy, sortOrder]);

  if (loadingItems || loadingProgress) return <div className="container mx-auto p-4">Loading vocabulary progress...</div>;
  if (errorItems) return <div className="container mx-auto p-4 text-red-500">Error loading vocabulary items: {errorItems.message}</div>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Vocabulary Progress Dashboard</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filter & Sort</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="search">Search</Label>
            <Input
              id="search"
              type="text"
              placeholder="Search character, pinyin, or meaning..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="sortBy">Sort By</Label>
            <select
              id="sortBy"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortKey)}
            >
              <option value="dueDate">Next Review Date</option>
              <option value="mastery">Mastery Level</option>
              <option value="character">Character</option>
              <option value="pinyin">Pinyin</option>
              <option value="meaning">Meaning</option>
            </select>
          </div>
          <div>
            <Label htmlFor="sortOrder">Sort Order</Label>
            <select
              id="sortOrder"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Your Vocabulary Items</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Character</TableHead>
                <TableHead>Pinyin</TableHead>
                <TableHead>Meaning</TableHead>
                <TableHead>Mastery Level</TableHead>
                <TableHead>Next Review</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground">
                    No vocabulary items found or matching your search.
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-bold text-lg">{item.character}</TableCell>
                    <TableCell>{item.pinyin}</TableCell>
                    <TableCell>{item.meaning}</TableCell>
                    <TableCell>
                      <MasteryLevelDisplay
                        level={item.mastery.level}
                        progress={item.mastery.progress}
                      />
                    </TableCell>
                    <TableCell>
                      {item.progress?.dueDate
                        ? format(new Date(item.progress.dueDate), 'PPP')
                        : 'N/A'}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default VocabularyProgressPage;