"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Head<PERSON> } from "@/components/header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { collection, query, where, getDocs, Timestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import Link from "next/link";

interface ProgressItem {
  id: string;
  itemType: string;
  itemId: string;
  status: string;
  attempts: number;
  correctAttempts: number;
  masteryScore: number;
  lastPracticed: Timestamp | null;
  itemTitle: string;
  chapterNumber: number;
}

export default function ProfilePage() {
  const router = useRouter();
  const { currentUser, userProfile, logout } = useAuth();

  const [progressItems, setProgressItems] = useState<ProgressItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalItems: 0,
    mastered: 0,
    inProgress: 0,
    new: 0,
    averageMastery: 0
  });

  useEffect(() => {
    if (!currentUser) {
      router.push("/login");
      return;
    }

    async function fetchUserProgress() {
      try {
        if (!currentUser) {
          console.error("No user is logged in");
          return;
        }

        const progressRef = collection(db, "userProgress");
        const q = query(progressRef, where("userId", "==", currentUser.uid));
        const querySnapshot = await getDocs(q);

        const items: ProgressItem[] = [];

        for (const doc of querySnapshot.docs) {
          const data = doc.data() as ProgressItem;

          // Fetch item details
          let itemTitle = "";
          let chapterNumber = 0;

          if (data.itemType === "grammar") {
            const grammarRef = collection(db, "grammarPoints");
            const grammarQuery = query(grammarRef, where("pointId", "==", data.itemId));
            const grammarSnapshot = await getDocs(grammarQuery);

            if (!grammarSnapshot.empty) {
              const grammarData = grammarSnapshot.docs[0].data();
              itemTitle = grammarData.title;
              chapterNumber = grammarData.chapterNumber;
            }
          } else if (data.itemType === "vocabulary") {
            const vocabRef = collection(db, "vocabularyItems");
            const vocabQuery = query(vocabRef, where("itemId", "==", data.itemId));
            const vocabSnapshot = await getDocs(vocabQuery);

            if (!vocabSnapshot.empty) {
              const vocabData = vocabSnapshot.docs[0].data();
              itemTitle = vocabData.word;
              chapterNumber = vocabData.chapterNumber;
            }
          }

          items.push({
            ...data,
            id: doc.id,
            itemTitle,
            chapterNumber
          });
        }

        // Sort by mastery score (descending) and then by chapter number
        items.sort((a, b) => {
          if (b.masteryScore !== a.masteryScore) {
            return b.masteryScore - a.masteryScore;
          }
          return a.chapterNumber - b.chapterNumber;
        });

        setProgressItems(items);

        // Calculate stats
        const totalItems = items.length;
        const mastered = items.filter(item => item.status === "mastered").length;
        const inProgress = items.filter(item => item.status === "in-progress").length;
        const newItems = items.filter(item => item.status === "new").length;
        const totalMastery = items.reduce((sum, item) => sum + item.masteryScore, 0);
        const averageMastery = totalItems > 0 ? Math.round(totalMastery / totalItems) : 0;

        setStats({
          totalItems,
          mastered,
          inProgress,
          new: newItems,
          averageMastery
        });

      } catch (error) {
        console.error("Error fetching user progress:", error);
        toast.error("Failed to load progress data");
      } finally {
        setLoading(false);
      }
    }

    fetchUserProgress();
  }, [currentUser, router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-500";
      case "in-progress":
        return "bg-yellow-500";
      case "mastered":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 container py-10">
        <div className="flex flex-col space-y-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Profile</h1>
            <p className="text-muted-foreground">
              View your learning progress and statistics
            </p>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <p>Loading profile data...</p>
            </div>
          ) : (
            <>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>User Information</CardTitle>
                  </CardHeader>
                  <CardContent className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarFallback className="text-xl">
                        {userProfile?.username?.charAt(0).toUpperCase() ?? "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="text-xl font-medium">{userProfile?.username}</h3>
                      <p className="text-muted-foreground">{userProfile?.email}</p>
                      <p className="text-sm text-muted-foreground">
                        Member since {userProfile?.createdAt?.toDate().toLocaleDateString()}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button asChild variant="outline">
                      <a href="/settings">Edit Settings</a>
                    </Button>
                    <Button variant="destructive" onClick={async () => {
                      try {
                        await logout();
                        toast.success("Logged out successfully");
                        router.push("/login");
                      } catch (error) {
                        console.error("Logout error:", error);
                        toast.error("Failed to log out");
                      }
                    }}>
                      Logout
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Learning Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Items:</span>
                        <span>{stats.totalItems}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Mastered:</span>
                        <span>{stats.mastered}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>In Progress:</span>
                        <span>{stats.inProgress}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>New:</span>
                        <span>{stats.new}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Average Mastery:</span>
                        <span>{stats.averageMastery}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Learning Progress</CardTitle>
                  <CardDescription>
                    Track your progress on grammar points and vocabulary items
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {progressItems.length === 0 ? (
                    <div className="text-center py-8">
                      <p>You haven&apos;t started learning any items yet.</p>
                      <Button asChild className="mt-4">
                        <Link href="/grammar">Start Learning</Link>
                      </Button>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Item</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Chapter</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Mastery</TableHead>
                          <TableHead>Last Practiced</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {progressItems.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.itemTitle}</TableCell>
                            <TableCell className="capitalize">{item.itemType}</TableCell>
                            <TableCell>{item.chapterNumber}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full ${getStatusColor(item.status)}`}></div>
                                <span className="capitalize">{item.status}</span>
                              </div>
                            </TableCell>
                            <TableCell>{item.masteryScore}%</TableCell>
                            <TableCell>
                              {item.lastPracticed?.toDate().toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <Button asChild variant="outline" size="sm">
                                <Link href={`/${item.itemType}/${item.itemId}`}>
                                  Study
                                </Link>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </main>
    </div>
  );
}
