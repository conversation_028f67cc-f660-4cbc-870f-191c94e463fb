'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import ChatInterface from '@/components/chat/ChatInterface';
import { useAuth } from '@/hooks/useAuth';

export default function ChatPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  // For now, we'll use a placeholder user ID
  // In a real app, this would come from authentication
  const userId = user?.uid || 'demo-user';

  const handleExit = () => {
    router.push('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <ChatInterface 
      userId={userId}
      onExit={handleExit}
    />
  );
}
