'use client';

import { useState } from 'react';
import { Header } from "@/components/header";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { useRouter } from 'next/navigation';

const categories = ['综合', '听说', '写作', '阅读'];
const resourceTypes = [
  { id: 'grammar', title: 'Grammar', description: 'Learn Chinese grammar points systematically' },
  { id: 'vocabulary', title: 'Vocabulary', description: 'Build your Chinese vocabulary' }
];

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const router = useRouter();

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSelectedType(null); // Reset type selection when category changes
  };

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
    if (selectedCategory) {
      router.push(`/${type}?category=${encodeURIComponent(selectedCategory)}&type=${type}`);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-10 max-w-7xl">
        <section className="py-12 md:py-16 lg:py-20">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  Master Chinese with AI-Powered Learning
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  Choose a category and resource type to start learning Chinese systematically.
                </p>
              </div>

              {/* Category Selection */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-4xl mt-8">
                {categories.map((category) => (
                  <motion.div
                    key={category}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Card
                      className={`cursor-pointer transition-colors ${selectedCategory === category ? 'border-primary bg-primary/10' : ''}`}
                      onClick={() => handleCategorySelect(category)}
                    >
                      <CardHeader>
                        <CardTitle className="text-center">{category}</CardTitle>
                      </CardHeader>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Resource Type Selection */}
              {selectedCategory && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="grid md:grid-cols-2 gap-6 w-full max-w-4xl mt-8"
                >
                  {resourceTypes.map((type) => (
                    <motion.div
                      key={type.id}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Card
                        className={`cursor-pointer transition-colors ${selectedType === type.id ? 'border-primary bg-primary/10' : ''}`}
                        onClick={() => handleTypeSelect(type.id)}
                      >
                        <CardHeader>
                          <CardTitle>{type.title}</CardTitle>
                          <CardDescription>{type.description}</CardDescription>
                        </CardHeader>
                      </Card>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row md:gap-6">
            <p className="text-center text-sm leading-loose text-muted-foreground">
              © 2025 Master Chinese. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
