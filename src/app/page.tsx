'use client';

import { useState } from 'react';
import { Header } from "@/components/header";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { useRouter } from 'next/navigation';
import { MessageSquare, BookOpen, Sparkles } from 'lucide-react';

const categories = ['综合', '听说', '写作', '阅读'];
const resourceTypes = [
  { id: 'grammar', title: 'Grammar', description: 'Learn Chinese grammar points systematically' },
  { id: 'vocabulary', title: 'Vocabulary', description: 'Build your Chinese vocabulary' }
];

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const router = useRouter();

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSelectedType(null); // Reset type selection when category changes
  };

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
    if (selectedCategory) {
      router.push(`/${type}?category=${encodeURIComponent(selectedCategory)}&type=${type}`);
    }
  };

  const handleChatSelect = () => {
    router.push('/chat');
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-10 max-w-7xl">
        <section className="py-12 md:py-16 lg:py-20">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  Master Chinese with AI-Powered Learning
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  Practice Chinese through AI-powered conversations or study systematically by category.
                </p>
              </div>

              {/* Chat Feature - Primary Option */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="w-full max-w-2xl mt-8"
              >
                <Card
                  className="cursor-pointer transition-all hover:shadow-lg border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50"
                  onClick={handleChatSelect}
                >
                  <CardHeader className="text-center p-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <MessageSquare className="h-6 w-6 text-blue-600" />
                      <Badge className="bg-blue-600 text-white">
                        <Sparkles className="h-3 w-3 mr-1" />
                        New Feature
                      </Badge>
                    </div>
                    <CardTitle className="text-xl text-blue-700">
                      Freestyle Conversation Practice
                    </CardTitle>
                    <CardDescription className="text-base mt-2">
                      Practice Chinese through realistic conversations on any topic.
                      AI adapts to your skill level and focuses on your problem areas.
                    </CardDescription>
                    <Button className="mt-4 bg-blue-600 hover:bg-blue-700">
                      Start Conversation Practice
                    </Button>
                  </CardHeader>
                </Card>
              </motion.div>

              {/* Divider */}
              <div className="flex items-center gap-4 w-full max-w-4xl mt-8">
                <div className="flex-1 h-px bg-gray-300"></div>
                <span className="text-gray-500 text-sm">OR</span>
                <div className="flex-1 h-px bg-gray-300"></div>
              </div>

              {/* Traditional Study Mode */}
              <div className="w-full max-w-4xl">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-700 flex items-center justify-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Traditional Study Mode
                  </h2>
                  <p className="text-gray-500 mt-1">Study by category and resource type</p>
                </div>
              </div>

              {/* Category Selection */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-4xl mt-8">
                {categories.map((category) => (
                  <motion.div
                    key={category}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Card
                      className={`cursor-pointer transition-colors ${selectedCategory === category ? 'border-primary bg-primary/10' : ''}`}
                      onClick={() => handleCategorySelect(category)}
                    >
                      <CardHeader>
                        <CardTitle className="text-center">{category}</CardTitle>
                      </CardHeader>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Resource Type Selection */}
              {selectedCategory && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="grid md:grid-cols-2 gap-6 w-full max-w-4xl mt-8"
                >
                  {resourceTypes.map((type) => (
                    <motion.div
                      key={type.id}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Card
                        className={`cursor-pointer transition-colors ${selectedType === type.id ? 'border-primary bg-primary/10' : ''}`}
                        onClick={() => handleTypeSelect(type.id)}
                      >
                        <CardHeader>
                          <CardTitle>{type.title}</CardTitle>
                          <CardDescription>{type.description}</CardDescription>
                        </CardHeader>
                      </Card>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row md:gap-6">
            <p className="text-center text-sm leading-loose text-muted-foreground">
              © 2025 Master Chinese. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
