import { getGrammarPointById } from '@/lib/firestore';
import { GrammarPoint } from '@/types/content';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import TextWithTTS from '@/components/ui/TextWithTTS';

interface GrammarPointPageProps {
  params: {
    itemId: string;
  };
}

export default async function GrammarPointPage({ params }: GrammarPointPageProps) {
  const { itemId } = await params;
  const item: GrammarPoint | undefined = await getGrammarPointById(itemId);

  if (!item) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-4"><TextWithTTS text={item.title} /></h1>

      {item.category && <p className="text-sm text-gray-500">Category: {item.category}</p>}
      {item.chapter && <p className="text-sm text-gray-500">Chapter: {item.chapter}</p>}
      {item.tags && item.tags.length > 0 && (
        <p className="text-sm text-gray-500">Tags: {item.tags.join(', ')}</p>
      )}
       {item.relatedPoints && item.relatedPoints.length > 0 && (
        <p className="text-sm text-gray-500">Related Points: {item.relatedPoints.join(', ')}</p>
      )}


      <div className="mt-6">
        <h2 className="text-2xl font-semibold mb-3">Explanation</h2>
        <p><TextWithTTS text={item.explanation} /></p>
      </div>

      {item.examples && item.examples.length > 0 && (
        <div className="mt-6">
          <h2 className="text-2xl font-semibold mb-3">Examples</h2>
          {item.examples.map((example, index) => (
            <div key={index} className="mb-4 p-4 border rounded">
              <p><strong>Chinese:</strong> <TextWithTTS text={example.chinese} /></p>
              <p><strong>Pinyin:</strong> <TextWithTTS text={example.pinyin} /></p>
              <p><strong>English:</strong> {example.english}</p>
            </div>
          ))}
        </div>
      )}

      <div className="mt-8">
        <Button asChild>
            <Link href={`/grammar/${itemId}/practice`} passHref>
                Practice this Grammar Point
            </Link>
          </Button>
      </div>
    </div>
  );
}