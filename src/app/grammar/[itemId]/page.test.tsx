import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import GrammarPointPage from './page';
import * as firestore from '@/lib/firestore';
import * as nextNavigation from 'next/navigation';
// import TextWithTTS from '@/components/ui/TextWithTTS'; // Assuming this is the correct path - Mocked below

// Mock TextWithTTS component
jest.mock('@/components/ui/TextWithTTS', () => {
  const MockTextWithTTS = ({ text, lang }: { text: string; lang: string }) => (
    <div data-testid={`text-with-tts-${text.replace(/\s+/g, '-').toLowerCase()}`} data-lang={lang}>
      {text}
      <button data-testid={`tts-button-${text.replace(/\s+/g, '-').toLowerCase()}`} onClick={() => mockSpeak(text, lang)}>
        Speak
      </button>
    </div>
  );
  return MockTextWithTTS;
});


// Mock useTextToSpeech hook
const mockSpeak = jest.fn();
jest.mock('@/hooks/useTextToSpeech', () => ({
  __esModule: true,
  default: () => ({
    speak: mockSpeak,
    isPlaying: false,
    isAvailable: true,
    error: null,
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
  notFound: jest.fn(),
  useRouter: jest.fn(() => ({ push: jest.fn() })),
  usePathname: jest.fn(() => '/grammar/some-id'),
}));

// Mock firestore
jest.mock('@/lib/firestore', () => ({
  getGrammarPointById: jest.fn(),
}));

const mockGrammarPoint = {
  id: 'test-grammar-id',
  title: 'Test Grammar Title',
  explanation: 'This is a test explanation.',
  examples: [
    { sentence_chinese: '你好世界', sentence_pinyin: 'Nǐ hǎo shìjiè', translation: 'Hello World' },
    { sentence_chinese: '再见朋友', sentence_pinyin: 'Zàijiàn péngyǒu', translation: 'Goodbye Friend' },
  ],
  level: 'HSK1',
  related_grammar_points: [],
  related_vocabulary: [],
  exercises: [],
  common_mistakes: [],
  notes: '',
  tags: [],
  slug: 'test-grammar-title',
  book: 'Standard Course HSK 1',
  lesson: 'Lesson 1',
  category: 'Greetings',
  created_at: new Date(),
  updated_at: new Date(),
};

describe('GrammarPointPage - src/app/grammar/[itemId]/page.tsx', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (firestore.getGrammarPointById as jest.Mock).mockResolvedValue(mockGrammarPoint);
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
  });

  it('renders TextWithTTS for title, explanation, and examples', async () => {
    render(await GrammarPointPage({ params: { itemId: 'test-grammar-id' } }));

    // Check title
    expect(screen.getByTestId('text-with-tts-test-grammar-title')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-test-grammar-title')).toHaveTextContent('Test Grammar Title');
    expect(screen.getByTestId('text-with-tts-test-grammar-title')).toHaveAttribute('data-lang', 'zh');

    // Check explanation
    expect(screen.getByTestId('text-with-tts-this-is-a-test-explanation.')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-this-is-a-test-explanation.')).toHaveTextContent('This is a test explanation.');
     // Assuming explanation is in English, or adjust if it can be Chinese
    expect(screen.getByTestId('text-with-tts-this-is-a-test-explanation.')).toHaveAttribute('data-lang', 'en');


    // Check examples - Chinese
    expect(screen.getByTestId('text-with-tts-你好世界')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-你好世界')).toHaveTextContent('你好世界');
    expect(screen.getByTestId('text-with-tts-你好世界')).toHaveAttribute('data-lang', 'zh');

    expect(screen.getByTestId('text-with-tts-再见朋友')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-再见朋友')).toHaveTextContent('再见朋友');
    expect(screen.getByTestId('text-with-tts-再见朋友')).toHaveAttribute('data-lang', 'zh');

    // Check examples - Pinyin
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo-shìjiè')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo-shìjiè')).toHaveTextContent('Nǐ hǎo shìjiè');
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo-shìjiè')).toHaveAttribute('data-lang', 'pinyin');


    expect(screen.getByTestId('text-with-tts-zàijiàn-péngyǒu')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-zàijiàn-péngyǒu')).toHaveTextContent('Zàijiàn péngyǒu');
    expect(screen.getByTestId('text-with-tts-zàijiàn-péngyǒu')).toHaveAttribute('data-lang', 'pinyin');
  });

  it('calls speak function when TTS button is clicked for title', async () => {
    render(await GrammarPointPage({ params: { itemId: 'test-grammar-id' } }));
    const ttsButton = screen.getByTestId('tts-button-test-grammar-title');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('Test Grammar Title', 'zh');
  });

  it('calls speak function when TTS button is clicked for an example sentence (Chinese)', async () => {
    render(await GrammarPointPage({ params: { itemId: 'test-grammar-id' } }));
    const ttsButton = screen.getByTestId('tts-button-你好世界');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('你好世界', 'zh');
  });

  it('calls speak function when TTS button is clicked for an example sentence (Pinyin)', async () => {
    render(await GrammarPointPage({ params: { itemId: 'test-grammar-id' } }));
    const ttsButton = screen.getByTestId('tts-button-nǐ-hǎo-shìjiè');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('Nǐ hǎo shìjiè', 'pinyin');
  });


  it('calls notFound if grammar point is not found', async () => {
    (firestore.getGrammarPointById as jest.Mock).mockResolvedValue(null);
    await GrammarPointPage({ params: { itemId: 'non-existent-id' } });
    expect(nextNavigation.notFound).toHaveBeenCalled();
  });
});