'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getAllGrammarPoints, getGrammarPointsByCategoryAndChapter, getMetadata } from '@/lib/firestore';
import { GrammarPoint, CategoryStructureMetadata as Metadata, TagStructure } from '@/types/content';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { Button } from '@/components/ui/button';
import { useGrammarProgress } from '@/hooks/useGrammarProgress';
import { AdaptiveGrammarGame } from '@/components/games/grammar/AdaptiveGrammarGame';

export default function GrammarPage() {
  const searchParams = useSearchParams();
  const category = searchParams.get('category');
  const [grammarPoints, setGrammarPoints] = useState<GrammarPoint[]>([]);
  const [metadata, setMetadata] = useState<Metadata | undefined>(undefined);
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdaptiveGameActive, setIsAdaptiveGameActive] = useState(false); // New state for adaptive game

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [items, meta] = await Promise.all([
          category ? getGrammarPointsByCategoryAndChapter(category, 'all') : getAllGrammarPoints(),
          getMetadata()
        ]);
        setGrammarPoints(items);
        setMetadata(meta);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [category]);

  // Filter grammar points based on selected tag
  const filteredGrammarPoints = selectedTag === 'all'
    ? grammarPoints
    : grammarPoints.filter(point => point.tags && point.tags.includes(selectedTag)); // Filter by tags

  // Extract unique tags from metadata for the filter dropdown
  const extractTags = (tagStructure: TagStructure, tags: Set<string>) => {
    for (const key in tagStructure) {
      if (key !== 'description' && key !== 'items') {
        tags.add(key);
        if (typeof tagStructure[key] === 'object' && tagStructure[key] !== null && !Array.isArray(tagStructure[key])) {
          extractTags(tagStructure[key] as TagStructure, tags);
        }
      }
    }
  };

  const uniqueTags = new Set<string>();
  if (metadata?.refined_tag_structure) {
    extractTags(metadata.refined_tag_structure.topical_tags, uniqueTags);
    extractTags(metadata.refined_tag_structure.grammatical_tags, uniqueTags);
  }
  const tags = Array.from(uniqueTags).sort(); // Convert set to array and sort

  const {
    progress: grammarProgressMap,
    updateGrammarExercisePerformance,
    getPrioritizedGrammarRules,
    isLoading: loadingGrammarProgress,
  } = useGrammarProgress();

  if (loading || loadingGrammarProgress) {
    return <div>Loading grammar points...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (isAdaptiveGameActive) {
    return (
      <div className="container mx-auto p-4">
        <Button onClick={() => setIsAdaptiveGameActive(false)} className="mb-4">
          ← Back to Grammar List
        </Button>
        <AdaptiveGrammarGame
          grammarPoints={grammarPoints}
          grammarProgressMap={grammarProgressMap}
          updateGrammarExercisePerformance={updateGrammarExercisePerformance}
          getPrioritizedGrammarRules={getPrioritizedGrammarRules}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Grammar Points</h1>

      <div className="mb-6">
        <Button onClick={() => setIsAdaptiveGameActive(true)} className="bg-blue-500 hover:bg-blue-600 text-white">
          Start Adaptive Grammar Practice
        </Button>
      </div>

      {/* Tag Filter */}
      <div className="mb-4">
        <label htmlFor="tag-select" className="mr-2">Filter by Tag:</label>
        <select
          id="tag-select"
          value={selectedTag}
          onChange={(e) => setSelectedTag(e.target.value)}
          className="border rounded p-1"
        >
          <option value="all">All Tags</option>
          {tags.map(tag => (
            <option key={tag} value={tag}>{tag}</option>
          ))}
        </select>
      </div>

      {filteredGrammarPoints.length === 0 ? (
        <p>No grammar points found for the selected category.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredGrammarPoints.map((point) => (
            <div key={point.id} className="border p-4 rounded shadow">
            <a href={`/grammar/${point.id}`}>
              <h2 className="text-xl font-semibold mb-2"><TextWithTTS text={point.title} /></h2>
            </a>
            <p className="mb-2"><strong>Explanation:</strong> <TextWithTTS text={point.explanation} /></p>
            <div>
              <strong>Examples:</strong>
              <ul className="list-disc ml-5">
                {point.examples.map((example, index) => (
                  <li key={index}>
                    <TextWithTTS text={example.chinese} /> (<TextWithTTS text={example.pinyin} />) - {example.english}
                  </li>
                ))}
              </ul>
            </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}