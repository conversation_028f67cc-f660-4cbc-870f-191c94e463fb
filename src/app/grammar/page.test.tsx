import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import GrammarPage from './page';
import * as firestore from '@/lib/firestore';
import * as nextNavigation from 'next/navigation';

// Mock TextWithTTS component
const mockSpeak = jest.fn();
jest.mock('@/components/ui/TextWithTTS', () => {
  const MockTextWithTTS = ({ text, lang, id }: { text: string; lang: string; id?: string }) => (
    <div data-testid={id || `text-with-tts-${text.replace(/\s+/g, '-').toLowerCase()}`} data-lang={lang}>
      {text}
      <button data-testid={`tts-button-${id || text.replace(/\s+/g, '-').toLowerCase()}`} onClick={() => mockSpeak(text, lang)}>
        Speak
      </button>
    </div>
  );
  return MockTextWithTTS;
});

// Mock useTextToSpeech hook
jest.mock('@/hooks/useTextToSpeech', () => ({
  __esModule: true,
  default: () => ({
    speak: mockSpeak,
    isPlaying: false,
    isAvailable: true,
    error: null,
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams()), // Default mock
  useRouter: jest.fn(() => ({ push: jest.fn() })),
  usePathname: jest.fn(() => '/grammar'),
}));

// Mock firestore
jest.mock('@/lib/firestore', () => ({
  getAllGrammarPoints: jest.fn(),
  getMetadata: jest.fn(),
}));

const mockGrammarPoints = [
  {
    id: 'gp1',
    title: 'Grammar Point 1 Title',
    explanation: 'Explanation for GP1.',
    examples: [
      { sentence_chinese: '句子一', sentence_pinyin: 'Jùzi yī', translation: 'Sentence one' },
    ],
    level: 'HSK1',
    slug: 'grammar-point-1-title',
    book: 'Book 1',
    lesson: 'Lesson 1',
    category: 'Category A',
    created_at: new Date(),
    updated_at: new Date(),
    related_grammar_points: [],
    related_vocabulary: [],
    exercises: [],
    common_mistakes: [],
    notes: '',
    tags: [],
  },
  {
    id: 'gp2',
    title: 'Grammar Point 2 Title',
    explanation: 'Explanation for GP2.',
    examples: [
      { sentence_chinese: '句子二', sentence_pinyin: 'Jùzi èr', translation: 'Sentence two' },
    ],
    level: 'HSK2',
    slug: 'grammar-point-2-title',
    book: 'Book 2',
    lesson: 'Lesson 2',
    category: 'Category B',
    created_at: new Date(),
    updated_at: new Date(),
    related_grammar_points: [],
    related_vocabulary: [],
    exercises: [],
    common_mistakes: [],
    notes: '',
    tags: [],
  },
];

const mockMetadata = {
  levels: ['HSK1', 'HSK2'],
  books: ['Book 1', 'Book 2'],
  categories: ['Category A', 'Category B'],
};

describe('GrammarPage - src/app/grammar/page.tsx', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (firestore.getAllGrammarPoints as jest.Mock).mockResolvedValue(mockGrammarPoints);
    (firestore.getMetadata as jest.Mock).mockResolvedValue(mockMetadata);
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams()); // Reset for each test
  });

  it('renders TextWithTTS for grammar point titles, explanations, and examples in the list', async () => {
    render(await GrammarPage());

    await waitFor(() => {
      // Check GP1 Title
      expect(screen.getByTestId('text-with-tts-grammar-point-1-title')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-grammar-point-1-title')).toHaveTextContent('Grammar Point 1 Title');
      expect(screen.getByTestId('text-with-tts-grammar-point-1-title')).toHaveAttribute('data-lang', 'zh');

      // Check GP1 Explanation (assuming it's part of the displayed card, might need adjustment based on actual component structure)
      // For this test, we'll assume the explanation is rendered with a unique ID or a more specific test ID if available.
      // If the page component renders explanations directly with TextWithTTS:
      expect(screen.getByTestId('text-with-tts-explanation-for-gp1.')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-explanation-for-gp1.')).toHaveTextContent('Explanation for GP1.');
      expect(screen.getByTestId('text-with-tts-explanation-for-gp1.')).toHaveAttribute('data-lang', 'en');


      // Check GP1 Example Chinese
      expect(screen.getByTestId('text-with-tts-句子一')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-句子一')).toHaveTextContent('句子一');
      expect(screen.getByTestId('text-with-tts-句子一')).toHaveAttribute('data-lang', 'zh');

      // Check GP1 Example Pinyin
      expect(screen.getByTestId('text-with-tts-jùzi-yī')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-jùzi-yī')).toHaveTextContent('Jùzi yī');
      expect(screen.getByTestId('text-with-tts-jùzi-yī')).toHaveAttribute('data-lang', 'pinyin');


      // Check GP2 Title
      expect(screen.getByTestId('text-with-tts-grammar-point-2-title')).toBeInTheDocument();
      // ... and so on for GP2
    });
  });

  it('calls speak function when TTS button for a title is clicked', async () => {
    render(await GrammarPage());
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-grammar-point-1-title');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith('Grammar Point 1 Title', 'zh');
    });
  });

  it('calls speak function when TTS button for an example (Chinese) is clicked', async () => {
    render(await GrammarPage());
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-句子一');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith('句子一', 'zh');
    });
  });

  it('calls speak function when TTS button for an example (Pinyin) is clicked', async () => {
    render(await GrammarPage());
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-jùzi-yī');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith('Jùzi yī', 'pinyin');
    });
  });

  it('filters grammar points based on searchParams', async () => {
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams("level=HSK1"));
    // Re-render or ensure the component re-fetches/re-filters based on new searchParams.
    // For server components, this means passing different searchParams.
    // The mock for getAllGrammarPoints might need to be more sophisticated if filtering happens client-side after fetch.
    // However, if filtering is done by getAllGrammarPoints itself based on params, that's what we test.

    // For this example, let's assume getAllGrammarPoints is called with filter params
    // and returns only matching items.
    (firestore.getAllGrammarPoints as jest.Mock).mockResolvedValue([mockGrammarPoints[0]]); // Only HSK1 item

    render(await GrammarPage());

    await waitFor(() => {
      expect(screen.getByTestId('text-with-tts-grammar-point-1-title')).toBeInTheDocument();
      expect(screen.queryByTestId('text-with-tts-grammar-point-2-title')).not.toBeInTheDocument();
    });
     // Reset for other tests
    (firestore.getAllGrammarPoints as jest.Mock).mockResolvedValue(mockGrammarPoints);
  });

});