"use client"
import { MatchingGame } from '@/components/games/vocabulary/MatchingGame';
import React from 'react';
import { useSearchParams } from 'next/navigation';

const MatchingGamePage: React.FC = () => {
  const searchParams = useSearchParams();
  const category = searchParams.get('category');
  const tag = searchParams.get('tag');

  return (
    <div className="container mx-auto py-8">
      <MatchingGame category={category || undefined} tag={tag || undefined} />
    </div>
  );
};

export default MatchingGamePage;
