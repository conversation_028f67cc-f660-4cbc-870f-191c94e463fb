'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation'; // Removed useRouter as it's unused
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { AdaptiveVocabularyGame } from '@/components/games/vocabulary/AdaptiveVocabularyGame';
import { courseData } from '@/lib/course-data'; // Import courseData
import { VocabularyItem } from '@/types/content';
import { useVocabularyProgress } from '@/hooks/useVocabularyProgress'; // Import useVocabularyProgress
import { pinyinGameLogic } from '@/features/adaptiveVocabularyGame/gameTypes/pinyinGameLogic'; // Import the specific game logic

export default function PinyinToCharacterGame() {
  const searchParams = useSearchParams();
  const category = searchParams.get('category');
  const tag = searchParams.get('tag');

  const [vocabulary, setVocabulary] = useState<VocabularyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Use the new method to fetch vocabulary by category and tag
        const items = await courseData.getVocabularyItemsByCategoryAndTag(category, tag);
        setVocabulary(items);
      } catch (err) {
        console.error('Error fetching vocabulary:', err);
        setError('Failed to load vocabulary items.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [category, tag]);

  const { progress: vocabularyProgressMap, updateItemProgress, getPrioritizedVocabulary, isLoading: loadingProgress } = useVocabularyProgress(vocabulary);

  if (loading || loadingProgress) {
    return <div className="container mx-auto px-4 py-8">Loading vocabulary...</div>;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-red-600">{error}</div>
        <Link href={`/vocabulary/games?category=${category}`} className="text-blue-600 hover:text-blue-800">
          Return to Game Selection
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={`/vocabulary/games?category=${category}`} passHref>
          <Button variant="outline">← Back to Game Selection</Button>
        </Link>
      </div>
      
      <AdaptiveVocabularyGame
        vocabulary={vocabulary}
        gameLogic={pinyinGameLogic} // Pass the specific game logic object
        vocabularyProgressMap={vocabularyProgressMap}
        updateVocabularyItemProgress={updateItemProgress}
        getPrioritizedVocabulary={getPrioritizedVocabulary}
      />
    </div>
  );
}
