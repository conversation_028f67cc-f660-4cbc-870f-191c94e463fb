'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { StoryAdventureGame } from '@/components/games/vocabulary/StoryAdventureGame';
import { courseData } from '@/lib/course-data'; // Import courseData
import { VocabularyItem } from '@/types/content';

export default function StoryAdventurePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const category = searchParams.get('category');
  const tag = searchParams.get('tag');

  const [vocabulary, setVocabulary] = useState<VocabularyItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Use the new method to fetch vocabulary by category and tag
        const items = await courseData.getVocabularyItemsByCategoryAndTag(category, tag);
        setVocabulary(items);
      } catch (err) {
        console.error('Error fetching vocabulary:', err);
        setError('Failed to load vocabulary items.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [category, tag]);

  if (loading) {
    return <div className="container mx-auto px-4 py-8">Loading vocabulary...</div>;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-red-600">{error}</div>
        <Link href={`/vocabulary/games?category=${category}`} className="text-blue-600 hover:text-blue-800">
          Return to Game Selection
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={`/vocabulary/games?category=${category}`} passHref>
          <Button variant="outline">← Back to Game Selection</Button>
        </Link>
      </div>
      
      <StoryAdventureGame
        vocabulary={vocabulary}
        category={category || '综合'}
        onExitGame={() => router.push(`/vocabulary/games?category=${category}`)}
      />
    </div>
  );
}
