'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function GamesPage() {
  const searchParams = useSearchParams();
  const category = searchParams.get('category');
  const tag = searchParams.get('tag');

  // Construct query string for game links
  const queryString = `${category ? `category=${category}` : ''}${tag ? `&tag=${tag}` : ''}`;
  const getGamePath = (gamePath: string) => `/vocabulary/games/${gamePath}${queryString ? `?${queryString}` : ''}`;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <h1 className="text-4xl font-bold text-gray-800">Vocabulary Games</h1>
        <Link href="/vocabulary" className="text-blue-600 hover:text-blue-800 transition-colors">
          ← Back to Vocabulary List
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link href={getGamePath('character-to-meaning')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Character to Meaning</CardTitle>
              <CardDescription>
                Practice recognizing Chinese characters and selecting their meanings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Test your ability to match Chinese characters with their English definitions</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={getGamePath('pinyin-to-character')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Pinyin to Character</CardTitle>
              <CardDescription>
                Practice matching pinyin pronunciation with correct characters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Test your ability to identify characters based on their pronunciation</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={getGamePath('english-to-character')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>English to Character</CardTitle>
              <CardDescription>
                Read an English definition and select the correct Chinese character(s).
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Test your recall of characters based on their English meaning.</p>
            </CardContent>
          </Card>
        </Link>
        <Link href={getGamePath('matching-game')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Vocabulary Matching Game</CardTitle>
              <CardDescription>
                Match Chinese characters to their meanings or Pinyin within a time limit.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Challenge your speed and accuracy in pairing vocabulary items.</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={getGamePath('writing-practice')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Writing Practice</CardTitle>
              <CardDescription>
                Practice writing in Chinese with AI assistance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Write about given topics using vocabulary with AI suggestions and speech input</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={getGamePath('story-adventure')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Story Adventure</CardTitle>
              <CardDescription>
                Embark on an interactive story where your vocabulary choices shape the narrative.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Use your learned vocabulary to make decisions and progress through an engaging story.</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={getGamePath('translation-chat')}>
          <Card className="cursor-pointer hover:shadow-lg transition-shadow h-full">
            <CardHeader>
              <CardTitle>Translation Chat</CardTitle>
              <CardDescription>
                Practice translating Chinese sentences with AI feedback and adaptive difficulty.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Translate sentences, get instant feedback, and track your vocabulary and grammar progress.</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
