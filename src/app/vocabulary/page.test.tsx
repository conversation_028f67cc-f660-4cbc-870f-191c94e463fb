import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VocabularyPage from './page';
import * as firestore from '@/lib/firestore';
import * as nextNavigation from 'next/navigation';

// Mock TextWithTTS component
const mockSpeak = jest.fn();
jest.mock('@/components/ui/TextWithTTS', () => {
  const MockTextWithTTS = ({ text, lang, id }: { text: string | string[]; lang: string; id?: string }) => {
    const textContent = Array.isArray(text) ? text.join(', ') : text;
    const testIdSuffix = (Array.isArray(text) ? text.join('-') : text).toString().replace(/\s+/g, '-').toLowerCase();
    return (
      <div data-testid={id || `text-with-tts-${testIdSuffix}`} data-lang={lang}>
        {textContent}
        <button data-testid={`tts-button-${testIdSuffix}`} onClick={() => mockSpeak(text, lang)}>
          Speak
        </button>
      </div>
    );
  };
  return MockTextWithTTS;
});

// Mock useTextToSpeech hook
jest.mock('@/hooks/useTextToSpeech', () => ({
  __esModule: true,
  default: () => ({
    speak: mockSpeak,
    isPlaying: false,
    isAvailable: true,
    error: null,
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams()), // Default mock
  useRouter: jest.fn(() => ({ push: jest.fn() })),
  usePathname: jest.fn(() => '/vocabulary'),
}));

// Mock firestore
jest.mock('@/lib/firestore', () => ({
  getAllVocabularyItems: jest.fn(),
  getVocabularyItemsByCategoryAndChapter: jest.fn(),
  getMetadata: jest.fn(),
}));

const mockVocabularyItems = [
  {
    id: 'vocab1',
    word: '你好',
    pinyin: 'Nǐ hǎo',
    definitions: ['Hello', 'Hi'],
    part_of_speech: ['greeting'],
    level: 'HSK1',
    examples: [{ sentence_chinese: '你好世界', sentence_pinyin: 'Nǐ hǎo shìjiè', translation: 'Hello World' }],
    slug: 'ni-hao',
    book: 'Book 1',
    lesson: 'Lesson 1',
    category: 'Greetings',
    tags: ['greeting', 'common'],
    created_at: new Date(),
    updated_at: new Date(),
    progress: { leitner_box: 0, next_review_date: new Date(), last_reviewed_date: new Date(), ease_factor: 2.5, interval: 0, repetitions: 0, correct_in_a_row: 0 }
  },
  {
    id: 'vocab2',
    word: '谢谢',
    pinyin: 'Xièxie',
    definitions: ['Thanks', 'Thank you'],
    part_of_speech: ['expression'],
    level: 'HSK1',
    examples: [{ sentence_chinese: '谢谢你', sentence_pinyin: 'Xièxie nǐ', translation: 'Thank you' }],
    slug: 'xiexie',
    book: 'Book 1',
    lesson: 'Lesson 1',
    category: 'Politeness',
    tags: ['politeness', 'common'],
    created_at: new Date(),
    updated_at: new Date(),
    progress: { leitner_box: 0, next_review_date: new Date(), last_reviewed_date: new Date(), ease_factor: 2.5, interval: 0, repetitions: 0, correct_in_a_row: 0 }
  },
];

const mockMetadata = {
  levels: ['HSK1'],
  books: ['Book 1'],
  categories: ['Greetings', 'Politeness'],
  refined_tag_structure: {
    topical_tags: { common: {}, greeting: {}, politeness: {} },
    grammatical_tags: {},
  }
};

describe('VocabularyPage - src/app/vocabulary/page.tsx', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (firestore.getAllVocabularyItems as jest.Mock).mockResolvedValue(mockVocabularyItems);
    (firestore.getVocabularyItemsByCategoryAndChapter as jest.Mock).mockResolvedValue(mockVocabularyItems);
    (firestore.getMetadata as jest.Mock).mockResolvedValue(mockMetadata);
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams()); // Reset for each test
  });

  it('renders TextWithTTS for vocabulary word, pinyin, and definitions in the list', async () => {
    render(<VocabularyPage />);

    await waitFor(() => {
      // Check Vocab1 Word
      expect(screen.getByTestId('text-with-tts-你好')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-你好')).toHaveTextContent('你好');
      expect(screen.getByTestId('text-with-tts-你好')).toHaveAttribute('data-lang', 'zh');

      // Check Vocab1 Pinyin
      expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toHaveTextContent('Nǐ hǎo');
      expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toHaveAttribute('data-lang', 'pinyin');

      // Check Vocab1 Definitions (mock joins with ", ")
      expect(screen.getByTestId('text-with-tts-hello-hi')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-hello-hi')).toHaveTextContent('Hello, Hi');
      expect(screen.getByTestId('text-with-tts-hello-hi')).toHaveAttribute('data-lang', 'en');

      // Check Vocab2 Word
      expect(screen.getByTestId('text-with-tts-谢谢')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-谢谢')).toHaveTextContent('谢谢');
      expect(screen.getByTestId('text-with-tts-谢谢')).toHaveAttribute('data-lang', 'zh');
    });
  });

  it('calls speak function when TTS button for a word is clicked', async () => {
    render(<VocabularyPage />);
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-你好');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith('你好', 'zh');
    });
  });

  it('calls speak function when TTS button for pinyin is clicked', async () => {
    render(<VocabularyPage />);
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-nǐ-hǎo');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith('Nǐ hǎo', 'pinyin');
    });
  });

  it('calls speak function when TTS button for definitions is clicked', async () => {
    render(<VocabularyPage />);
    await waitFor(() => {
      const ttsButton = screen.getByTestId('tts-button-hello-hi');
      fireEvent.click(ttsButton);
      expect(mockSpeak).toHaveBeenCalledWith(['Hello', 'Hi'], 'en');
    });
  });

  it('filters vocabulary items based on searchParams (category)', async () => {
    const params = new URLSearchParams("category=Greetings");
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(params);
    (firestore.getVocabularyItemsByCategoryAndChapter as jest.Mock).mockResolvedValue([mockVocabularyItems[0]]);

    render(<VocabularyPage />);

    await waitFor(() => {
      expect(firestore.getVocabularyItemsByCategoryAndChapter).toHaveBeenCalledWith('Greetings', 'all');
      expect(screen.getByTestId('text-with-tts-你好')).toBeInTheDocument();
      expect(screen.queryByTestId('text-with-tts-谢谢')).not.toBeInTheDocument();
    });
     // Reset for other tests
    (firestore.getAllVocabularyItems as jest.Mock).mockResolvedValue(mockVocabularyItems);
  });

  it('filters vocabulary items based on selected tag', async () => {
    render(<VocabularyPage />);

    await waitFor(() => {
      // Ensure "common" tag button is rendered (assuming it's part of topical_tags)
      expect(screen.getByText('common')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('common')); // Click the 'common' tag button

    await waitFor(() => {
      // Both items have 'common' tag, so both should be visible
      expect(screen.getByTestId('text-with-tts-你好')).toBeInTheDocument();
      expect(screen.getByTestId('text-with-tts-谢谢')).toBeInTheDocument();
    });

    // Mock metadata to have a unique tag for one item
     const specificMockMetadata = {
      ...mockMetadata,
      refined_tag_structure: {
        topical_tags: { ...mockMetadata.refined_tag_structure.topical_tags, unique_greeting: {} },
        grammatical_tags: {},
      }
    };
    (firestore.getMetadata as jest.Mock).mockResolvedValue(specificMockMetadata);
    const itemsWithSpecificTag = [
      { ...mockVocabularyItems[0], tags: ['greeting', 'common', 'unique_greeting'] },
      mockVocabularyItems[1]
    ];
    (firestore.getAllVocabularyItems as jest.Mock).mockResolvedValue(itemsWithSpecificTag);


    // Re-render with new data or simulate state update if tags are fetched dynamically
    // For simplicity, we assume the component re-evaluates filters upon tag click
    // Need to ensure the 'unique_greeting' button is available and clicked
    // This part might need adjustment based on how tags are displayed and selected in the UI

    // Let's find the button for 'unique_greeting' if it's rendered
    // This requires the tag filtering UI to be fully mocked or rendered
    // For now, let's assume a button with text 'unique_greeting' exists after metadata update
    // This part of the test is more complex due to the dynamic nature of tag filters.
    // A more robust test would involve interacting with the actual filter UI elements.

    // Simulate clicking a more specific tag that only one item has
    // This requires the component to re-render or update its state based on the new tag selection.
    // We'll manually filter here to assert the logic, actual UI interaction is more complex.
    
    // Click on a tag that only the first item has (e.g., 'greeting' if 'politeness' is for the second)
    // First, ensure the 'greeting' tag button is rendered
    fireEvent.click(screen.getByText('greeting'));

    await waitFor(() => {
      expect(screen.getByTestId('text-with-tts-你好')).toBeInTheDocument();
      expect(screen.queryByTestId('text-with-tts-谢谢')).not.toBeInTheDocument();
    });
  });
});