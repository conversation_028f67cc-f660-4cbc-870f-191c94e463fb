import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import VocabularyItemPage from './page';
import * as firestore from '@/lib/firestore';
import * as nextNavigation from 'next/navigation';

// Mock TextWithTTS component
const mockSpeak = jest.fn();
jest.mock('@/components/ui/TextWithTTS', () => {
  const MockTextWithTTS = ({ text, lang }: { text: string; lang: string }) => (
    <div data-testid={`text-with-tts-${text.toString().replace(/\s+/g, '-').toLowerCase()}`} data-lang={lang}>
      {Array.isArray(text) ? text.join(', ') : text}
      <button data-testid={`tts-button-${text.toString().replace(/\s+/g, '-').toLowerCase()}`} onClick={() => mockSpeak(text, lang)}>
        Speak
      </button>
    </div>
  );
  return MockTextWithTTS;
});

// Mock useTextToSpeech hook (though TextWithTTS mock might be sufficient)
jest.mock('@/hooks/useTextToSpeech', () => ({
  __esModule: true,
  default: () => ({
    speak: mockSpeak,
    isPlaying: false,
    isAvailable: true,
    error: null,
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
  notFound: jest.fn(),
  useRouter: jest.fn(() => ({ push: jest.fn() })),
  usePathname: jest.fn(() => '/vocabulary/some-id'),
}));

// Mock firestore
jest.mock('@/lib/firestore', () => ({
  getVocabularyItemById: jest.fn(),
}));

const mockVocabularyItem = {
  id: 'test-vocab-id',
  word: '你好',
  pinyin: 'Nǐ hǎo',
  definitions: ['Hello', 'Hi'],
  part_of_speech: ['greeting'],
  level: 'HSK1',
  examples: [
    { sentence_chinese: '你好，世界', sentence_pinyin: 'Nǐ hǎo, shìjiè', translation: 'Hello, world' },
    { sentence_chinese: '老师你好', sentence_pinyin: 'Lǎoshī nǐ hǎo', translation: 'Hello, teacher' },
  ],
  related_vocabulary: [],
  related_grammar_points: [],
  notes: '',
  tags: [],
  slug: 'ni-hao',
  book: 'Standard Course HSK 1',
  lesson: 'Lesson 1',
  category: 'Greetings',
  created_at: new Date(),
  updated_at: new Date(),
  progress: {
    leitner_box: 0,
    next_review_date: new Date(),
    last_reviewed_date: new Date(),
    ease_factor: 2.5,
    interval: 0,
    repetitions: 0,
    correct_in_a_row: 0,
  }
};

describe('VocabularyItemPage - src/app/vocabulary/[itemId]/page.tsx', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (firestore.getVocabularyItemById as jest.Mock).mockResolvedValue(mockVocabularyItem);
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
  });

  it('renders TextWithTTS for word, pinyin, definitions, and examples', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));

    // Check word
    expect(screen.getByTestId('text-with-tts-你好')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-你好')).toHaveTextContent('你好');
    expect(screen.getByTestId('text-with-tts-你好')).toHaveAttribute('data-lang', 'zh');

    // Check pinyin
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toHaveTextContent('Nǐ hǎo');
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo')).toHaveAttribute('data-lang', 'pinyin');

    // Check definitions (assuming TextWithTTS handles array by joining or similar)
    // The mock joins with ", ", so the testid becomes "hello,-hi"
    expect(screen.getByTestId('text-with-tts-hello,-hi')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-hello,-hi')).toHaveTextContent('Hello, Hi');
    expect(screen.getByTestId('text-with-tts-hello,-hi')).toHaveAttribute('data-lang', 'en');


    // Check examples - Chinese
    expect(screen.getByTestId('text-with-tts-你好，世界')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-你好，世界')).toHaveTextContent('你好，世界');
    expect(screen.getByTestId('text-with-tts-你好，世界')).toHaveAttribute('data-lang', 'zh');

    expect(screen.getByTestId('text-with-tts-老师你好')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-老师你好')).toHaveTextContent('老师你好');
    expect(screen.getByTestId('text-with-tts-老师你好')).toHaveAttribute('data-lang', 'zh');

    // Check examples - Pinyin
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo,-shìjiè')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo,-shìjiè')).toHaveTextContent('Nǐ hǎo, shìjiè');
    expect(screen.getByTestId('text-with-tts-nǐ-hǎo,-shìjiè')).toHaveAttribute('data-lang', 'pinyin');

    expect(screen.getByTestId('text-with-tts-lǎoshī-nǐ-hǎo')).toBeInTheDocument();
    expect(screen.getByTestId('text-with-tts-lǎoshī-nǐ-hǎo')).toHaveTextContent('Lǎoshī nǐ hǎo');
    expect(screen.getByTestId('text-with-tts-lǎoshī-nǐ-hǎo')).toHaveAttribute('data-lang', 'pinyin');
  });

  it('calls speak function when TTS button is clicked for word', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));
    const ttsButton = screen.getByTestId('tts-button-你好');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('你好', 'zh');
  });

  it('calls speak function when TTS button is clicked for pinyin', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));
    const ttsButton = screen.getByTestId('tts-button-nǐ-hǎo');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('Nǐ hǎo', 'pinyin');
  });

  it('calls speak function when TTS button is clicked for definitions', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));
    const ttsButton = screen.getByTestId('tts-button-hello,-hi');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith(['Hello', 'Hi'], 'en');
  });

  it('calls speak function when TTS button is clicked for an example sentence (Chinese)', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));
    const ttsButton = screen.getByTestId('tts-button-你好，世界');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('你好，世界', 'zh');
  });

  it('calls speak function when TTS button is clicked for an example sentence (Pinyin)', async () => {
    render(await VocabularyItemPage({ params: { itemId: 'test-vocab-id' } }));
    const ttsButton = screen.getByTestId('tts-button-nǐ-hǎo,-shìjiè');
    fireEvent.click(ttsButton);
    expect(mockSpeak).toHaveBeenCalledWith('Nǐ hǎo, shìjiè', 'pinyin');
  });

  it('calls notFound if vocabulary item is not found', async () => {
    (firestore.getVocabularyItemById as jest.Mock).mockResolvedValue(null);
    await VocabularyItemPage({ params: { itemId: 'non-existent-id' } });
    expect(nextNavigation.notFound).toHaveBeenCalled();
  });
});