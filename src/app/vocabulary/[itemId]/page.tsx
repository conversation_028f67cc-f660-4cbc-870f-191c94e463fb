import { getVocabularyItemById } from '@/lib/firestore';
import { VocabularyItem, SerializableVocabularyItem } from '@/types/content';
import { notFound } from 'next/navigation';
import TextWithTTS from '@/components/ui/TextWithTTS';
import VocabularyPracticeSection from '@/components/vocabulary/VocabularyPracticeSection';

interface VocabularyItemPageProps {
  params: {
    itemId: string;
  };
}

export default async function VocabularyItemPage({ params }: VocabularyItemPageProps) {
  const { itemId } = await params;
  const item = await getVocabularyItemById(itemId);

  if (!item) {
    notFound();
  }

  // Convert the item to a plain object to pass to client components
  const plainItem = {
    ...item,
    createdAt: item.createdAt ? item.createdAt.toDate().toISOString() : null,
    definitions: item.definitions || [], // Ensure definitions is an array
    examples: item.examples || [],       // Ensure examples is an array
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-4">
        <TextWithTTS text={plainItem.word} /> (<span> {plainItem.pinyin} </span>)
      </h1>
      <p className="text-lg text-gray-600 mb-4">Part of Speech: {plainItem.partOfSpeech}</p>

      {plainItem.category && <p className="text-sm text-gray-500">Category: {plainItem.category}</p>}
      {plainItem.chapter && <p className="text-sm text-gray-500">Chapter: {plainItem.chapter}</p>}
      {plainItem.tags && plainItem.tags.length > 0 && (
        <p className="text-sm text-gray-500">Tags: {plainItem.tags.join(', ')}</p>
      )}

      <div className="mt-6">
        <h2 className="text-2xl font-semibold mb-3">Definitions</h2>
        <ul className="list-disc list-inside">
          {plainItem.definitions.map((def, index) => (
            <li key={index}><TextWithTTS text={def} /></li>
          ))}
        </ul>
      </div>

      {plainItem.examples && plainItem.examples.length > 0 && (
        <div className="mt-6">
          <h2 className="text-2xl font-semibold mb-3">Examples</h2>
          {plainItem.examples.map((example, index) => (
            <div key={index} className="mb-4 p-4 border rounded">
              <div><strong>Chinese:</strong> <TextWithTTS text={example.chinese} /></div>
              <div><strong>Pinyin:</strong> <span>{example.pinyin} </span></div>
              <div><strong>English:</strong> {example.english}</div>
            </div>
          ))}
        </div>
      )}

      <VocabularyPracticeSection vocabularyItem={plainItem} />
    </div>
  );
}
