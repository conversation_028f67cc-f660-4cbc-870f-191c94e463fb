'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getAllVocabularyItems, getVocabularyItemsByCategoryAndChapter, getMetadata } from '@/lib/firestore';
import { VocabularyItem, CategoryStructureMetadata as Metadata, TagStructure } from '@/types/content';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import TextWithTTS from '@/components/ui/TextWithTTS';
import Link from 'next/link';

const VocabularyPage = () => {
  const searchParams = useSearchParams();
  const categoryFromParams = searchParams.get('category'); // Renamed to avoid conflict

  const [vocabulary, setVocabulary] = useState<VocabularyItem[]>([]);
  const [metadata, setMetadata] = useState<Metadata | undefined>(undefined);
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [expandedCategory, setExpandedCategory] = useState<'topical' | 'grammatical' | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [items, meta] = await Promise.all([
          categoryFromParams ? getVocabularyItemsByCategoryAndChapter(categoryFromParams, 'all') : getAllVocabularyItems(),
          getMetadata()
        ]);
        setVocabulary(items);
        setMetadata(meta);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryFromParams]);

  // Filter vocabulary based on selected tag
  const filteredVocabulary = selectedTag === 'all'
    ? vocabulary
    : vocabulary.filter(item => item.tags?.includes(selectedTag)); // Filter by tags

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div style={{ color: 'red' }}>{error}</div>;
  }

  // Extract unique tags from metadata for the filter dropdown
  const extractTags = (tagStructure: TagStructure, tags: Set<string>) => {
    for (const key in tagStructure) {
      if (key !== 'description' && key !== 'items') {
        tags.add(key);
        // Check if the value is an object and not null or an array before recursing
        if (typeof tagStructure[key] === 'object' && tagStructure[key] !== null && !Array.isArray(tagStructure[key])) {
          extractTags(tagStructure[key] as TagStructure, tags); // Cast after check
        }
      }
    }
  };

  // Organize tags into categories
  const tagCategories = {
    topical: new Set<string>(),
    grammatical: new Set<string>()
  };

  if (metadata?.refined_tag_structure) {
    extractTags(metadata.refined_tag_structure.topical_tags, tagCategories.topical);
    extractTags(metadata.refined_tag_structure.grammatical_tags, tagCategories.grammatical);
  }

  return (
    <AnimatePresence mode="wait">
      (
        <motion.div
          key="vocabulary-list"
          initial={{ opacity: 0, x: -100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 100 }}
          transition={{ duration: 0.3 }}
          className="container mx-auto px-4 py-8" // Added mx-auto and adjusted padding
        >
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4"> {/* Adjusted for responsiveness and spacing */}
            <h1 className="text-4xl font-bold text-gray-800">Vocabulary List</h1> {/* Increased text size and added color */}
            <Link href={`/vocabulary/games${categoryFromParams ? `?category=${categoryFromParams}` : ''}${selectedTag !== 'all' ? `${categoryFromParams ? '&' : '?'}tag=${selectedTag}` : ''}`}>
              <Button className="w-full sm:w-auto"> {/* Made button full width on small screens */}
                Practice with Games
              </Button>
            </Link>
          </div>

          {/* Tag Filter */}
          <div className="mb-8 space-y-4">
            <h2 className="text-2xl font-semibold text-gray-700">Filter by Tag:</h2>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  variant={selectedTag === 'all' ? 'default' : 'outline'}
                  onClick={() => setSelectedTag('all')}
                  size="sm"
                  className="min-w-[100px]"
                >
                  All Tags
                </Button>
                {selectedTag !== 'all' && (
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedTag('all')}
                    size="sm"
                  >
                    Reset Filter
                  </Button>
                )}
              </div>
              
              {/* Topical Tags */}
              <div className="border rounded-lg overflow-hidden">
                <Button
                  variant="ghost"
                  onClick={() => setExpandedCategory(expandedCategory === 'topical' ? null : 'topical')}
                  className="w-full justify-between px-4 py-2 hover:bg-gray-100"
                >
                  <span className="font-semibold">Topical Tags</span>
                  <span className="text-sm">{expandedCategory === 'topical' ? '▼' : '▶'}</span>
                </Button>
                {expandedCategory === 'topical' && (
                  <div className="p-4 bg-gray-50">
                    <div className="flex flex-wrap gap-2">
                      {Array.from(tagCategories.topical).sort().map(tag => (
                        <Button
                          key={tag}
                          variant={selectedTag === tag ? 'default' : 'outline'}
                          onClick={() => setSelectedTag(tag)}
                          size="sm"
                        >
                          {tag}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Grammatical Tags */}
              <div className="border rounded-lg overflow-hidden">
                <Button
                  variant="ghost"
                  onClick={() => setExpandedCategory(expandedCategory === 'grammatical' ? null : 'grammatical')}
                  className="w-full justify-between px-4 py-2 hover:bg-gray-100"
                >
                  <span className="font-semibold">Grammatical Tags</span>
                  <span className="text-sm">{expandedCategory === 'grammatical' ? '▼' : '▶'}</span>
                </Button>
                {expandedCategory === 'grammatical' && (
                  <div className="p-4 bg-gray-50">
                    <div className="flex flex-wrap gap-2">
                      {Array.from(tagCategories.grammatical).sort().map(tag => (
                        <Button
                          key={tag}
                          variant={selectedTag === tag ? 'default' : 'outline'}
                          onClick={() => setSelectedTag(tag)}
                          size="sm"
                        >
                          {tag}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {filteredVocabulary.length === 0 ? (
            <p className="text-gray-600">No vocabulary items found for the selected category.</p>
          ) : (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" // Increased gap
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.05,
                  },
                },
              }}
            >
              {filteredVocabulary.map((item) => (
                <motion.div
                  key={item.id}
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: { opacity: 1, y: 0 },
                  }}
                >
                  <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-0">
                      <a href={`/vocabulary/${item.id}`} className="block">
                        <h2 className="text-xl font-semibold text-blue-600"><TextWithTTS text={item.word} /> (<TextWithTTS text={item.pinyin} />)</h2>
                        <p className="mt-2 text-gray-700">Definitions:</p>
                        <ul className="list-disc ml-5 text-gray-600">
                          {item.definitions.map((def: string, index: number) => (
                            <li key={index}><TextWithTTS text={def} /></li>
                          ))}
                        </ul>
                      </a>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}
        </motion.div>
      )
    </AnimatePresence>
  );
};

export default VocabularyPage;