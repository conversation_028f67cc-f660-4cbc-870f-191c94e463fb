"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Header } from "@/components/header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { sendPasswordResetEmail } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { courseData } from "@/lib/course-data";

export default function SettingsPage() {
  const router = useRouter();
  const { currentUser, userProfile, updateApiKey } = useAuth();
  
  const [apiKey, setApiKey] = useState(userProfile?.apiKey || "");
  const [saving, setSaving] = useState(false);
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [syncing, setSyncing] = useState(false);

  if (!currentUser) {
    router.push("/login");
    return null;
  }

  const handleSaveApiKey = async () => {
    try {
      setSaving(true);
      await updateApiKey(apiKey);
      toast.success("API key saved successfully");
    } catch (error) {
      console.error("Error saving API key:", error);
      toast.error("Failed to save API key");
    } finally {
      setSaving(false);
    }
  };

  const handleResetPassword = async () => {
    try {
      if (!currentUser?.email) return;
      
      await sendPasswordResetEmail(auth, currentUser.email);
      setResetEmailSent(true);
      toast.success("Password reset email sent");
    } catch (error) {
      console.error("Error sending password reset email:", error);
      toast.error("Failed to send password reset email");
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 container py-10">
        <div className="flex flex-col space-y-8 max-w-3xl mx-auto">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Gemini API Key</CardTitle>
              <CardDescription>
                Your API key is required to generate exercises using Google&apos;s Gemini API
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your Gemini API key"
                  enableStt={true} 
                />
                <p className="text-sm text-muted-foreground">
                  You can get a Gemini API key from{" "}
                  <a
                    href="https://ai.google.dev/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    Google AI Studio
                  </a>
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveApiKey} disabled={saving}>
                {saving ? "Saving..." : "Save API Key"}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account information and security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Email</Label>
                <Input value={userProfile?.email} disabled />
              </div>
              <div className="space-y-2">
                <Label>Username</Label>
                <Input value={userProfile?.username} disabled />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">Change Password</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Change Password</DialogTitle>
                    <DialogDescription>
                      We&apos;ll send a password reset link to your email address.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <p>Email: {userProfile?.email}</p>
                    {resetEmailSent && (
                      <p className="mt-2 text-green-600 dark:text-green-400">
                        Password reset email sent! Check your inbox.
                      </p>
                    )}
                  </div>
                  <DialogFooter>
                    <Button onClick={handleResetPassword}>
                      Send Reset Link
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button variant="destructive" asChild>
                <a href="/delete-account">Delete Account</a>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Course Materials</CardTitle>
              <CardDescription>
                Sync your course materials with the server to ensure you have the latest content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button
                  onClick={async () => {
                    try {
                      setSyncing(true);
                      await courseData.syncCourseMaterials();
                      toast.success("Course materials synced successfully");
                    } catch (error) {
                      console.error("Error syncing course materials:", error);
                      const errorMessage = error instanceof Error ? error.message : "Failed to sync course materials";
                      toast.error(errorMessage);
                    } finally {
                      setSyncing(false);
                    }
                  }}
                  disabled={syncing}
                >
                  {syncing ? "Syncing..." : "Sync Course Materials"}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
              <CardDescription>
                Customize your learning experience
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Preferences settings will be available in a future update.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
