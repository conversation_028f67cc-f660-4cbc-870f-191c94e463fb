import { NextRequest, NextResponse } from 'next/server';
import { performanceTracker } from '@/lib/performance-tracker';

// POST /api/chat/performance - Save performance data for a session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.sessionId || !body.userId || !body.performanceData) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, userId, performanceData' },
        { status: 400 }
      );
    }

    await performanceTracker.saveSessionPerformance(
      body.sessionId,
      body.userId,
      body.performanceData
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving performance data:', error);
    return NextResponse.json(
      { error: 'Failed to save performance data' },
      { status: 500 }
    );
  }
}

// GET /api/chat/performance - Get performance data for a user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const sessionId = searchParams.get('sessionId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    if (sessionId) {
      // Get performance for specific session
      const sessionSummary = await performanceTracker.getSessionSummary(sessionId);
      return NextResponse.json(sessionSummary);
    } else {
      // Get adaptive learning profile
      const adaptiveProfile = await performanceTracker.getAdaptiveLearningProfile(userId);
      return NextResponse.json(adaptiveProfile);
    }
  } catch (error) {
    console.error('Error fetching performance data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch performance data' },
      { status: 500 }
    );
  }
}
