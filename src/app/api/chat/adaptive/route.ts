import { NextRequest, NextResponse } from 'next/server';
import { adaptiveLearningEngine } from '@/lib/adaptive-learning-engine';

// POST /api/chat/adaptive/dialogue-request - Generate adaptive dialogue request
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.userId || !body.baseTopic) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, baseTopic' },
        { status: 400 }
      );
    }

    const adaptiveRequest = await adaptiveLearningEngine.generateAdaptiveDialogueRequest(
      body.userId,
      body.baseTopic,
      body.userPreferences
    );

    return NextResponse.json(adaptiveRequest);
  } catch (error) {
    console.error('Error generating adaptive dialogue request:', error);
    return NextResponse.json(
      { error: 'Failed to generate adaptive dialogue request' },
      { status: 500 }
    );
  }
}

// GET /api/chat/adaptive/recommendations - Get topic recommendations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // For now, we'll use sample topics
    // In a real app, these would come from a database
    const sampleTopics = [
      {
        id: 'daily-routine',
        name: 'Daily Routine',
        description: 'Discuss daily activities, schedules, and habits',
        difficulty: 'beginner',
        category: 'Lifestyle',
        estimatedDuration: 15,
        keyVocabulary: ['起床', '吃饭', '工作', '睡觉', '时间'],
        culturalContext: 'Learn about typical Chinese daily schedules',
        popularity: 95
      },
      {
        id: 'food-ordering',
        name: 'Ordering Food',
        description: 'Practice ordering food at restaurants and cafes',
        difficulty: 'beginner',
        category: 'Food & Dining',
        estimatedDuration: 12,
        keyVocabulary: ['菜单', '点菜', '服务员', '买单', '好吃'],
        culturalContext: 'Chinese dining etiquette and common dishes',
        popularity: 88
      }
    ];

    const recommendations = await adaptiveLearningEngine.recommendTopics(userId, sampleTopics);
    return NextResponse.json(recommendations);
  } catch (error) {
    console.error('Error getting topic recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to get topic recommendations' },
      { status: 500 }
    );
  }
}
