import { NextRequest, NextResponse } from 'next/server';
import { localDB, STORES } from '@/lib/localdb';

// GET /api/chat/sessions - Get user's chat sessions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const allSessions = await localDB.getAll(STORES.CHAT_SESSIONS);
    const userSessions = allSessions.filter((session: any) => session.userId === userId);

    return NextResponse.json(userSessions);
  } catch (error) {
    console.error('Error fetching chat sessions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat sessions' },
      { status: 500 }
    );
  }
}

// POST /api/chat/sessions - Create a new chat session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.userId || !body.topic) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, topic' },
        { status: 400 }
      );
    }

    const sessionData = {
      id: body.id || `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: body.userId,
      topic: body.topic,
      difficulty: body.difficulty || 'beginner',
      createdAt: Date.now(),
      status: 'active',
      totalSegments: body.totalSegments || 0,
      completedSegments: 0,
      overallScore: 0,
      targetCharacters: body.targetCharacters || []
    };

    await localDB.put(STORES.CHAT_SESSIONS, sessionData);

    return NextResponse.json(sessionData);
  } catch (error) {
    console.error('Error creating chat session:', error);
    return NextResponse.json(
      { error: 'Failed to create chat session' },
      { status: 500 }
    );
  }
}
