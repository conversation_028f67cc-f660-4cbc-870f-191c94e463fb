import { NextResponse } from 'next/server';
import { generateExercise } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const params = await req.json();
    const exercise = await generateExercise(params);
    return NextResponse.json(exercise);
  } catch (error) {
    console.error('Error in generate-exercise API:', error);
    return NextResponse.json({ error: 'Failed to generate exercise' }, { status: 500 });
  }
}
