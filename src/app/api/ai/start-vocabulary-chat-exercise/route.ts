import { NextResponse } from 'next/server';
import { startVocabularyChatExercise } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { word, pinyin, definitions, examples } = await req.json();
    const result = await startVocabularyChatExercise(word, pinyin, definitions, examples);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in start-vocabulary-chat-exercise API:', error);
    return NextResponse.json({ error: 'Failed to start vocabulary chat exercise' }, { status: 500 });
  }
}
