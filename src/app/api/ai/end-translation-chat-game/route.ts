import { NextResponse } from 'next/server';
import { endTranslationChatGame } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { chatHistory, vocabulary, grammarPoints } = await req.json();
    const result = await endTranslationChatGame(chatHistory, vocabulary, grammarPoints);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in end-translation-chat-game API:', error);
    return NextResponse.json({ error: 'Failed to end translation chat game' }, { status: 500 });
  }
}
