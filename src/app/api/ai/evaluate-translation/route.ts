import { NextRequest, NextResponse } from 'next/server';
import { evaluateTranslation } from '@/lib/server/ai-service';

interface EvaluateTranslationRequest {
  englishText: string;
  userTranslation: string;
  correctTranslation: string;
  context?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: EvaluateTranslationRequest = await request.json();
    
    // Validate required fields
    if (!body.englishText || !body.userTranslation || !body.correctTranslation) {
      return NextResponse.json(
        { error: 'Missing required fields: englishText, userTranslation, correctTranslation' },
        { status: 400 }
      );
    }

    const evaluation = await evaluateTranslation(
      body.englishText,
      body.userTranslation,
      body.correctTranslation,
      body.context
    );
    
    return NextResponse.json(evaluation);
  } catch (error) {
    console.error('Error evaluating translation:', error);
    return NextResponse.json(
      { error: 'Failed to evaluate translation' },
      { status: 500 }
    );
  }
}
