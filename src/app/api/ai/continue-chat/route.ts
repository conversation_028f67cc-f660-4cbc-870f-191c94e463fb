import { NextResponse } from 'next/server';
import { continueChat } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { grammarPoint, chatHistory, userAnswer } = await req.json();
    const result = await continueChat(grammarPoint, chatHistory, userAnswer);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in continue-chat API:', error);
    return NextResponse.json({ error: 'Failed to continue chat' }, { status: 500 });
  }
}
