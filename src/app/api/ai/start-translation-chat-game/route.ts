import { NextResponse } from 'next/server';
import { startTranslationChatGame } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { category, vocabulary, grammarPoints } = await req.json();
    const result = await startTranslationChatGame(category, vocabulary, grammarPoints);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in start-translation-chat-game API:', error);
    return NextResponse.json({ error: 'Failed to start translation chat game' }, { status: 500 });
  }
}
