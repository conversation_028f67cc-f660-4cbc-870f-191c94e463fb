import { NextRequest, NextResponse } from 'next/server';
import { generateChatDialogue } from '@/lib/server/ai-service';
import { ChatDialogueRequest } from '@/types/chat';

export async function POST(request: NextRequest) {
  try {
    const body: ChatDialogueRequest = await request.json();
    
    // Validate required fields
    if (!body.topic || !body.difficulty) {
      return NextResponse.json(
        { error: 'Missing required fields: topic and difficulty' },
        { status: 400 }
      );
    }

    const dialogue = await generateChatDialogue(body);
    
    return NextResponse.json(dialogue);
  } catch (error) {
    console.error('Error generating chat dialogue:', error);
    return NextResponse.json(
      { error: 'Failed to generate dialogue' },
      { status: 500 }
    );
  }
}
