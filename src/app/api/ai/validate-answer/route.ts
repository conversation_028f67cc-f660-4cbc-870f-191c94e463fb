import { NextResponse } from 'next/server';
import { validateAnswer } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const params = await req.json();
    const result = await validateAnswer(params);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in validate-answer API:', error);
    return NextResponse.json({ error: 'Failed to validate answer' }, { status: 500 });
  }
}
