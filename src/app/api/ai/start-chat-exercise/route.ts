import { NextResponse } from 'next/server';
import { startChatExercise } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { grammarPoint } = await req.json();
    const result = await startChatExercise(grammarPoint);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in start-chat-exercise API:', error);
    return NextResponse.json({ error: 'Failed to start chat exercise' }, { status: 500 });
  }
}
