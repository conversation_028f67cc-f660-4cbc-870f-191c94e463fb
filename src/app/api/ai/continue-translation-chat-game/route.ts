import { NextResponse } from 'next/server';
import { continueTranslationChatGame } from '@/lib/server/ai-service';

export async function POST(req: Request) {
  try {
    const { category, vocabulary, grammarPoints, chatHistory, userAnswer, previousChineseSentence } = await req.json();
    const result = await continueTranslationChatGame(category, vocabulary, grammarPoints, chatHistory, userAnswer, previousChineseSentence);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in continue-translation-chat-game API:', error);
    return NextResponse.json({ error: 'Failed to continue translation chat game' }, { status: 500 });
  }
}
