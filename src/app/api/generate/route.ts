import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateText } from "ai";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const { prompt, model, temperature, maxOutputTokens } = await req.json();

    // Get the API key from the Authorization header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7); // Remove "Bearer " prefix

    // Validate required parameters
    if (!prompt) {
      return NextResponse.json(
        { error: "Missing required parameter: prompt" },
        { status: 400 }
      );
    }

    // Initialize the Google Generative AI client
    const google = createGoogleGenerativeAI({ apiKey });

    // Get the specified model or default to gemini-pro
    const modelName = model ?? "gemma-3-27b-it";
    const aiModel = google(modelName);

    // Generate content using the AI SDK
    const result = await generateText({
      model: aiModel,
      prompt,
      temperature: temperature ?? 0.2,
      maxTokens: maxOutputTokens ?? 8192
    });

    // Get the response text
    const content = result.text;

    // Return the generated content
    return NextResponse.json({ content: JSON.parse(content) });
  } catch (error) {
    console.error("Error generating content:", error);

    // Return an appropriate error response
    return NextResponse.json(
      {
        error: error instanceof Error
          ? error.message
          : "An error occurred while generating content"
      },
      { status: 500 }
    );
  }
}
