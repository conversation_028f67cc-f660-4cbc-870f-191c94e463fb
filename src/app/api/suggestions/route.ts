import { NextResponse } from 'next/server';
import { VocabularyItem } from '@/types/content';

interface SuggestionRequest {
  text: string;
  vocabulary: Array<{
    word: string;
    pinyin: string;
    definitions: string[];
    category: string;
    partOfSpeech: string;
  }>;
}

interface LLMResponse {
  suggestions: Array<{
    word: string;
    context: string;
    relevance: number;
  }>;
}

export async function POST(request: Request) {
  try {
    const body: SuggestionRequest = await request.json();
    const { text, vocabulary } = body;

    if (!text || !vocabulary) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Analyze the text context
    const context = {
      topic: detectTopic(text),
      sentiment: analyzeSentiment(text),
      recentWords: extractRecentWords(text),
    };

    // Generate contextual suggestions
    const suggestions = generateContextualSuggestions(text, vocabulary, context);

    return NextResponse.json({ suggestions });
  } catch (error) {
    console.error('Error processing suggestion request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function detectTopic(text: string): string {
  // Simple topic detection based on keyword matching
  const topics = {
    family: ['家', '父母', '兄弟', '姐妹', '爸爸', '妈妈'],
    hobby: ['爱好', '运动', '音乐', '读书', '游戏'],
    school: ['学校', '老师', '同学', '课程', '学习'],
    weather: ['天气', '下雨', '晴天', '季节'],
    food: ['吃', '菜', '饭', '美食', '餐厅'],
  };

  let maxCount = 0;
  let detectedTopic = 'general';

  for (const [topic, keywords] of Object.entries(topics)) {
    const count = keywords.filter(keyword => text.includes(keyword)).length;
    if (count > maxCount) {
      maxCount = count;
      detectedTopic = topic;
    }
  }

  return detectedTopic;
}

function analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
  const positiveWords = ['喜欢', '开心', '好', '棒', '爱'];
  const negativeWords = ['不', '难', '坏', '讨厌', '累'];

  const positiveCount = positiveWords.filter(word => text.includes(word)).length;
  const negativeCount = negativeWords.filter(word => text.includes(word)).length;

  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

function extractRecentWords(text: string): string[] {
  return text.split(/\s+/).slice(-3);
}

function generateContextualSuggestions(
  text: string,
  vocabulary: SuggestionRequest['vocabulary'],
  context: {
    topic: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    recentWords: string[];
  }
): LLMResponse['suggestions'] {
  const suggestions: LLMResponse['suggestions'] = [];

  // Filter and score vocabulary based on context
  vocabulary.forEach(item => {
    let relevance = 0;
    let contextReason = '';

    // Don't suggest words already used
    if (text.includes(item.word)) return;

    // Topic relevance
    if (item.category.toLowerCase().includes(context.topic)) {
      relevance += 3;
      contextReason = `Relevant to current topic: ${context.topic}`;
    }

    // Sentiment matching
    const sentimentMatch =
      (context.sentiment === 'positive' && item.definitions.some(def => def.includes('good') || def.includes('happy'))) ||
      (context.sentiment === 'negative' && item.definitions.some(def => def.includes('bad') || def.includes('difficult')));
    if (sentimentMatch) {
      relevance += 2;
      contextReason += ' | Matches current sentiment';
    }

    // Grammar and part of speech relevance
    const lastWord = context.recentWords[context.recentWords.length - 1];
    if (lastWord) {
      if (
        (item.partOfSpeech === 'verb' && !text.endsWith('了')) ||
        (item.partOfSpeech === 'adjective' && text.includes('很'))
      ) {
        relevance += 2;
        contextReason += ` | Appropriate ${item.partOfSpeech} for current grammar`;
      }
    }

    // Add high-relevance suggestions
    if (relevance > 0) {
      suggestions.push({
        word: item.word,
        context: contextReason.trim(),
        relevance,
      });
    }
  });

  // Sort by relevance and return top suggestions
  return suggestions
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, 5);
}