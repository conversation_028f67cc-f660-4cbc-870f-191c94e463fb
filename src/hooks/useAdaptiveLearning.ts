'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  ChatDialogueRequest, 
  ChatTopic,
  AdaptiveLearningProfile 
} from '@/types/chat';
import { adaptiveLearningEngine } from '@/lib/adaptive-learning-engine';
import { usePerformanceTracking } from './usePerformanceTracking';

interface UseAdaptiveLearningOptions {
  userId: string;
  autoLoadRecommendations?: boolean;
}

interface LearningInsights {
  overallProgress: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  nextGoals: string[];
}

export const useAdaptiveLearning = (options: UseAdaptiveLearningOptions) => {
  const { userId, autoLoadRecommendations = true } = options;
  
  const [recommendedTopics, setRecommendedTopics] = useState<ChatTopic[]>([]);
  const [learningInsights, setLearningInsights] = useState<LearningInsights | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { adaptiveProfile } = usePerformanceTracking({ userId });

  // Generate adaptive dialogue request
  const generateAdaptiveRequest = useCallback(async (
    baseTopic: string,
    userPreferences?: Partial<ChatDialogueRequest>
  ): Promise<ChatDialogueRequest> => {
    try {
      setError(null);
      return await adaptiveLearningEngine.generateAdaptiveDialogueRequest(
        userId, 
        baseTopic, 
        userPreferences
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate adaptive request';
      setError(errorMessage);
      console.error('Failed to generate adaptive request:', err);
      
      // Return fallback request
      return {
        topic: baseTopic,
        difficulty: 'beginner',
        conversationLength: 8,
        speakers: 2,
        ...userPreferences
      };
    }
  }, [userId]);

  // Load topic recommendations
  const loadTopicRecommendations = useCallback(async (availableTopics: ChatTopic[]) => {
    if (!userId || !availableTopics.length) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const recommendations = await adaptiveLearningEngine.recommendTopics(userId, availableTopics);
      setRecommendedTopics(recommendations);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load topic recommendations';
      setError(errorMessage);
      console.error('Failed to load topic recommendations:', err);
      
      // Fallback to first few topics
      setRecommendedTopics(availableTopics.slice(0, 5));
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Load learning insights
  const loadLearningInsights = useCallback(async () => {
    if (!userId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const insights = await adaptiveLearningEngine.analyzeLearningProgress(userId);
      setLearningInsights(insights);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load learning insights';
      setError(errorMessage);
      console.error('Failed to load learning insights:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Get difficulty recommendation
  const getRecommendedDifficulty = useCallback((): 'beginner' | 'intermediate' | 'advanced' => {
    if (!adaptiveProfile) return 'beginner';
    
    const { averageScore, totalSessions, learningVelocity } = adaptiveProfile;
    
    if (averageScore >= 85 && totalSessions >= 10 && learningVelocity > 0.5) {
      return 'advanced';
    } else if (averageScore >= 70 && totalSessions >= 5) {
      return 'intermediate';
    } else {
      return 'beginner';
    }
  }, [adaptiveProfile]);

  // Get recommended session length
  const getRecommendedSessionLength = useCallback((): number => {
    if (!adaptiveProfile) return 8;
    
    const { averageScore, learningVelocity } = adaptiveProfile;
    
    if (averageScore < 60) {
      return 6; // Shorter for struggling learners
    } else if (averageScore >= 80 && learningVelocity > 0.3) {
      return 12; // Longer for advanced learners
    } else {
      return 8; // Standard length
    }
  }, [adaptiveProfile]);

  // Get focus characters for next session
  const getFocusCharacters = useCallback((limit: number = 5): string[] => {
    if (!adaptiveProfile) return [];
    
    return adaptiveProfile.problematicCharacters
      .filter(char => char.masteryScore < 80)
      .sort((a, b) => {
        // Prioritize by mistake frequency and recency
        const daysSinceA = (Date.now() - a.lastMistake.getTime()) / (1000 * 60 * 60 * 24);
        const daysSinceB = (Date.now() - b.lastMistake.getTime()) / (1000 * 60 * 60 * 24);
        const scoreA = a.mistakeCount * (1 / Math.max(1, daysSinceA));
        const scoreB = b.mistakeCount * (1 / Math.max(1, daysSinceB));
        return scoreB - scoreA;
      })
      .slice(0, limit)
      .map(char => char.character);
  }, [adaptiveProfile]);

  // Check if user should level up
  const shouldLevelUp = useCallback((): boolean => {
    if (!adaptiveProfile) return false;
    
    const currentLevel = adaptiveProfile.difficultyLevel;
    const recommendedLevel = getRecommendedDifficulty();
    
    return recommendedLevel !== currentLevel && 
           (currentLevel === 'beginner' && recommendedLevel === 'intermediate') ||
           (currentLevel === 'intermediate' && recommendedLevel === 'advanced');
  }, [adaptiveProfile, getRecommendedDifficulty]);

  // Get personalized encouragement message
  const getEncouragementMessage = useCallback((): string => {
    if (!adaptiveProfile || !learningInsights) {
      return "Welcome to Chinese conversation practice! Let's start your learning journey.";
    }

    const { averageScore, totalSessions } = adaptiveProfile;
    const { overallProgress } = learningInsights;

    if (overallProgress >= 80) {
      return "Excellent progress! You're becoming quite proficient in Chinese conversations.";
    } else if (overallProgress >= 60) {
      return "Great work! You're making solid progress. Keep up the momentum!";
    } else if (totalSessions >= 5) {
      return "You're building a good foundation. Consistency is key to improvement!";
    } else {
      return "Every expert was once a beginner. You're on the right path!";
    }
  }, [adaptiveProfile, learningInsights]);

  // Auto-load insights when profile changes
  useEffect(() => {
    if (autoLoadRecommendations && adaptiveProfile) {
      loadLearningInsights();
    }
  }, [autoLoadRecommendations, adaptiveProfile, loadLearningInsights]);

  return {
    // State
    recommendedTopics,
    learningInsights,
    isLoading,
    error,
    
    // Actions
    generateAdaptiveRequest,
    loadTopicRecommendations,
    loadLearningInsights,
    
    // Computed values
    recommendedDifficulty: getRecommendedDifficulty(),
    recommendedSessionLength: getRecommendedSessionLength(),
    focusCharacters: getFocusCharacters(),
    shouldLevelUp: shouldLevelUp(),
    encouragementMessage: getEncouragementMessage(),
    
    // Utilities
    getRecommendedDifficulty,
    getRecommendedSessionLength,
    getFocusCharacters,
    getEncouragementMessage
  };
};
