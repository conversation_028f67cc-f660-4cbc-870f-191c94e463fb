import { useState, useEffect, useCallback } from 'react';
import { VocabularyProgress, SuperMemoGrade } from '@/types/progress';
import { VocabularyItem } from '@/types/content';
import {
  initializeProgress,
  updateProgress,
  getPriorityScore
} from '@/lib/spaced-repetition';
import { localDB, STORES } from '@/lib/localdb';

export function useVocabularyProgress(vocabulary: VocabularyItem[]) {
  const [progress, setProgress] = useState<Record<string, VocabularyProgress>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadProgress = async () => {
      setIsLoading(true);
      try {
        const savedProgressArray = await localDB.getAll<VocabularyProgress>(STORES.VOCABULARY_PROGRESS);
        const savedProgressMap = savedProgressArray.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, VocabularyProgress>);

        // Initialize progress for new vocabulary items not yet in DB
        const initialProgress = vocabulary.reduce((acc, item) => {
          if (!savedProgressMap[item.id]) {
            acc[item.id] = initializeProgress(item.id);
          }
          return acc;
        }, {} as Record<string, VocabularyProgress>);

        const mergedProgress = { ...savedProgressMap, ...initialProgress };
        setProgress(mergedProgress);

        // Persist newly initialized items to IndexedDB
        for (const itemId in initialProgress) {
          await localDB.put(STORES.VOCABULARY_PROGRESS, initialProgress[itemId]);
        }

      } catch (error) {
        console.error("Failed to load vocabulary progress from IndexedDB:", error);
        // Fallback to initializing all if DB load fails
        const initialProgress = vocabulary.reduce((acc, item) => {
          acc[item.id] = initializeProgress(item.id);
          return acc;
        }, {} as Record<string, VocabularyProgress>);
        setProgress(initialProgress);
      } finally {
        setIsLoading(false);
      }
    };

    loadProgress();
  }, [vocabulary]);

  const updateItemProgress = useCallback(async (itemId: string, grade: SuperMemoGrade) => {
    setProgress(prev => {
      const itemProgress = prev[itemId] || initializeProgress(itemId);
      const updatedProgress = updateProgress(itemProgress, grade);
      // Asynchronously save to IndexedDB
      localDB.put(STORES.VOCABULARY_PROGRESS, updatedProgress)
        .catch(error => console.error("Failed to save vocabulary progress to IndexedDB:", error));
      return { ...prev, [itemId]: updatedProgress };
    });
  }, []);

  const getPrioritizedVocabulary = useCallback(() => {
    if (isLoading) return []; // Return empty or handle loading state appropriately
    return [...vocabulary].sort((a, b) => {
      const scoreA = getPriorityScore(progress[a.id] || initializeProgress(a.id));
      const scoreB = getPriorityScore(progress[b.id] || initializeProgress(b.id));
      return scoreB - scoreA;
    });
  }, [vocabulary, progress, isLoading]);

  return {
    progress,
    updateItemProgress,
    getPrioritizedVocabulary,
    isLoading,
  };
}