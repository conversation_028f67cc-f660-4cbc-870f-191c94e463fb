'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  AdaptiveLearningProfile, 
  ChatProgressSummary, 
  CharacterMistakeFrequency,
  ChatPerformanceData 
} from '@/types/chat';
import { performanceTracker } from '@/lib/performance-tracker';

interface UsePerformanceTrackingOptions {
  userId: string;
  autoLoad?: boolean;
}

export const usePerformanceTracking = (options: UsePerformanceTrackingOptions) => {
  const { userId, autoLoad = true } = options;
  
  const [adaptiveProfile, setAdaptiveProfile] = useState<AdaptiveLearningProfile | null>(null);
  const [sessionSummaries, setSessionSummaries] = useState<ChatProgressSummary[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load adaptive learning profile
  const loadAdaptiveProfile = useCallback(async () => {
    if (!userId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const profile = await performanceTracker.getAdaptiveLearningProfile(userId);
      setAdaptiveProfile(profile);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load adaptive profile';
      setError(errorMessage);
      console.error('Failed to load adaptive profile:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Load session summaries
  const loadSessionSummaries = useCallback(async () => {
    if (!userId) return;
    
    try {
      const sessions = await performanceTracker.getUserSessions(userId);
      const summaries = await Promise.all(
        sessions.map(session => performanceTracker.getSessionSummary(session.id))
      );
      setSessionSummaries(summaries.filter(Boolean) as ChatProgressSummary[]);
    } catch (err) {
      console.error('Failed to load session summaries:', err);
    }
  }, [userId]);

  // Save session performance
  const saveSessionPerformance = useCallback(async (
    sessionId: string,
    performanceData: ChatPerformanceData[]
  ) => {
    if (!userId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await performanceTracker.saveSessionPerformance(sessionId, userId, performanceData);
      
      // Reload data after saving
      await Promise.all([
        loadAdaptiveProfile(),
        loadSessionSummaries()
      ]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save performance data';
      setError(errorMessage);
      console.error('Failed to save session performance:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [userId, loadAdaptiveProfile, loadSessionSummaries]);

  // Get problematic characters for adaptive learning
  const getProblematicCharacters = useCallback((limit: number = 10) => {
    if (!adaptiveProfile) return [];
    
    return adaptiveProfile.problematicCharacters
      .sort((a, b) => {
        // Sort by mistake frequency and recency
        const aScore = a.mistakeCount * (1 / Math.max(1, (Date.now() - a.lastMistake.getTime()) / (1000 * 60 * 60 * 24)));
        const bScore = b.mistakeCount * (1 / Math.max(1, (Date.now() - b.lastMistake.getTime()) / (1000 * 60 * 60 * 24)));
        return bScore - aScore;
      })
      .slice(0, limit);
  }, [adaptiveProfile]);

  // Get learning insights
  const getLearningInsights = useCallback(() => {
    if (!adaptiveProfile || !sessionSummaries.length) return null;

    const recentSessions = sessionSummaries
      .sort((a, b) => b.sessionId.localeCompare(a.sessionId))
      .slice(0, 5);

    const averageRecentScore = recentSessions.length > 0
      ? recentSessions.reduce((sum, s) => sum + s.overallScore, 0) / recentSessions.length
      : 0;

    const improvingCharacters = adaptiveProfile.problematicCharacters
      .filter(c => c.improvementRate > 0.7)
      .length;

    const strugglingCharacters = adaptiveProfile.problematicCharacters
      .filter(c => c.masteryScore < 50)
      .length;

    return {
      totalSessions: adaptiveProfile.totalSessions,
      averageScore: adaptiveProfile.averageScore,
      recentAverageScore: averageRecentScore,
      improvingCharacters,
      strugglingCharacters,
      learningVelocity: adaptiveProfile.learningVelocity,
      difficultyLevel: adaptiveProfile.difficultyLevel,
      strongAreas: adaptiveProfile.problematicCharacters
        .filter(c => c.masteryScore >= 80)
        .map(c => c.character),
      weakAreas: adaptiveProfile.problematicCharacters
        .filter(c => c.masteryScore < 50)
        .map(c => c.character)
    };
  }, [adaptiveProfile, sessionSummaries]);

  // Get recommended focus characters
  const getRecommendedFocusCharacters = useCallback((count: number = 5) => {
    const problematic = getProblematicCharacters(count * 2);
    
    // Prioritize characters that:
    // 1. Have high mistake frequency
    // 2. Were encountered recently
    // 3. Have low mastery score
    // 4. Show declining trend
    
    return problematic
      .filter(c => c.masteryScore < 70) // Focus on characters not yet mastered
      .sort((a, b) => {
        const aRecency = Date.now() - a.lastMistake.getTime();
        const bRecency = Date.now() - b.lastMistake.getTime();
        const aScore = (a.mistakeCount * 0.4) + ((100 - a.masteryScore) * 0.4) + (aRecency < 7 * 24 * 60 * 60 * 1000 ? 0.2 : 0);
        const bScore = (b.mistakeCount * 0.4) + ((100 - b.masteryScore) * 0.4) + (bRecency < 7 * 24 * 60 * 60 * 1000 ? 0.2 : 0);
        return bScore - aScore;
      })
      .slice(0, count);
  }, [getProblematicCharacters]);

  // Auto-load data on mount
  useEffect(() => {
    if (autoLoad && userId) {
      Promise.all([
        loadAdaptiveProfile(),
        loadSessionSummaries()
      ]);
    }
  }, [autoLoad, userId, loadAdaptiveProfile, loadSessionSummaries]);

  return {
    // State
    adaptiveProfile,
    sessionSummaries,
    isLoading,
    error,
    
    // Actions
    loadAdaptiveProfile,
    loadSessionSummaries,
    saveSessionPerformance,
    
    // Computed data
    problematicCharacters: getProblematicCharacters(),
    learningInsights: getLearningInsights(),
    recommendedFocusCharacters: getRecommendedFocusCharacters(),
    
    // Utilities
    getProblematicCharacters,
    getLearningInsights,
    getRecommendedFocusCharacters
  };
};
