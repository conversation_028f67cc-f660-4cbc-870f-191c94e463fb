import { useCallback, useEffect, useState } from 'react';
import { VocabularyItem } from '@/types/content';

interface SuggestionResult {
  word: string;
  pinyin: string;
  definition: string;
  score: number;
  context?: string;
}

interface LLMResponse {
  suggestions: Array<{
    word: string;
    context: string;
    relevance: number;
  }>;
}

export function useSuggestionsLLM(vocabulary: VocabularyItem[]) {
  const [suggestions, setSuggestions] = useState<SuggestionResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const getLLMSuggestions = async (text: string): Promise<LLMResponse> => {
    // TODO: Replace with actual LLM API endpoint
    const response = await fetch('/api/suggestions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        vocabulary: vocabulary.map(item => ({
          word: item.word,
          pinyin: item.pinyin,
          definitions: item.definitions,
          category: item.category,
          partOfSpeech: item.partOfSpeech
        }))
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get LLM suggestions');
    }

    return response.json();
  };

  const analyzeSentence = useCallback(async (text: string) => {
    if (!text.trim()) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);

    try {
      // Get contextual suggestions from LLM
      const llmSuggestions = await getLLMSuggestions(text);

      // Map LLM suggestions to vocabulary items
      const enhancedSuggestions = llmSuggestions.suggestions
        .map(suggestion => {
          const vocabItem = vocabulary.find(item => item.word === suggestion.word);
          if (!vocabItem) return null;

          return {
            word: vocabItem.word,
            pinyin: vocabItem.pinyin,
            definition: vocabItem.definitions[0],
            score: suggestion.relevance,
            context: suggestion.context
          };
        })
        .filter((item): item is SuggestionResult => item !== null)
        .sort((a, b) => b.score - a.score)
        .slice(0, 5); // Increased number of suggestions

      setSuggestions(enhancedSuggestions);
    } catch (error) {
      console.error('Error getting LLM suggestions:', error);
      // Fallback to basic suggestions if LLM fails
      const basicSuggestions = getBasicSuggestions(text);
      setSuggestions(basicSuggestions);
    } finally {
      setIsLoading(false);
    }
  }, [vocabulary]);

  const getBasicSuggestions = (text: string): SuggestionResult[] => {
    const characters = text.split('');
    const lastChar = characters[characters.length - 1];
    const words = text.split(/\s+/);
    const lastWord = words[words.length - 1];

    return vocabulary
      .map(item => {
        let score = 0;

        if (text.includes(item.word)) return null;

        if (item.word.startsWith(lastChar)) score += 3;

        words.forEach(word => {
          if (item.definitions.some(def => def.toLowerCase().includes(word.toLowerCase()))) {
            score += 1;
          }
        });

        if (text.includes(item.category)) score += 2;

        if (lastWord && item.partOfSpeech === 'verb' && text.length > 3) score += 1;

        return score > 0
          ? {
              word: item.word,
              pinyin: item.pinyin,
              definition: item.definitions[0],
              score
            }
          : null;
      })
      .filter((item): item is SuggestionResult => item !== null)
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
  };

  return {
    suggestions,
    analyzeSentence,
    isLoading
  };
}