'use client';

import { useState, useEffect } from 'react';

// Simple mock auth hook for demo purposes
// In a real app, this would integrate with Firebase Auth or another auth provider
export const useAuth = () => {
  const [user, setUser] = useState<{ uid: string; email?: string } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate auth check
    const timer = setTimeout(() => {
      // For demo purposes, create a mock user
      setUser({
        uid: 'demo-user-' + Math.random().toString(36).substr(2, 9),
        email: '<EMAIL>'
      });
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!user
  };
};
