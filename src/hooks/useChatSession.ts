'use client';

import { useState, useCallback, useEffect } from 'react';
import {
  ChatSessionState,
  GeneratedDialogue,
  ChatPerformanceData,
  TranslationEvaluation,
  ChatDialogueRequest,
  DialogueSegmentData
} from '@/types/chat';
import { generateChatDialogue, evaluateUserTranslation } from '@/lib/ai-service';

interface UseChatSessionOptions {
  onSessionComplete?: (sessionId: string, performance: ChatPerformanceData[]) => void;
  onError?: (error: string) => void;
}

export const useChatSession = (options: UseChatSessionOptions = {}) => {
  const [sessionState, setSessionState] = useState<ChatSessionState | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Start a new chat session
  const startSession = useCallback(async (request: ChatDialogueRequest) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const dialogue = await generateChatDialogue(request);
      const sessionId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const newSessionState: ChatSessionState = {
        sessionId,
        currentSegmentIndex: 0,
        dialogue,
        performance: [],
        isLoading: false,
        error: null,
        sessionStartTime: new Date(),
        totalTimeSpent: 0
      };
      
      setSessionState(newSessionState);
      return sessionId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start chat session';
      setError(errorMessage);
      options.onError?.(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  // Get current dialogue segment
  const getCurrentSegment = useCallback((): DialogueSegmentData | null => {
    if (!sessionState || sessionState.currentSegmentIndex >= sessionState.dialogue.segments.length) {
      return null;
    }
    return sessionState.dialogue.segments[sessionState.currentSegmentIndex];
  }, [sessionState]);

  // Submit translation for current segment
  const submitTranslation = useCallback(async (userTranslation: string) => {
    if (!sessionState) {
      throw new Error('No active session');
    }

    const currentSegment = getCurrentSegment();
    if (!currentSegment) {
      throw new Error('No current segment');
    }

    setIsLoading(true);
    setError(null);

    try {
      const startTime = Date.now();
      
      // Evaluate the translation
      const evaluation = await evaluateUserTranslation(
        currentSegment.englishText,
        userTranslation,
        currentSegment.chineseText,
        `Topic: ${sessionState.dialogue.topic}, Speaker: ${currentSegment.speaker}`
      );

      const endTime = Date.now();
      const timeSpent = Math.round((endTime - startTime) / 1000); // seconds

      // Create performance data
      const performanceData: ChatPerformanceData = {
        segmentIndex: sessionState.currentSegmentIndex,
        userTranslation,
        evaluation,
        timeSpent,
        attempts: 1, // TODO: Track multiple attempts
        timestamp: new Date()
      };

      // Update session state
      setSessionState(prev => {
        if (!prev) return null;
        
        const updatedPerformance = [...prev.performance, performanceData];
        const updatedTotalTime = prev.totalTimeSpent + timeSpent;
        
        return {
          ...prev,
          performance: updatedPerformance,
          totalTimeSpent: updatedTotalTime
        };
      });

      return evaluation;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to evaluate translation';
      setError(errorMessage);
      options.onError?.(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [sessionState, getCurrentSegment, options]);

  // Move to next segment
  const nextSegment = useCallback(() => {
    if (!sessionState) return false;

    const nextIndex = sessionState.currentSegmentIndex + 1;
    const isComplete = nextIndex >= sessionState.dialogue.segments.length;

    setSessionState(prev => {
      if (!prev) return null;
      
      return {
        ...prev,
        currentSegmentIndex: nextIndex
      };
    });

    // Check if session is complete
    if (isComplete) {
      options.onSessionComplete?.(sessionState.sessionId, sessionState.performance);
    }

    return !isComplete; // Returns true if there are more segments
  }, [sessionState, options]);

  // Get session progress
  const getProgress = useCallback(() => {
    if (!sessionState) return { current: 0, total: 0, percentage: 0 };
    
    const current = sessionState.currentSegmentIndex;
    const total = sessionState.dialogue.segments.length;
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
    
    return { current, total, percentage };
  }, [sessionState]);

  // Get session statistics
  const getSessionStats = useCallback(() => {
    if (!sessionState) return null;

    const completedSegments = sessionState.performance.length;
    const totalSegments = sessionState.dialogue.segments.length;
    const averageScore = completedSegments > 0 
      ? Math.round(sessionState.performance.reduce((sum, p) => sum + p.evaluation.score, 0) / completedSegments)
      : 0;
    const totalTime = sessionState.totalTimeSpent;
    const correctTranslations = sessionState.performance.filter(p => p.evaluation.isCorrect).length;
    const accuracy = completedSegments > 0 ? Math.round((correctTranslations / completedSegments) * 100) : 0;

    return {
      completedSegments,
      totalSegments,
      averageScore,
      totalTime,
      accuracy,
      topic: sessionState.dialogue.topic,
      difficulty: sessionState.dialogue.difficulty
    };
  }, [sessionState]);

  // Check if session is complete
  const isSessionComplete = useCallback(() => {
    if (!sessionState) return false;
    return sessionState.currentSegmentIndex >= sessionState.dialogue.segments.length;
  }, [sessionState]);

  // Reset session
  const resetSession = useCallback(() => {
    setSessionState(null);
    setError(null);
    setIsLoading(false);
  }, []);

  // Pause/Resume session (for future use)
  const pauseSession = useCallback(() => {
    // Implementation for pausing session
    // Could save state to localStorage or database
  }, []);

  const resumeSession = useCallback((savedState: ChatSessionState) => {
    setSessionState(savedState);
  }, []);

  return {
    // State
    sessionState,
    isLoading,
    error,
    
    // Actions
    startSession,
    submitTranslation,
    nextSegment,
    resetSession,
    pauseSession,
    resumeSession,
    
    // Getters
    getCurrentSegment,
    getProgress,
    getSessionStats,
    isSessionComplete,
    
    // Computed values
    hasActiveSession: !!sessionState,
    currentSegment: getCurrentSegment(),
    progress: getProgress(),
    sessionStats: getSessionStats(),
    sessionComplete: isSessionComplete()
  };
};
