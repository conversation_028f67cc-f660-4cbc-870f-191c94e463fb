import { useState, useEffect, useCallback } from 'react';
import { GrammarProgress, SuperMemoGrade } from '@/types/progress';
import { localDB, STORES } from '@/lib/localdb';
import dayjs from 'dayjs';
import { getGrammarMasteryLevel, GrammarMasteryLevel } from '@/lib/spaced-repetition';

export function useGrammarProgress() {
  const [progress, setProgress] = useState<Record<string, GrammarProgress>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadProgress = async () => {
      setIsLoading(true);
      try {
        const savedProgressArray = await localDB.getAll<GrammarProgress>(STORES.GRAMMAR_PROGRESS);
        const savedProgressMap = savedProgressArray.reduce((acc, item) => {
          acc[item.id] = item;
          return acc;
        }, {} as Record<string, GrammarProgress>);
        setProgress(savedProgressMap);
      } catch (error) {
        console.error("Failed to load grammar progress from IndexedDB:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProgress();
  }, []);

  const calculateOverallMastery = useCallback((currentProgress: GrammarProgress): number => {
    let totalAccuracy = 0;
    let exerciseTypeCount = 0;

    for (const type in currentProgress.exercisePerformance) {
      const { correct, attempts } = currentProgress.exercisePerformance[type];
      if (attempts > 0) {
        totalAccuracy += correct / attempts;
        exerciseTypeCount++;
      }
    }

    const averageExerciseAccuracy = exerciseTypeCount > 0 ? totalAccuracy / exerciseTypeCount : 0;

    // Weighted average: 70% from exercise accuracy, 30% from self-assessment
    let overallMastery = averageExerciseAccuracy * 0.7;
    if (currentProgress.selfAssessmentScore !== undefined) {
      // Normalize self-assessment score (e.g., 1-5 scale to 0-1)
      const normalizedSelfAssessment = (currentProgress.selfAssessmentScore - 1) / 4;
      overallMastery += normalizedSelfAssessment * 0.3;
    }

    return Math.min(1, Math.max(0, overallMastery)); // Ensure mastery is between 0 and 1
  }, []);

  const initializeGrammarProgress = useCallback(async (grammarItemId: string) => {
    const newProgress: GrammarProgress = {
      id: grammarItemId,
      overallMastery: 0,
      lastPracticedDate: dayjs().toISOString(),
      exercisePerformance: {},
      history: [],
    };
    await localDB.put(STORES.GRAMMAR_PROGRESS, newProgress);
    setProgress(prev => ({ ...prev, [grammarItemId]: newProgress }));
    return newProgress;
  }, []);

  const updateGrammarExercisePerformance = useCallback(async (
    grammarItemId: string,
    exerciseType: string,
    isCorrect: boolean,
    grade: SuperMemoGrade // Assuming SuperMemoGrade is used for history
  ) => {
    setProgress(prev => {
      const currentProgress = prev[grammarItemId] || {
        id: grammarItemId,
        overallMastery: 0,
        lastPracticedDate: dayjs().toISOString(),
        exercisePerformance: {},
        history: [],
      };

      const updatedExercisePerformance = { ...currentProgress.exercisePerformance };
      if (!updatedExercisePerformance[exerciseType]) {
        updatedExercisePerformance[exerciseType] = { correct: 0, incorrect: 0, attempts: 0 };
      }

      updatedExercisePerformance[exerciseType].attempts++;
      if (isCorrect) {
        updatedExercisePerformance[exerciseType].correct++;
      } else {
        updatedExercisePerformance[exerciseType].incorrect++;
      }

      const updatedProgress: GrammarProgress = {
        ...currentProgress,
        exercisePerformance: updatedExercisePerformance,
        lastPracticedDate: dayjs().toISOString(),
        history: [...(currentProgress.history || []), { date: dayjs().toISOString(), grade, exerciseType }],
      };

      updatedProgress.overallMastery = calculateOverallMastery(updatedProgress);

      localDB.put(STORES.GRAMMAR_PROGRESS, updatedProgress)
        .catch(error => console.error("Failed to save grammar exercise performance to IndexedDB:", error));

      return { ...prev, [grammarItemId]: updatedProgress };
    });
  }, [calculateOverallMastery]);

  const updateGrammarSelfAssessment = useCallback(async (grammarItemId: string, score: number) => {
    setProgress(prev => {
      const currentProgress = prev[grammarItemId] || {
        id: grammarItemId,
        overallMastery: 0,
        lastPracticedDate: dayjs().toISOString(),
        exercisePerformance: {},
        history: [],
      };

      const updatedProgress: GrammarProgress = {
        ...currentProgress,
        selfAssessmentScore: score,
        lastPracticedDate: dayjs().toISOString(),
        history: [...(currentProgress.history || []), { date: dayjs().toISOString(), grade: score }], // Using score as grade for self-assessment
      };

      updatedProgress.overallMastery = calculateOverallMastery(updatedProgress);

      localDB.put(STORES.GRAMMAR_PROGRESS, updatedProgress)
        .catch(error => console.error("Failed to save grammar self-assessment to IndexedDB:", error));

      return { ...prev, [grammarItemId]: updatedProgress };
    });
  }, [calculateOverallMastery]);

  const getGrammarItemProgress = useCallback((grammarItemId: string): GrammarProgress | undefined => {
    return progress[grammarItemId];
  }, [progress]);

  const getAllGrammarProgress = useCallback((): GrammarProgress[] => {
    return Object.values(progress);
  }, [progress]);

  const getGrammarMastery = useCallback((grammarItemId: string): GrammarMasteryLevel | undefined => {
    const itemProgress = progress[grammarItemId];
    if (itemProgress) {
      return getGrammarMasteryLevel(itemProgress.overallMastery);
    }
    return undefined;
  }, [progress]);

  const getPrioritizedGrammarRules = useCallback((): string[] => {
    const allGrammarProgress = Object.values(progress);
    if (allGrammarProgress.length === 0) {
      return [];
    }

    const now = dayjs();

    const prioritized = allGrammarProgress.map(item => {
      let score = 0;

      // 1. Lowest overallMastery (higher priority for lower mastery)
      // Scale 0-1, so (1 - mastery) gives higher score for lower mastery
      score += (1 - item.overallMastery) * 50; // High weight

      // 2. Lowest accuracy in specific exercisePerformance types
      let totalExerciseAccuracy = 0;
      let exerciseTypeCount = 0;
      for (const type in item.exercisePerformance) {
        const { correct, attempts } = item.exercisePerformance[type];
        if (attempts > 0) {
          totalExerciseAccuracy += correct / attempts;
          exerciseTypeCount++;
        }
      }
      const averageExerciseAccuracy = exerciseTypeCount > 0 ? totalExerciseAccuracy / exerciseTypeCount : 1; // Default to 1 if no attempts
      score += (1 - averageExerciseAccuracy) * 30; // Medium weight

      // 3. Lowest selfAssessmentScore (higher priority for lower score)
      if (item.selfAssessmentScore !== undefined) {
        const normalizedSelfAssessment = (item.selfAssessmentScore - 1) / 4; // Scale 1-5 to 0-1
        score += (1 - normalizedSelfAssessment) * 20; // Medium weight
      } else {
        // If no self-assessment, give a slight boost to encourage review
        score += 5;
      }

      // 4. Time since lastPracticedDate (older items need refreshing)
      const lastPracticed = dayjs(item.lastPracticedDate);
      const daysSinceLastPracticed = now.diff(lastPracticed, 'day');
      score += daysSinceLastPracticed * 0.5; // Lower weight, but increases over time

      // Add a boost for new items (no practice history)
      if (item.history?.length === 0) {
        score += 100; // Significant boost for new items
      }

      return { id: item.id, score };
    });

    // Sort by score in descending order (higher score means higher priority)
    prioritized.sort((a, b) => b.score - a.score);

    return prioritized.map(item => item.id);
  }, [progress]);

  return {
    progress,
    isLoading,
    initializeGrammarProgress,
    updateGrammarExercisePerformance,
    updateGrammarSelfAssessment,
    getGrammarItemProgress,
    getAllGrammarProgress,
    getGrammarMastery,
    getPrioritizedGrammarRules, // Expose the new function
  };
}