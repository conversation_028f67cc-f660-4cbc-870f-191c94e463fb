import { useState, useEffect, useRef, useCallback } from 'react';

// Type definitions for SpeechRecognition (consistent with WritingGame.tsx)
interface ISpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: ISpeechRecognitionResultList;
}

interface ISpeechRecognitionResultList {
  readonly length: number;
  item(index: number): ISpeechRecognitionResult;
  [index: number]: ISpeechRecognitionResult;
}

interface ISpeechRecognitionResult {
  readonly isFinal: boolean;
  readonly length: number;
  item(index: number): ISpeechRecognitionAlternative;
  [index: number]: ISpeechRecognitionAlternative;
}

interface ISpeechRecognitionAlternative {
  readonly transcript: string;
  readonly confidence: number;
}

interface SpeechGrammar {
  src: string;
  weight: number;
}

interface SpeechGrammarList {
  readonly length: number;
  item(index: number): SpeechGrammar;
  [index: number]: SpeechGrammar;
  addFromString(string: string, weight?: number): void;
  addFromURI(src: string, weight?: number): void;
}

interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string; 
  readonly message: string; 
}

interface ISpeechRecognitionStatic {
  new (): ISpeechRecognition;
}

interface ISpeechRecognition extends EventTarget {
  grammars: SpeechGrammarList;
  lang: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  serviceURI: string;

  onaudiostart: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onaudioend: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onend: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onerror: ((this: ISpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null;
  onnomatch: ((this: ISpeechRecognition, ev: ISpeechRecognitionEvent) => void) | null;
  onresult: ((this: ISpeechRecognition, ev: ISpeechRecognitionEvent) => void) | null;
  onsoundstart: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onsoundend: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onspeechstart: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onspeechend: ((this: ISpeechRecognition, ev: Event) => void) | null;
  onstart: ((this: ISpeechRecognition, ev: Event) => void) | null;

  abort(): void;
  start(): void;
  stop(): void;
}

declare global {
  interface Window {
    SpeechRecognition: ISpeechRecognitionStatic;
    webkitSpeechRecognition: ISpeechRecognitionStatic;
  }
}

interface UseSpeechToTextOptions {
  lang?: string;
  continuous?: boolean;
  interimResults?: boolean;
}

interface UseSpeechToTextReturn {
  isListening: boolean;
  transcript: string;
  error: string | null;
  startListening: () => void;
  stopListening: () => void;
  clearError: () => void;
  browserSupportsSpeechRecognition: boolean;
}

const useSpeechToText = (options?: UseSpeechToTextOptions): UseSpeechToTextReturn => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const recognitionRef = useRef<ISpeechRecognition | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);
  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(true);

  useEffect(() => {
    if (typeof window === 'undefined') {
      setBrowserSupportsSpeechRecognition(false);
      return;
    }

    const SpeechRecognitionAPI =
      window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognitionAPI) {
      console.warn('SpeechRecognition API is not supported in this browser.');
      setError('SpeechRecognition API is not supported in this browser.');
      setBrowserSupportsSpeechRecognition(false);
      return;
    }

    if (!recognitionRef.current) {
        recognitionRef.current = new SpeechRecognitionAPI();
        recognitionRef.current.lang = options?.lang || 'zh-CN';
        recognitionRef.current.continuous = options?.continuous !== undefined ? options.continuous : true;
        recognitionRef.current.interimResults = options?.interimResults !== undefined ? options.interimResults : true;

        recognitionRef.current.onstart = () => {
            setIsListening(true);
            setError(null);
        };

        recognitionRef.current.onend = () => {
            setIsListening(false);
        };

        recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent) => {
          console.error('Speech recognition error:', event.error, event.message);
          let errorMessage = 'An unknown error occurred during speech recognition.';
          switch (event.error) {
            case 'no-speech':
              errorMessage = 'No speech was detected. Please try again.';
              break;
            case 'audio-capture':
              errorMessage = 'Audio capture failed. Please check your microphone.';
              break;
            case 'not-allowed':
              errorMessage = 'Permission to use microphone was denied. Please enable microphone access in your browser settings.';
              break;
            case 'network':
              errorMessage = 'A network error occurred. Please check your internet connection.';
              break;
            case 'aborted':
              errorMessage = 'Speech recognition was aborted.';
              break;
            case 'language-not-supported':
              errorMessage = 'The selected language is not supported by the speech recognition service.';
              break;
            case 'service-not-allowed':
              errorMessage = 'The speech recognition service is not allowed. This might be due to browser policies or extensions.';
              break;
            default:
              errorMessage = `Speech recognition error: ${event.error}. ${event.message || ''}`.trim();
          }
          setError(errorMessage);
          setIsListening(false); // Ensure listening state is reset on error
        };

        recognitionRef.current.onresult = (event: ISpeechRecognitionEvent) => {
            const currentTranscript = Array.from(event.results)
                .map(result => result[0]) // SpeechRecognitionResult
                .map(result => result.transcript) // SpeechRecognitionAlternative
                .join('');
            setTranscript(currentTranscript);
        };
    }

    // Cleanup function
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current.onstart = null;
        recognitionRef.current.onend = null;
        recognitionRef.current.onerror = null;
        recognitionRef.current.onresult = null;
        // recognitionRef.current = null; // Avoid nullifying here if re-used across re-renders without full re-init
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options?.lang, options?.continuous, options?.interimResults]); // Re-run if options change

  const startListening = useCallback(() => {
    if (recognitionRef.current && !isListening) {
      try {
        setTranscript(''); // Clear previous transcript
        setError(null);
        recognitionRef.current.start();
      } catch (e: any) {
        console.error('Error starting speech recognition:', e);
        setError(e.message || 'Failed to start speech recognition.');
        setIsListening(false);
      }
    }
  }, [isListening]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
      } catch (e: any) {
        console.error('Error stopping speech recognition:', e);
        setError(e.message || 'Failed to stop speech recognition.');
      }
      // onend will set isListening to false
    }
  }, [isListening]);

  return {
    isListening,
    transcript,
    error,
    startListening,
    stopListening,
    clearError,
    browserSupportsSpeechRecognition
  };
};

export default useSpeechToText;