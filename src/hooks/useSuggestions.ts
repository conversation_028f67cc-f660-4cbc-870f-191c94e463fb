import { useCallback, useEffect, useState } from 'react';
import { VocabularyItem } from '@/types/content';

interface SuggestionResult {
  word: string;
  pinyin: string;
  definition: string;
  score: number;
}

export function useSuggestions(vocabulary: VocabularyItem[]) {
  const [suggestions, setSuggestions] = useState<SuggestionResult[]>([]);

  const analyzeSentence = useCallback((text: string) => {
    if (!text.trim()) {
      setSuggestions([]);
      return;
    }

    // Split the text into characters and words for analysis
    const characters = text.split('');
    const lastChar = characters[characters.length - 1];
    const words = text.split(/\s+/);
    const lastWord = words[words.length - 1];

    // Score and rank vocabulary items based on relevance
    const scoredSuggestions = vocabulary
      .map(item => {
        let score = 0;

        // Don't suggest words already used in the text
        if (text.includes(item.word)) {
          return null;
        }

        // Check if the vocabulary item starts with the last character typed
        if (item.word.startsWith(lastChar)) {
          score += 3;
        }

        // Check if the vocabulary item is related to any words in the text
        words.forEach(word => {
          if (item.definitions.some(def => def.toLowerCase().includes(word.toLowerCase()))) {
            score += 1;
          }
        });

        // Check category relevance
        if (text.includes(item.category)) {
          score += 2;
        }

        // Check if the word's part of speech might be relevant
        // For example, after a subject, suggest verbs
        if (lastWord && item.partOfSpeech === 'verb' && text.length > 3) {
          score += 1;
        }

        return score > 0
          ? {
              word: item.word,
              pinyin: item.pinyin,
              definition: item.definitions[0],
              score
            }
          : null;
      })
      .filter((item): item is SuggestionResult => item !== null)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);

    setSuggestions(scoredSuggestions);
  }, [vocabulary]);

  return {
    suggestions,
    analyzeSentence
  };
}