import { useState, useEffect, useCallback } from 'react';

export interface UseTextToSpeechOptions {
  defaultLang?: string;
  defaultRate?: number;
  defaultPitch?: number;
  defaultVoiceName?: string; // Allow specifying a preferred voice by name
}

interface UseTextToSpeechReturn {
  isPlaying: boolean;
  error: string | null;
  speak: (text: string, lang?: string, rate?: number, pitch?: number, voiceName?: string) => void;
  cancel: () => void;
  clearError: () => void;
  getAvailableVoices: (lang?: string) => SpeechSynthesisVoice[];
  browserSupportsSpeechSynthesis: boolean;
}

const useTextToSpeech = (options?: UseTextToSpeechOptions): UseTextToSpeechReturn => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [browserSupportsSpeechSynthesis, setBrowserSupportsSpeechSynthesis] = useState(true);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined' || !window.speechSynthesis) {
      console.warn('SpeechSynthesis API is not supported in this browser.');
      setError('SpeechSynthesis API is not supported in this browser.');
      setBrowserSupportsSpeechSynthesis(false);
      return;
    }

    const updateVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      setVoices(availableVoices);
    };

    // Initial voices load
    updateVoices();

    // Voices can be loaded asynchronously, so listen for changes
    window.speechSynthesis.onvoiceschanged = updateVoices;

    return () => {
      window.speechSynthesis.onvoiceschanged = null; // Clean up listener
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel(); // Stop any ongoing speech on unmount
      }
    };
  }, []);

  const getAvailableVoices = useCallback((lang?: string): SpeechSynthesisVoice[] => {
    if (!browserSupportsSpeechSynthesis) return [];
    if (lang) {
      return voices.filter(voice => voice.lang.startsWith(lang));
    }
    return voices;
  }, [voices, browserSupportsSpeechSynthesis]);

  const speak = useCallback(
    (text: string, lang?: string, rate?: number, pitch?: number, voiceName?: string) => {
      if (!browserSupportsSpeechSynthesis || !text) return;

      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel(); // Stop current speech before starting new one
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang || options?.defaultLang || 'zh-CN';
      utterance.rate = rate || options?.defaultRate || 1;
      utterance.pitch = pitch || options?.defaultPitch || 1;

      const selectedVoiceName = voiceName || options?.defaultVoiceName;
      if (selectedVoiceName) {
        const voice = voices.find(v => v.name === selectedVoiceName && v.lang.startsWith(utterance.lang));
        if (voice) {
          utterance.voice = voice;
        } else {
          console.warn(`Voice "${selectedVoiceName}" not found for lang "${utterance.lang}". Using default.`);
        }
      }
      // If no specific voice or preferred voice not found, try to find any matching language voice
      if (!utterance.voice) {
        const langVoices = getAvailableVoices(utterance.lang);
        if (langVoices.length > 0) {
            // Prefer a voice that is explicitly for the language, not a region-specific one if possible
            utterance.voice = langVoices.find(v => v.lang === utterance.lang) || langVoices[0];
        }
      }

      utterance.onstart = () => {
        setIsPlaying(true);
        setError(null);
      };

      utterance.onend = () => {
        setIsPlaying(false);
      };

      utterance.onerror = (event: SpeechSynthesisErrorEvent) => {
        console.error('Speech synthesis error:', event.error);
        let errorMessage = 'An unknown error occurred during speech synthesis.';
        // Use event.error to determine the cause
        switch (event.error) {
          case 'canceled':
            // This might not be an error if intentionally cancelled, but good to log
            errorMessage = 'Speech synthesis was canceled.';
            break;
          case 'interrupted':
            errorMessage = 'Speech synthesis was interrupted.';
            break;
          case 'audio-busy':
            errorMessage = 'Audio output is busy. Please try again shortly.';
            break;
          case 'audio-hardware':
            errorMessage = 'Audio hardware error. Please check your audio output device.';
            break;
          case 'network':
            errorMessage = 'A network error occurred while fetching speech data.';
            break;
          case 'synthesis-unavailable':
            errorMessage = 'Speech synthesis service is temporarily unavailable.';
            break;
          case 'synthesis-failed':
            errorMessage = 'Speech synthesis failed.';
            break;
          case 'language-unavailable':
            errorMessage = 'The selected language is not available for synthesis.';
            break;
          case 'voice-unavailable':
            errorMessage = 'The selected voice is not available for synthesis.';
            break;
          case 'text-too-long':
            errorMessage = 'The text to synthesize is too long.';
            break;
          case 'invalid-argument':
            errorMessage = 'Invalid argument provided for speech synthesis.';
            break;
          default:
            errorMessage = `Speech synthesis error: ${event.error || 'unknown'}`.trim();
        }
        setError(errorMessage);
        setIsPlaying(false);
      };

      try {
        window.speechSynthesis.speak(utterance);
      } catch (e: unknown) {
        console.error('Error speaking text:', e);
        if (e instanceof Error) {
          setError(e.message || 'Failed to speak text.');
        } else {
          setError('An unknown error occurred while trying to speak.');
        }
        setIsPlaying(false);
      }
    },
    [voices, options, browserSupportsSpeechSynthesis, getAvailableVoices]
  );

  const cancel = useCallback(() => {
    if (!browserSupportsSpeechSynthesis) return;
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
      setIsPlaying(false);
    }
  }, [browserSupportsSpeechSynthesis]);

  return {
    isPlaying,
    error,
    speak,
    cancel,
    clearError,
    getAvailableVoices,
    browserSupportsSpeechSynthesis
  };
};

export default useTextToSpeech;