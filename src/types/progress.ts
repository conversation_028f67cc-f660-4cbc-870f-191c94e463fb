export interface VocabularyProgress extends SuperMemoItem {
  id: string;
  lastReviewDate: string;
  dueDate: string;
}

export interface GrammarProgress {
  id: string; // primary key, links to GrammarItem.id
  overallMastery: number; // 0-1 or categorical e.g., 0: <PERSON><PERSON>, 1: <PERSON><PERSON><PERSON>, etc.
  lastPracticedDate: string; // ISO date
  exercisePerformance: {
    [exerciseType: string]: {
      correct: number;
      incorrect: number;
      attempts: number;
    };
  };
  selfAssessmentScore?: number; // optional, e.g., 1-5 scale
  history?: { date: string; grade: SuperMemoGrade | number; exerciseType?: string }[]; // optional
}

export interface SuperMemoItem {
  interval: number;  // days until next review
  repetition: number;  // number of successful reviews
  efactor: number;  // easiness factor (1.3 - 2.5)
}

export type SuperMemoGrade = 0 | 1 | 2 | 3 | 4 | 5;

// Grade descriptions:
// 5: Perfect response
// 4: Correct after hesitation
// 3: Correct with difficulty
// 2: Incorrect but remembered
// 1: Incorrect with hint
// 0: Complete blackout