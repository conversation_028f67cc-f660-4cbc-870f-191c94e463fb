import { Timestamp } from 'firebase/firestore';

// AI Service Schemas for Chat Feature
export interface ChatDialogueRequest {
  topic: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  targetCharacters?: string[]; // Characters to focus on
  conversationLength: number; // Number of dialogue segments
  speakers: number; // Number of people in conversation (2-4)
}

export interface GeneratedDialogue {
  topic: string;
  difficulty: string;
  speakers: string[];
  segments: DialogueSegmentData[];
  vocabulary: string[];
  grammarPoints: string[];
  culturalContext?: string;
}

export interface DialogueSegmentData {
  speaker: string;
  chineseText: string;
  englishText: string;
  pinyin?: string;
  targetVocabulary?: string[];
  targetGrammar?: string[];
  difficulty: number; // 1-10 scale
}

export interface TranslationEvaluation {
  score: number; // 0-100
  isCorrect: boolean;
  feedback: string;
  correctTranslation: string;
  mistakeAnalysis: CharacterMistakeAnalysis[];
  suggestions: string[];
  contextTips?: string[];
}

export interface CharacterMistakeAnalysis {
  character: string;
  expectedCharacter: string;
  position: number; // Position in the user's translation
  mistakeType: 'substitution' | 'omission' | 'addition' | 'tone' | 'context' | 'grammar';
  severity: 'minor' | 'moderate' | 'major';
  explanation: string;
  similarCharacters?: string[]; // Characters that might cause confusion
  contextualUsage?: string; // How this character should be used in this context
}

// Chat Session State Management
export interface ChatSessionState {
  sessionId: string;
  currentSegmentIndex: number;
  dialogue: GeneratedDialogue;
  performance: ChatPerformanceData[];
  isLoading: boolean;
  error: string | null;
  sessionStartTime: Date;
  totalTimeSpent: number;
}

export interface ChatPerformanceData {
  segmentIndex: number;
  userTranslation: string;
  evaluation: TranslationEvaluation;
  timeSpent: number;
  attempts: number;
  timestamp: Date;
}

// Progress Tracking
export interface ChatProgressSummary {
  sessionId: string;
  topic: string;
  totalSegments: number;
  completedSegments: number;
  overallScore: number;
  timeSpent: number;
  charactersLearned: string[];
  charactersMistaken: CharacterMistakeFrequency[];
  improvementAreas: string[];
  strengths: string[];
}

export interface CharacterMistakeFrequency {
  character: string;
  frequency: number;
  contexts: string[];
  lastOccurrence: Date;
  improvementTrend: 'improving' | 'stable' | 'declining';
}

// Adaptive Learning Types
export interface AdaptiveLearningProfile {
  userId: string;
  totalSessions: number;
  averageScore: number;
  preferredTopics: string[];
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
  problematicCharacters: ProblematicCharacter[];
  learningVelocity: number; // Characters mastered per session
  lastUpdated: Date;
}

export interface ProblematicCharacter {
  character: string;
  mistakeCount: number;
  totalEncounters: number;
  masteryScore: number; // 0-100
  mistakeTypes: string[];
  contexts: string[];
  firstEncounter: Date;
  lastMistake: Date;
  improvementRate: number; // How quickly user is improving with this character
}

// Topic and Content Generation
export interface ChatTopic {
  id: string;
  name: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  estimatedDuration: number; // minutes
  keyVocabulary: string[];
  culturalContext?: string;
  popularity: number; // How often this topic is selected
}

// API Response Types
export interface StartChatSessionResponse {
  sessionId: string;
  dialogue: GeneratedDialogue;
  firstSegment: DialogueSegmentData;
}

export interface ContinueChatSessionResponse {
  evaluation: TranslationEvaluation;
  nextSegment?: DialogueSegmentData;
  isSessionComplete: boolean;
  progressSummary?: ChatProgressSummary;
}

export interface EndChatSessionResponse {
  sessionSummary: ChatProgressSummary;
  adaptiveLearningUpdate: AdaptiveLearningProfile;
  recommendations: string[];
}

// UI Component Props
export interface ChatInterfaceProps {
  sessionId?: string;
  onSessionComplete: (summary: ChatProgressSummary) => void;
  onSessionExit: () => void;
}

export interface TopicSelectorProps {
  onTopicSelect: (topic: ChatTopic) => void;
  userProfile?: AdaptiveLearningProfile;
  recommendedTopics?: ChatTopic[];
}

export interface TranslationEvaluatorProps {
  evaluation: TranslationEvaluation;
  onContinue: () => void;
  showCorrectAnswer: boolean;
}

export interface PerformanceDashboardProps {
  userId: string;
  sessions: ChatProgressSummary[];
  adaptiveProfile: AdaptiveLearningProfile;
}
