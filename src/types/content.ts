import { Timestamp } from 'firebase/firestore';

// Example structure used in both grammar points and vocabulary items
export interface Example {
  chinese: string;
  pinyin: string;
  english: string;
}

// Vocabulary item content type
export interface VocabularyItem {
  id: string; // Add document ID
  character: string;
  pinyin: string;
  meaning: string;
  exampleSentences: { chinese: string; english: string }[];
  // The following fields are from the original VocabularyItem, but are not explicitly mentioned in the plan for IndexedDB.
  // Keeping them here for now, but they might be removed if they are only relevant for Firestore.
  word?: string;
  partOfSpeech?: string;
  definitions?: string[];
  examples?: Example[];
  category?: string;
  chapter?: string;
  createdAt?: Timestamp;
  tags?: string[];
}

// Define a serializable version of VocabularyItem for client components
export interface SerializableVocabularyItem extends Omit<VocabularyItem, 'createdAt' | 'word' | 'pinyin' | 'definitions' | 'examples'> {
  createdAt: string | null;
  word: string;
  pinyin: string;
  definitions: string[];
  examples: { chinese: string; pinyin: string; english: string; }[];
}

// Grammar point content type
export interface GrammarPoint {
  id: string; // Add document ID
  ruleName: string;
  explanation: string;
  examples: Example[]; // Use the shared Example interface
  difficultyLevel: 'easy' | 'medium' | 'hard';
  // The following fields are from the original GrammarPoint, but are not explicitly mentioned in the plan for IndexedDB.
  // Keeping them here for now, but they might be removed if they are only relevant for Firestore.
  title?: string;
  relatedPoints?: string[];
  category?: string;
  chapter?: string;
  createdAt?: Timestamp;
  tags?: string[];
}

// Grammar Game content type
export interface GrammarGame {
  id: string; // Add document ID
  title: string;
  description: string;
  instructions: string;
  example: string;
  targetGrammar: string | string[]; // String or array of strings
  difficulty: string;
  interactiveElements: string[]; // Array of strings
  category: string;
  gameId: string;
  createdAt: Timestamp;
  // Game-specific data will be added here as needed,
  // potentially as a union type or a generic object depending on complexity.
  // Based on docs/firestore_refactoring_strategy.md, line 106-112, define a specific type for game content.
  content?: DailyRoutineBuilderContent[]; // Example content structure for a specific game
}

// Example content structure for the "Daily Routine Builder" game
export interface DailyRoutineBuilderContent {
  timeExpression: string;
  verb: string;
  correctSentence: string;
  otherElements?: string[]; // Add other parts of the sentence if needed, e.g., object, location
}


// Metadata types
// Structure for the nested tags within refined_tag_structure
export interface TagStructure {
  description?: string;
  items?: string[]; // Array of associated vocabulary or grammar point IDs/titles
  [key: string]: TagStructure | string | string[] | undefined; // Allows for nested tags
}

// Structure for the categoryStructure metadata document
export interface CategoryStructureMetadata {
  refined_tag_structure: {
    topical_tags: TagStructure;
    grammatical_tags: TagStructure;
    [key: string]: TagStructure; // Allow for other top-level tag categories
  };
}

// Union type for the main content collections
export type LearningContentItem = VocabularyItem | GrammarPoint | GrammarGame;


// Type for content collections
export type ContentCollection = 'vocabulary' | 'grammar' | 'grammarGames' | 'metadata';

// Helper function to get collection name
export function getCollectionName(collectionType: ContentCollection): string {
  return collectionType;
}
