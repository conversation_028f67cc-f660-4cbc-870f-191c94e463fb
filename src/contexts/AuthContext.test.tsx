import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from './AuthContext';
import { createUserWithEmailAndPassword, onAuthStateChanged, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

// Mock Firebase
jest.mock('firebase/auth');
jest.mock('firebase/firestore');
jest.mock('@/lib/firebase', () => ({
  auth: {},
  db: {},
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { currentUser, userProfile, loading } = useAuth();
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      <div data-testid="auth-state">
        {currentUser ? 'Logged In' : 'Logged Out'}
      </div>
      {userProfile && (
        <div data-testid="user-profile">
          {userProfile.username}
        </div>
      )}
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    // Mock onAuthStateChanged to simulate no user initially
    (onAuthStateChanged as jest.Mock).mockImplementation((auth, callback) => {
      callback(null);
      return jest.fn(); // Return unsubscribe function
    });
    
    // Mock getDoc to return null for user profile
    (getDoc as jest.Mock).mockResolvedValue({
      exists: () => false,
      data: () => null,
    });
  });
  
  it('provides authentication state to children', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Initially loading
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Wait for auth state to resolve
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Should show logged out state
    expect(screen.getByTestId('auth-state')).toHaveTextContent('Logged Out');
  });
  
  it('updates auth state when user logs in', async () => {
    // Mock user object
    const mockUser = {
      uid: 'test-user-id',
      email: '<EMAIL>',
    };
    
    // Mock user profile
    const mockUserProfile = {
      username: 'TestUser',
      email: '<EMAIL>',
      createdAt: { toDate: () => new Date() },
      lastLogin: { toDate: () => new Date() },
      role: 'user',
    };
    
    // Mock onAuthStateChanged to simulate a logged in user
    (onAuthStateChanged as jest.Mock).mockImplementation((auth, callback) => {
      callback(mockUser);
      return jest.fn(); // Return unsubscribe function
    });
    
    // Mock getDoc to return user profile
    (getDoc as jest.Mock).mockResolvedValue({
      exists: () => true,
      data: () => mockUserProfile,
    });
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for auth state to resolve
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Should show logged in state
    expect(screen.getByTestId('auth-state')).toHaveTextContent('Logged In');
    expect(screen.getByTestId('user-profile')).toHaveTextContent('TestUser');
  });
  
  it('provides signup function that creates a user', async () => {
    // Mock successful user creation
    const mockUserCredential = {
      user: {
        uid: 'new-user-id',
        email: '<EMAIL>',
      },
    };
    
    (createUserWithEmailAndPassword as jest.Mock).mockResolvedValue(mockUserCredential);
    (setDoc as jest.Mock).mockResolvedValue(undefined);
    
    // Test component that calls signup
    const TestSignup = () => {
      const { signup, loading } = useAuth();
      
      const handleSignup = async () => {
        await signup('<EMAIL>', 'password123', 'NewUser');
      };
      
      if (loading) return <div>Loading...</div>;
      
      return <button onClick={handleSignup}>Sign Up</button>;
    };
    
    render(
      <AuthProvider>
        <TestSignup />
      </AuthProvider>
    );
    
    // Wait for auth state to resolve
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click signup button
    await act(async () => {
      screen.getByText('Sign Up').click();
    });
    
    // Verify createUserWithEmailAndPassword was called
    expect(createUserWithEmailAndPassword).toHaveBeenCalledWith(
      auth,
      '<EMAIL>',
      'password123'
    );
    
    // Verify setDoc was called to create user profile
    expect(setDoc).toHaveBeenCalled();
    expect(setDoc.mock.calls[0][1]).toMatchObject({
      username: 'NewUser',
      email: '<EMAIL>',
      role: 'user',
    });
  });
  
  it('provides login function that authenticates a user', async () => {
    // Mock successful login
    const mockUserCredential = {
      user: {
        uid: 'existing-user-id',
        email: '<EMAIL>',
      },
    };
    
    (signInWithEmailAndPassword as jest.Mock).mockResolvedValue(mockUserCredential);
    (setDoc as jest.Mock).mockResolvedValue(undefined);
    
    // Test component that calls login
    const TestLogin = () => {
      const { login, loading } = useAuth();
      
      const handleLogin = async () => {
        await login('<EMAIL>', 'password123');
      };
      
      if (loading) return <div>Loading...</div>;
      
      return <button onClick={handleLogin}>Log In</button>;
    };
    
    render(
      <AuthProvider>
        <TestLogin />
      </AuthProvider>
    );
    
    // Wait for auth state to resolve
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click login button
    await act(async () => {
      screen.getByText('Log In').click();
    });
    
    // Verify signInWithEmailAndPassword was called
    expect(signInWithEmailAndPassword).toHaveBeenCalledWith(
      auth,
      '<EMAIL>',
      'password123'
    );
    
    // Verify setDoc was called to update last login
    expect(setDoc).toHaveBeenCalled();
    expect(setDoc.mock.calls[0][1]).toMatchObject({
      lastLogin: expect.any(Object),
    });
  });
  
  it('provides logout function that signs out a user', async () => {
    // Mock successful logout
    (signOut as jest.Mock).mockResolvedValue(undefined);
    
    // Test component that calls logout
    const TestLogout = () => {
      const { logout, loading } = useAuth();
      
      const handleLogout = async () => {
        await logout();
      };
      
      if (loading) return <div>Loading...</div>;
      
      return <button onClick={handleLogout}>Log Out</button>;
    };
    
    render(
      <AuthProvider>
        <TestLogout />
      </AuthProvider>
    );
    
    // Wait for auth state to resolve
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click logout button
    await act(async () => {
      screen.getByText('Log Out').click();
    });
    
    // Verify signOut was called
    expect(signOut).toHaveBeenCalledWith(auth);
  });
});
