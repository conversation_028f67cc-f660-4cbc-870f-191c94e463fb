"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  User,
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { doc, getDoc, setDoc, Timestamp } from "firebase/firestore";

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  signup: (email: string, password: string, username: string) => Promise<User>;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  updateApiKey: (apiKey: string) => Promise<void>;
  userProfile: UserProfile | null;
}

interface UserProfile {
  username: string;
  email: string;
  createdAt: Timestamp;
  lastLogin: Timestamp;
  role: string;
  apiKey?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  async function signup(email: string, password: string, username: string) {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );
    
    // Create user profile in Firestore
    const user = userCredential.user;
    await setDoc(doc(db, "users", user.uid), {
      username,
      email,
      createdAt: Timestamp.now(),
      lastLogin: Timestamp.now(),
      role: "user",
    });
    
    return user;
  }

  async function login(email: string, password: string) {
    const userCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    
    // Update last login
    const user = userCredential.user;
    await setDoc(
      doc(db, "users", user.uid),
      {
        lastLogin: Timestamp.now(),
      },
      { merge: true }
    );
    
    return user;
  }

  async function logout() {
    return signOut(auth);
  }

  async function updateApiKey(apiKey: string) {
    if (!currentUser) return;
    
    await setDoc(
      doc(db, "users", currentUser.uid),
      {
        apiKey,
      },
      { merge: true }
    );
    
    // Update local state
    if (userProfile) {
      setUserProfile({
        ...userProfile,
        apiKey,
      });
    }
  }

  async function fetchUserProfile(user: User) {
    const userDoc = await getDoc(doc(db, "users", user.uid));
    if (userDoc.exists()) {
      setUserProfile(userDoc.data() as UserProfile);
    }
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      if (user) {
        await fetchUserProfile(user);
      } else {
        setUserProfile(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userProfile,
    loading,
    signup,
    login,
    logout,
    updateApiKey,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
