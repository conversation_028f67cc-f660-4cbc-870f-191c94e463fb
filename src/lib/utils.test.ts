import { containsChinese } from './utils';

describe('containsChinese', () => {
  it('should return true for strings containing Chinese characters', () => {
    expect(containsChinese('你好')).toBe(true);
    expect(containsChinese('Hello 世界')).toBe(true);
    expect(containsChinese('你好，world!')).toBe(true);
  });

  it('should return false for strings not containing Chinese characters', () => {
    expect(containsChinese('Hello')).toBe(false);
    expect(containsChinese('12345')).toBe(false);
    expect(containsChinese('!@#$%^')).toBe(false);
  });

  it('should return false for an empty string', () => {
    expect(containsChinese('')).toBe(false);
  });

  it('should handle mixed scripts correctly', () => {
    expect(containsChinese('English and 中文')).toBe(true);
    expect(containsChinese('日本語 and English')).toBe(false); // Japanese is not Chinese
    expect(containsChinese('한국어 and English')).toBe(false); // Korean is not Chinese
    expect(containsChinese('Русский and 中文')).toBe(true);
    expect(containsChinese('English, 日本語, 中文, 한국어')).toBe(true);
  });

  it('should handle strings with only punctuation and spaces', () => {
    expect(containsChinese(' , . ! ? ')).toBe(false);
  });

  it('should handle strings with numbers and Chinese characters', () => {
    expect(containsChinese('123你好')).toBe(true);
  });

  it('should handle strings with English, numbers, and Chinese characters', () => {
    expect(containsChinese('Hello123你好World')).toBe(true);
  });
});