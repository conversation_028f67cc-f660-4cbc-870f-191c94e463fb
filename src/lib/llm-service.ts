// Import necessary modules
import { llmService } from "../lib/llm-abstraction/llm-abstraction-service";
import { z } from "zod";

// Define the types for grammar points and vocabulary items
export interface GrammarPoint {
  chapterNumber: number;
  chapterTitle: string;
  pointId: string;
  title: string;
  explanation: string;
  examples: {
    chinese: string;
    pinyin: string;
    english: string;
  }[];
  difficulty: number;
  relatedPoints?: string[];
}

export interface VocabularyItem {
  chapterNumber: number;
  chapterTitle: string;
  itemId: string;
  word: string;
  pinyin: string;
  partOfSpeech: string;
  definitions: string[];
  examples: {
    chinese: string;
    pinyin: string;
    english: string;
  }[];
  difficulty: number;
}

// Zod Schemas
const grammarPointSchema = z.object({
  title: z.string(),
  explanation: z.string(),
  examples: z.array(
    z.object({
      chinese: z.string(),
      pinyin: z.string(),
      english: z.string(),
    })
  ),
  relatedPoints: z.array(z.string()).optional(),
});
const vocabularyItemSchema = z.object({
  word: z.string(),
  pinyin: z.string(),
  partOfSpeech: z.string(),
  definitions: z.array(z.string()),
  examples: z.array(
    z.object({
      chinese: z.string(),
      pinyin: z.string(),
      english: z.string(),
    })
  ),
});

const grammarPointArraySchema = z.array(grammarPointSchema);
const vocabularyItemArraySchema = z.array(vocabularyItemSchema);


// Function to process raw content using the LLMAbstractionService
export async function processRawContent(
  content: string,
  contentType: 'grammar' | 'vocabulary',
  chapterNumber: number
  // apiKey parameter removed
): Promise<GrammarPoint[] | VocabularyItem[]> {
  // Build the prompt based on content type
  const prompt = buildPrompt(content, contentType, chapterNumber);

  try {
    let parsedResponse;
    if (contentType === 'grammar') {
      parsedResponse = await llmService.generateStructuredOutput(prompt, {
        responseSchema: grammarPointArraySchema,
        temperature: 0.2,
        maxTokens: 8192,
      });
      return validateGrammarPoints(parsedResponse, chapterNumber);
    } else {
      parsedResponse = await llmService.generateStructuredOutput(prompt, {
        responseSchema: vocabularyItemArraySchema,
        temperature: 0.2,
        maxTokens: 8192,
      });
      return validateVocabularyItems(parsedResponse, chapterNumber);
    }
  } catch (error: unknown) {
    console.error("Error processing content with AI:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to process content: ${errorMessage}`);
  }
}

// Build the prompt for the LLM
function buildPrompt(
  content: string,
  contentType: 'grammar' | 'vocabulary',
  chapterNumber: number
): string {
  if (contentType === 'grammar') {
    return `
You are a Chinese language learning content processor. Your task is to extract grammar points from the provided raw content and format them according to the specified JSON structure.

Raw Content:
${content}

Instructions:
1. Carefully analyze the raw content, which may be in various formats (text, markdown, etc.)
2. Extract all Chinese grammar points, patterns, or structures mentioned in the content
3. For each grammar point, provide:
   - A clear title (the grammar pattern itself)
   - A detailed explanation in English
   - At least 2-3 examples with Chinese text, pinyin, and English translation
4. If the raw content doesn't explicitly provide examples or explanations, use your knowledge to create appropriate ones
5. Format the output as a JSON array of grammar point objects

Output Format:
[
  {
    "title": "Grammar pattern (e.g., '虽然...但是...')",
    "explanation": "Detailed explanation of the grammar point in English",
    "examples": [
      {
        "chinese": "Example sentence in Chinese",
        "pinyin": "Pinyin for the example",
        "english": "English translation of the example"
      }
    ]
  }
]

The chapter number is ${chapterNumber}. Make sure all grammar points are correctly extracted and formatted.

IMPORTANT: If the content is not related to Chinese grammar or doesn't contain any identifiable grammar points, create at least 3-5 appropriate grammar points for Chapter ${chapterNumber} based on standard Chinese language curriculum.
`; // Removed: Please wrap the JSON output in <json> and </json> tags.
  } else {
    return `
You are a Chinese language learning content processor. Your task is to extract vocabulary items from the provided raw content and format them according to the specified JSON structure.

Raw Content:
${content}

Instructions:
1. Carefully analyze the raw content, which may be in various formats (text, markdown, etc.)
2. Extract all Chinese vocabulary items mentioned in the content
3. For each vocabulary item, provide:
   - The Chinese word
   - Its pinyin pronunciation
   - Part of speech (e.g., noun, verb, adjective)
   - Definitions (one or more meanings)
   - Examples with Chinese text, pinyin, and English translation
4. If the raw content doesn't explicitly provide all this information, use your knowledge to complete it
5. Format the output as a JSON array of vocabulary item objects

Output Format:
[
  {
    "word": "Chinese word",
    "pinyin": "Pinyin pronunciation",
    "partOfSpeech": "Part of speech (e.g., '名' for noun, '动' for verb)",
    "definitions": ["Definition 1", "Definition 2"],
    "examples": [
      {
        "chinese": "Example sentence in Chinese",
        "pinyin": "Pinyin for the example",
        "english": "English translation of the example"
      }
    ]
  }
]

The chapter number is ${chapterNumber}. Make sure all vocabulary items are correctly extracted and formatted.

IMPORTANT: If the content is not related to Chinese vocabulary or doesn't contain any identifiable vocabulary items, create at least 10-15 appropriate vocabulary items for Chapter ${chapterNumber} based on standard Chinese language curriculum.
`; // Removed: Please wrap the JSON output in <json> and </json> tags.
  }
}

// No unused interfaces

// Validate grammar points and add required fields
function validateGrammarPoints(
  data: unknown,
  chapterNumber: number
): GrammarPoint[] {
  if (!Array.isArray(data)) {
    throw new Error("Invalid response format: expected an array");
  }

  return data.map((point: unknown, index) => {
    // Type guard to check if point has the required structure
    if (!point || typeof point !== 'object') {
      throw new Error(`Invalid grammar point at index ${index}: not an object`);
    }

    const p = point as Record<string, unknown>;

    if (!p.title || typeof p.title !== 'string' ||
        !p.explanation || typeof p.explanation !== 'string' ||
        !p.examples || !Array.isArray(p.examples)) {
      throw new Error(`Invalid grammar point at index ${index}: missing required fields`);
    }

    // Generate a unique pointId
    const pointId = `ch${chapterNumber}_${p.title.replace(/\s+/g, '_')}`;

    // Determine difficulty based on chapter number
    const difficulty = getDifficulty(chapterNumber);

    // Process examples with proper type checking
    const examples = p.examples.map((example: unknown) => {
      if (!example || typeof example !== 'object') {
        return { chinese: "", pinyin: "", english: "" };
      }

      const e = example as Record<string, unknown>;

      return {
        chinese: typeof e.chinese === 'string' ? e.chinese : "",
        pinyin: typeof e.pinyin === 'string' ? e.pinyin : "",
        english: typeof e.english === 'string' ? e.english : ""
      };
    });

    // Handle relatedPoints with proper type checking
    let relatedPoints: string[] = [];
    if (p.relatedPoints && Array.isArray(p.relatedPoints)) {
      relatedPoints = p.relatedPoints
        .filter((item): item is string => typeof item === 'string');
    }

    return {
      chapterNumber,
      chapterTitle: `Chapter ${chapterNumber}`,
      pointId,
      title: p.title,
      explanation: p.explanation,
      examples,
      difficulty,
      relatedPoints
    };
  });
}

// Validate vocabulary items and add required fields
function validateVocabularyItems(
  data: unknown,
  chapterNumber: number
): VocabularyItem[] {
  if (!Array.isArray(data)) {
    throw new Error("Invalid response format: expected an array");
  }

  return data.map((item: unknown, index) => {
    // Type guard to check if item has the required structure
    if (!item || typeof item !== 'object') {
      throw new Error(`Invalid vocabulary item at index ${index}: not an object`);
    }

    const i = item as Record<string, unknown>;

    if (!i.word || typeof i.word !== 'string' ||
        !i.pinyin || typeof i.pinyin !== 'string' ||
        !i.partOfSpeech || typeof i.partOfSpeech !== 'string' ||
        !i.definitions || !Array.isArray(i.definitions) ||
        !i.examples || !Array.isArray(i.examples)) {
      throw new Error(`Invalid vocabulary item at index ${index}: missing required fields`);
    }

    // Generate a unique itemId
    const itemId = `ch${chapterNumber}_${i.word}`;

    // Determine difficulty based on chapter number
    const difficulty = getDifficulty(chapterNumber);

    // Process definitions with proper type checking
    const definitions = i.definitions
      .filter((def): def is string => typeof def === 'string');

    // Process examples with proper type checking
    const examples = i.examples.map((example: unknown) => {
      if (!example || typeof example !== 'object') {
        return { chinese: "", pinyin: "", english: "" };
      }

      const e = example as Record<string, unknown>;

      return {
        chinese: typeof e.chinese === 'string' ? e.chinese : "",
        pinyin: typeof e.pinyin === 'string' ? e.pinyin : "",
        english: typeof e.english === 'string' ? e.english : ""
      };
    });

    return {
      chapterNumber,
      chapterTitle: `Chapter ${chapterNumber}`,
      itemId,
      word: i.word,
      pinyin: i.pinyin,
      partOfSpeech: i.partOfSpeech,
      definitions,
      examples,
      difficulty
    };
  });
}

// Helper function to determine difficulty based on chapter number
export function getDifficulty(chapterNumber: number): number {
  if (chapterNumber <= 3) {
    return 1; // Beginner
  } else if (chapterNumber <= 6) {
    return 3; // Intermediate
  } else {
    return 5; // Advanced
  }
}
