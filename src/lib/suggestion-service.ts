import { z } from "zod";
import { llmService } from "../lib/llm-abstraction/llm-abstraction-service";
import { StructuredOutputOptions } from "../lib/llm-abstraction/types";
// import { GrammarPoint, VocabularyItem } from "@/types/content"; // Removed as GrammarPoint and VocabularyItem are not directly used in this file after refactor.

// Define the schema for word suggestions
const suggestionSchema = z.object({
  suggestions: z.array(z.object({
    word: z.string().describe("The suggested Chinese word or phrase"),
    pinyin: z.string().describe("The pinyin pronunciation of the word"),
    english: z.string().describe("English translation/meaning of the word"),
    confidence: z.number().min(0).max(1).describe("Confidence score for this suggestion"),
    inVocabulary: z.boolean().describe("Whether this word is in the provided vocabulary list"),
    grammarPoint: z.string().optional().describe("Related grammar point if applicable")
  })).describe("List of word suggestions")
});

interface SuggestionParams {
  currentText: string;
  vocabulary: string[];
  grammarPoints: string[];
  maxSuggestions?: number;
}

export async function getSuggestions(params: SuggestionParams) {
  const { currentText, vocabulary, grammarPoints, maxSuggestions = 5 } = params;

  const promptText = `
    As a Chinese language assistant, suggest the next word(s) that would naturally follow this text:

    Current text: ${currentText}

    Available vocabulary: ${vocabulary.join(", ")}
    Available grammar points: ${grammarPoints.join(", ")}

    Instructions:
    1. Analyze the current text and context
    2. Suggest up to ${maxSuggestions} possible next words or phrases
    3. Prioritize words from the provided vocabulary list
    4. If a suitable word isn't in the vocabulary, suggest the most natural option
    5. Consider grammar points when making suggestions
    6. Include confidence scores based on contextual appropriateness
    7. Mark whether each suggestion is from the provided vocabulary
    8. Add related grammar point if the suggestion follows a specific pattern

    Ensure your output is a valid JSON object matching the schema.
  `;

  try {
    const result = await llmService.generateStructuredOutput(
      promptText,
      {
        responseSchema: suggestionSchema,
        temperature: 0.3,
        maxSuggestions: maxSuggestions
      } as StructuredOutputOptions<typeof suggestionSchema> // Added type assertion for options
    );
    return result;
  } catch (error) {
    console.error('Error generating suggestions using LLMAbstractionService:', error);
    // It's good practice to re-throw or handle the error appropriately
    // For now, re-throwing to indicate failure to the caller
    throw new Error('Failed to generate suggestions via LLMAbstractionService.');
  }
}