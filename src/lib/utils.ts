import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Checks if a string contains Chinese characters.
 * @param text The string to check.
 * @returns True if the string contains Chinese characters, false otherwise.
 */
export function contains<PERSON>hinese(text: string): boolean {
  const chineseRegex = /\p{Script=Han}/u;
  return chineseRegex.test(text);
}

/**
 * Shuffles an array in place using the Fisher-Yates (<PERSON>h) algorithm.
 * @param array The array to shuffle.
 * @returns The shuffled array.
 */
export function shuffleArray<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
