import { 
  ChatPerformanceData, 
  CharacterMistakeFrequency, 
  ProblematicCharacter,
  AdaptiveLearningProfile,
  ChatProgressSummary,
  TranslationEvaluation,
  CharacterMistakeAnalysis
} from '@/types/chat';
import { localDB, STORES } from './localdb';

export class PerformanceTracker {
  
  /**
   * Save performance data for a chat session
   */
  async saveSessionPerformance(
    sessionId: string,
    userId: string,
    performanceData: ChatPerformanceData[]
  ): Promise<void> {
    try {
      // Save individual performance records
      for (const performance of performanceData) {
        const performanceRecord = {
          id: `${sessionId}_${performance.segmentIndex}`,
          sessionId,
          userId,
          segmentIndex: performance.segmentIndex,
          userTranslation: performance.userTranslation,
          correctTranslation: performance.evaluation.correctTranslation,
          isCorrect: performance.evaluation.isCorrect,
          score: performance.evaluation.score,
          feedback: performance.evaluation.feedback,
          timeSpent: performance.timeSpent,
          attempts: performance.attempts,
          createdAt: performance.timestamp.getTime()
        };

        await localDB.put(STORES.CHAT_PERFORMANCE, performanceRecord);
      }

      // Update adaptive learning data
      await this.updateAdaptiveLearningData(userId, performanceData);
    } catch (error) {
      console.error('Failed to save session performance:', error);
      throw error;
    }
  }

  /**
   * Analyze character mistakes from performance data
   */
  analyzeCharacterMistakes(performanceData: ChatPerformanceData[]): CharacterMistakeFrequency[] {
    const mistakeMap = new Map<string, {
      frequency: number;
      contexts: string[];
      lastOccurrence: Date;
      types: Set<string>;
    }>();

    performanceData.forEach(performance => {
      performance.evaluation.mistakeAnalysis.forEach(mistake => {
        const key = mistake.character;
        const existing = mistakeMap.get(key) || {
          frequency: 0,
          contexts: [],
          lastOccurrence: new Date(0),
          types: new Set()
        };

        existing.frequency += 1;
        existing.contexts.push(performance.userTranslation);
        existing.lastOccurrence = performance.timestamp > existing.lastOccurrence 
          ? performance.timestamp 
          : existing.lastOccurrence;
        existing.types.add(mistake.mistakeType);

        mistakeMap.set(key, existing);
      });
    });

    return Array.from(mistakeMap.entries()).map(([character, data]) => ({
      character,
      frequency: data.frequency,
      contexts: [...new Set(data.contexts)], // Remove duplicates
      lastOccurrence: data.lastOccurrence,
      improvementTrend: this.calculateImprovementTrend(character, performanceData)
    }));
  }

  /**
   * Calculate improvement trend for a character
   */
  private calculateImprovementTrend(
    character: string, 
    performanceData: ChatPerformanceData[]
  ): 'improving' | 'stable' | 'declining' {
    const mistakes = performanceData
      .filter(p => p.evaluation.mistakeAnalysis.some(m => m.character === character))
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    if (mistakes.length < 2) return 'stable';

    const firstHalf = mistakes.slice(0, Math.ceil(mistakes.length / 2));
    const secondHalf = mistakes.slice(Math.floor(mistakes.length / 2));

    const firstHalfRate = firstHalf.length / Math.max(firstHalf.length, 1);
    const secondHalfRate = secondHalf.length / Math.max(secondHalf.length, 1);

    if (secondHalfRate < firstHalfRate * 0.8) return 'improving';
    if (secondHalfRate > firstHalfRate * 1.2) return 'declining';
    return 'stable';
  }

  /**
   * Update adaptive learning data based on performance
   */
  async updateAdaptiveLearningData(
    userId: string, 
    performanceData: ChatPerformanceData[]
  ): Promise<void> {
    try {
      // Get existing adaptive learning data
      let adaptiveData = await localDB.get<any>(STORES.ADAPTIVE_LEARNING, userId);
      
      if (!adaptiveData) {
        adaptiveData = {
          id: userId,
          userId,
          problematicCharacters: {},
          learningPatterns: {
            commonMistakeTypes: [],
            difficultyProgression: 1,
            preferredTopics: []
          },
          lastUpdated: Date.now()
        };
      }

      // Analyze character mistakes
      const characterMistakes = this.analyzeCharacterMistakes(performanceData);
      
      // Update problematic characters
      characterMistakes.forEach(mistake => {
        const existing = adaptiveData.problematicCharacters[mistake.character] || {
          mistakeCount: 0,
          totalEncounters: 0,
          mistakeTypes: [],
          lastMistake: 0,
          masteryScore: 100,
          contexts: []
        };

        existing.mistakeCount += mistake.frequency;
        existing.totalEncounters += 1;
        existing.lastMistake = mistake.lastOccurrence.getTime();
        existing.contexts = [...new Set([...existing.contexts, ...mistake.contexts])];
        
        // Calculate mastery score (decreases with mistakes)
        const errorRate = existing.mistakeCount / Math.max(existing.totalEncounters, 1);
        existing.masteryScore = Math.max(0, Math.round(100 * (1 - errorRate)));

        adaptiveData.problematicCharacters[mistake.character] = existing;
      });

      // Update learning patterns
      const allMistakeTypes = performanceData.flatMap(p => 
        p.evaluation.mistakeAnalysis.map(m => m.mistakeType)
      );
      const mistakeTypeCounts = allMistakeTypes.reduce((acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      adaptiveData.learningPatterns.commonMistakeTypes = Object.entries(mistakeTypeCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([type]) => type);

      adaptiveData.lastUpdated = Date.now();

      await localDB.put(STORES.ADAPTIVE_LEARNING, adaptiveData);
    } catch (error) {
      console.error('Failed to update adaptive learning data:', error);
      throw error;
    }
  }

  /**
   * Get user's adaptive learning profile
   */
  async getAdaptiveLearningProfile(userId: string): Promise<AdaptiveLearningProfile | null> {
    try {
      const data = await localDB.get<any>(STORES.ADAPTIVE_LEARNING, userId);
      if (!data) return null;

      // Convert to AdaptiveLearningProfile format
      const problematicCharacters: ProblematicCharacter[] = Object.entries(data.problematicCharacters)
        .map(([character, info]: [string, any]) => ({
          character,
          mistakeCount: info.mistakeCount,
          totalEncounters: info.totalEncounters,
          masteryScore: info.masteryScore,
          mistakeTypes: info.mistakeTypes,
          contexts: info.contexts,
          firstEncounter: new Date(info.firstEncounter || Date.now()),
          lastMistake: new Date(info.lastMistake),
          improvementRate: this.calculateImprovementRate(info)
        }));

      // Get session statistics
      const sessions = await this.getUserSessions(userId);
      const totalSessions = sessions.length;
      const averageScore = sessions.length > 0 
        ? sessions.reduce((sum, s) => sum + s.overallScore, 0) / sessions.length 
        : 0;

      return {
        userId,
        totalSessions,
        averageScore,
        preferredTopics: data.learningPatterns.preferredTopics || [],
        difficultyLevel: this.determineDifficultyLevel(averageScore),
        problematicCharacters,
        learningVelocity: this.calculateLearningVelocity(problematicCharacters),
        lastUpdated: new Date(data.lastUpdated)
      };
    } catch (error) {
      console.error('Failed to get adaptive learning profile:', error);
      return null;
    }
  }

  /**
   * Get user's chat sessions
   */
  async getUserSessions(userId: string): Promise<any[]> {
    try {
      const allSessions = await localDB.getAll<any>(STORES.CHAT_SESSIONS);
      return allSessions.filter(session => session.userId === userId);
    } catch (error) {
      console.error('Failed to get user sessions:', error);
      return [];
    }
  }

  /**
   * Calculate improvement rate for a character
   */
  private calculateImprovementRate(characterInfo: any): number {
    if (characterInfo.totalEncounters === 0) return 0;
    
    const errorRate = characterInfo.mistakeCount / characterInfo.totalEncounters;
    return Math.max(0, 1 - errorRate);
  }

  /**
   * Determine difficulty level based on average score
   */
  private determineDifficultyLevel(averageScore: number): 'beginner' | 'intermediate' | 'advanced' {
    if (averageScore >= 80) return 'advanced';
    if (averageScore >= 60) return 'intermediate';
    return 'beginner';
  }

  /**
   * Calculate learning velocity (characters mastered per session)
   */
  private calculateLearningVelocity(problematicCharacters: ProblematicCharacter[]): number {
    const masteredCharacters = problematicCharacters.filter(c => c.masteryScore >= 80).length;
    const totalSessions = Math.max(1, problematicCharacters.length);
    return masteredCharacters / totalSessions;
  }

  /**
   * Get performance summary for a session
   */
  async getSessionSummary(sessionId: string): Promise<ChatProgressSummary | null> {
    try {
      const session = await localDB.get<any>(STORES.CHAT_SESSIONS, sessionId);
      if (!session) return null;

      const performanceRecords = await localDB.getAll<any>(STORES.CHAT_PERFORMANCE);
      const sessionPerformance = performanceRecords.filter(p => p.sessionId === sessionId);

      const charactersMistaken = this.analyzeCharacterMistakes(
        sessionPerformance.map(p => ({
          segmentIndex: p.segmentIndex,
          userTranslation: p.userTranslation,
          evaluation: {
            score: p.score,
            isCorrect: p.isCorrect,
            feedback: p.feedback,
            correctTranslation: p.correctTranslation,
            mistakeAnalysis: [], // Would need to be stored separately
            suggestions: [],
            contextTips: []
          },
          timeSpent: p.timeSpent,
          attempts: p.attempts,
          timestamp: new Date(p.createdAt)
        }))
      );

      return {
        sessionId,
        topic: session.topic,
        totalSegments: session.totalSegments,
        completedSegments: session.completedSegments,
        overallScore: session.overallScore,
        timeSpent: sessionPerformance.reduce((sum, p) => sum + p.timeSpent, 0),
        charactersLearned: [], // TODO: Extract from performance analysis
        charactersMistaken,
        improvementAreas: [], // TODO: Extract from mistake analysis
        strengths: [] // TODO: Extract from successful patterns
      };
    } catch (error) {
      console.error('Failed to get session summary:', error);
      return null;
    }
  }
}

export const performanceTracker = new PerformanceTracker();
