/**
 * Attempts to extract a JSON string from a given text, looking for content
 * wrapped in ```json and ``` markers.
 *
 * @param text The input text potentially containing JSON.
 * @returns The extracted JSON string, or null if no valid JSON block is found.
 */
export function extractJsonFromText(text: string): string | null {
  // Regex to find content between ```json and ```, non-greedily
  const regex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(regex);

  if (match && match[1]) {
    // Return the captured content (the JSON string)
    return match[1];
  }

  // If no match is found, return null
  return null;
}

/**
 * Attempts to parse a JSON string, returning null if parsing fails.
 *
 * @param jsonString The JSON string to parse.
 * @returns The parsed JSON object, or null if parsing fails.
 */
export function safeJsonParse<T>(jsonString: string): T | null {
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error("Failed to parse JSON string:", error);
    return null;
  }
}