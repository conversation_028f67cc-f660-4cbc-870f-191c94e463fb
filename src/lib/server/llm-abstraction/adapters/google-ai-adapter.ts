import { createGoogleGenerative<PERSON><PERSON> } from '@ai-sdk/google';
import { generateObject, generateText } from 'ai';
import { type ZodSchema, type z } from 'zod';
import { type LLMAdapter } from './adapter-interface';
import { type GenerationOptions, type ProviderConfig } from '../types';
import { extractJsonFromText, parseAndValidateJson } from '../json-processor';

/**
 * Adapter for Google AI (Gemini) models.
 * Implements the LLMAdapter interface to provide a consistent way
 * to interact with Google's generative AI capabilities.
 */
export class GoogleAIAdapter implements LLMAdapter {
  private providerConfig: ProviderConfig;

  /**
   * Initializes the GoogleAIAdapter with provider-specific configuration.
   * @param providerConfig Configuration for the Google AI provider, including API key and model ID.
   */
  constructor(providerConfig: ProviderConfig) {
    if (!providerConfig.apiKey) {
      throw new Error('Google AI API key is required in ProviderConfig.');
    }
    this.providerConfig = providerConfig;
  }

  /**
   * Generates structured content using Google AI.
   * @param prompt The input prompt.
   * @param schema The Zod schema for the desired output structure.
   * @param options Generation options (e.g., temperature, maxTokens).
   * @returns A promise that resolves to the structured data.
   */
  async generateStructured<S extends ZodSchema>(
    prompt: string,
    schema: S,
    options: GenerationOptions
    // providerConfig is available via this.providerConfig. Note: User instruction mentioned providerConfig as param,
    // but existing code uses this.providerConfig. Sticking to this.providerConfig.
  ): Promise<z.infer<S>> {
    try {
      const googleProvider = createGoogleGenerativeAI({
        apiKey: this.providerConfig.apiKey,
      });
      const model = googleProvider(this.providerConfig.modelId);

      if (this.providerConfig.supportsJsonMode) {
        const result = await generateObject({
          model,
          schema,
          prompt,
          ...options, // Spread options directly
        });
        return result.object;
      } else {
        const augmentedPrompt = `Your response must be a valid JSON object. Please wrap the entire JSON output in \`\`\`json and \`\`\` tags.\n\n${prompt}`;
        
        const textResult = await generateText({ // Using the imported generateText
          model,
          prompt: augmentedPrompt,
          ...options, // Spread options directly
        });
        const rawText = textResult.text;

        const jsonString = extractJsonFromText(rawText);
        if (!jsonString) {
          throw new Error('Failed to extract JSON from LLM text response.');
        }
        return parseAndValidateJson(jsonString, schema);
      }
    } catch (error) {
      console.error('Error generating structured content with Google AI:', error);
      throw new Error(
        `Google AI structured generation failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Generates plain text content using Google AI.
   * @param prompt The input prompt.
   * @param options Generation options (e.g., temperature, maxTokens).
   * @returns A promise that resolves to the generated text string.
   */
  async generateText(
    prompt: string,
    options: GenerationOptions
    // providerConfig is available via this.providerConfig
  ): Promise<string> {
    try {
      const googleProvider = createGoogleGenerativeAI({
        apiKey: this.providerConfig.apiKey,
      });

      // Correct: Call the provider instance with the model ID
      const model = googleProvider(this.providerConfig.modelId);

      const result = await generateText({
        model,
        prompt,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        // TODO: Map other options from GenerationOptions to Google's API if applicable
      });
      return result.text;
    } catch (error) {
      console.error('Error generating text content with Google AI:', error);
      throw new Error(`Google AI text generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}