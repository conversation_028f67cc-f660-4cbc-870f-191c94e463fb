import { type ZodSchema, type z } from 'zod';
import { type GenerationOptions, type ProviderConfig } from '../types';

/**
 * Interface for LLM adapters.
 * Defines the contract for interacting with different LLM providers.
 */
export interface LLMAdapter {
  /**
   * Generates structured content based on a prompt and a Zod schema.
   * @param prompt The input prompt for the LLM.
   * @param schema The Zod schema to validate and type the output.
   * @param options Generation options like temperature, maxTokens, etc.
   * @param providerConfig Configuration specific to the LLM provider.
   * @returns A promise that resolves to the structured data matching the schema.
   */
  generateStructured<S extends ZodSchema>(
    prompt: string,
    schema: S,
    options: GenerationOptions,
    providerConfig: ProviderConfig
  ): Promise<z.infer<S>>;

  /**
   * Generates plain text content based on a prompt.
   * @param prompt The input prompt for the LLM.
   * @param options Generation options like temperature, maxTokens, etc.
   * @param providerConfig Configuration specific to the LLM provider.
   * @returns A promise that resolves to the generated text string.
   */
  generateText(
    prompt: string,
    options: GenerationOptions,
    providerConfig: ProviderConfig
  ): Promise<string>;
}