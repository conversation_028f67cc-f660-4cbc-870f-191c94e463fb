import { LLMServiceConfig, ProviderConfig } from "./types";

// Default model ID for Google AI if not specified in environment variables
const DEFAULT_GOOGLE_MODEL_ID = "gemini-1.5-flash-latest";

/**
 * Loads LLM configuration from environment variables.
 * Currently supports Google AI configuration.
 *
 * @returns The loaded LLM service configuration.
 * @throws Error if required environment variables (e.g., GOOGLE_API_KEY) are missing.
 */
export function loadLLMConfig(): LLMServiceConfig {
  const googleApiKey = process.env.GEMINI_API_KEY ?? process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  const googleModelId = process.env.NEXT_PUBLIC_GOOGLE_AI_MODEL ?? process.env.GOOGLE_DEFAULT_MODEL_ID ?? DEFAULT_GOOGLE_MODEL_ID;
  const googleSupportsJsonModeEnv = process.env.NEXT_PUBLIC_GOOGLE_SUPPORTS_JSON_MODE;

  let googleConfig: ProviderConfig | undefined = undefined;

  if (googleApiKey) {
    if (googleSupportsJsonModeEnv === undefined) {
      throw new Error(
        "NEXT_PUBLIC_GOOGLE_SUPPORTS_JSON_MODE environment variable is not set. This is required for Google AI provider."
      );
    }

    let supportsJsonMode: boolean;
    if (googleSupportsJsonModeEnv.toLowerCase() === "true") {
      supportsJsonMode = true;
    } else if (googleSupportsJsonModeEnv.toLowerCase() === "false") {
      supportsJsonMode = false;
    } else {
      throw new Error(
        `Invalid boolean value for NEXT_PUBLIC_GOOGLE_SUPPORTS_JSON_MODE: "${googleSupportsJsonModeEnv}". Expected "true" or "false".`
      );
    }

    googleConfig = {
      provider: "google",
      modelId: googleModelId,
      apiKey: googleApiKey,
      supportsJsonMode: supportsJsonMode,
    };
  } else {
    // Optionally, you could choose to not throw an error here if Google AI is not
    // the only provider or if it's optional. For now, we'll make it clear
    // if the primary expected config is missing.
    // Consider if a warning or a more flexible fallback is needed in the future.
    console.warn(
      "GOOGLE_API_KEY environment variable is not set. Google AI provider will not be available."
    );
  }

  const config: LLMServiceConfig = {};
  if (googleConfig) {
    config.google = googleConfig;
  }

  // Basic validation: Ensure at least one provider is configured if the service expects it.
  // For this initial setup, we are focusing on Google.
  // If no providers are configured, the service might not be usable.
  if (!config.google /* && !config.openai && ... */) {
    // This warning is more general. If specific providers are *required*,
    // the checks should be more specific, like the GOOGLE_API_KEY check.
    console.warn(
      "No LLM providers seem to be configured via environment variables. The LLM service may not function."
    );
  }


  return config;
}

// Load the configuration once when the module is imported.
export const llmConfig: LLMServiceConfig = loadLLMConfig();

/**
 * Retrieves the configuration for a specific provider.
 *
 * @param providerName The name of the provider (e.g., "google").
 * @returns The ProviderConfig if found, otherwise undefined.
 */
export function getProviderConfig(providerName: keyof LLMServiceConfig): ProviderConfig | undefined {
    return llmConfig[providerName];
}

/**
 * Retrieves the Google AI provider configuration.
 *
 * @returns The Google ProviderConfig if configured, otherwise undefined.
 * @throws Error if Google API key is not configured (this behavior can be adjusted).
 */
export function getGoogleConfig(): ProviderConfig {
  if (!llmConfig.google || !llmConfig.google.apiKey) {
    throw new Error(
      "Google AI provider is not configured. Missing GOOGLE_API_KEY."
    );
  }
  return llmConfig.google;
}