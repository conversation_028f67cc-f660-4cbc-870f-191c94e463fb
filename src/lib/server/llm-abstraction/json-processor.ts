import { z, ZodSchema } from "zod";

/**
 * Extracts a JSON string from the input text.
 * Prioritizes extracting content wrapped in ```json ... ``` code blocks.
 *
 * @param text The input string to search for JSON.
 * @returns The extracted JSON string, or null if no valid block is found.
 */
export function extractJsonFromText(text: string): string | null {
  const regex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(regex);
  if (match && match[1]) {
    return match[1].trim();
  }
  // Optional: Fallback to find largest substring starting with { and ending with } or [ and ]
  // For now, only the ```json ... ``` block is implemented.
  return null;
}

/**
 * Parses a JSON string and validates it against a Zod schema.
 *
 * @param jsonString The JSON string to parse.
 * @param schema The Zod schema to validate against.
 * @returns A promise that resolves with the validated data.
 * @throws Error if parsing or validation fails.
 */
export async function parseAndValidateJson<S extends ZodSchema>(
  jsonString: string,
  schema: S
): Promise<z.infer<S>> {
  let parsedObject: unknown;
  try {
    parsedObject = JSON.parse(jsonString);
  } catch (error) {
    throw new Error(`Failed to parse JSON string: ${error instanceof Error ? error.message : String(error)}`);
  }

  const validationResult = await schema.safeParseAsync(parsedObject);

  if (!validationResult.success) {
    throw new Error(`JSON validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`);
  }

  return validationResult.data;
}