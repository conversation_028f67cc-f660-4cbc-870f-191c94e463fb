import { z, ZodSchema } from "zod";

// Configuration for a specific LLM model
export interface LLMModelConfig {
  provider: string; // e.g., "google", "openai"
  modelId: string;  // e.g., "gemini-2.0-flash", "gpt-4"
  apiKey?: string; // Can be centrally managed or overridden
  // Other provider-specific options
}

// Configuration for a specific LLM provider instance
export interface ProviderConfig {
  provider: string; // e.g., "google", "openai"
  modelId: string;  // e.g., "gemini-1.5-flash-latest", "gpt-4"
  apiKey: string;   // API key for the provider
  // Other provider-specific options can be added here
  supportsJsonMode: boolean;
}

// Top-level configuration for the LLM Service
export interface LLMServiceConfig {
  google?: ProviderConfig; // Configuration for Google AI
  // We can add other providers here later, e.g., openai?: ProviderConfig;
  // defaultProvider?: string; // Optional: to specify which provider to use if not specified in a request
}
// Options for a generation request
export interface GenerationOptions {
  modelConfig?: LLMModelConfig; // Specify a model, or let the service use a default
  temperature?: number;
  maxTokens?: number;
  promptVariables?: Record<string, unknown>; // For prompt templating
  // retryOptions?: RetryConfig; // For resiliency
}

export interface StructuredOutputOptions<T extends ZodSchema> extends GenerationOptions {
  responseSchema: T;
}

export interface LLMService {
  /**
   * Generates structured JSON output from an LLM, validated against a Zod schema.
   * Handles native JSON modes and robust extraction for other models.
   * @param prompt The base prompt or a prompt template key.
   * @param options Options including the Zod schema for the expected response.
   * @returns A promise that resolves to the validated structured data.
   */
  generateStructuredOutput: <T extends ZodSchema>(
    prompt: string,
    options: StructuredOutputOptions<T>
  ) => Promise<z.infer<T>>;

  /**
   * Generates raw text output from an LLM.
   * @param prompt The base prompt or a prompt template key.
   * @param options Optional generation parameters.
   * @returns A promise that resolves to the raw text string.
   */
  generateRawText: (
    prompt: string,
    options?: GenerationOptions
  ) => Promise<string>;

  // Future considerations:
  // streamStructuredOutput?: <T extends ZodSchema>(...) => AsyncIterable<Partial<z.infer<T>>>;
  // streamRawText?: (...) => AsyncIterable<string>;
}