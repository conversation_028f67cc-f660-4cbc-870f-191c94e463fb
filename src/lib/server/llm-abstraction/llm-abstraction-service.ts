import { ZodSchema } from 'zod';
import { LLMService, StructuredOutputOptions, GenerationOptions } from '@/lib/server/llm-abstraction/types';
import { getGoogleConfig } from '@/lib/server/llm-abstraction/config';
import { GoogleAIAdapter } from '@/lib/server/llm-abstraction/adapters/google-ai-adapter';

export class LLMAbstractionService implements LLMService {
  constructor() {
    // Constructor can be expanded later if needed for initial setup
  }

  async generateStructuredOutput<S extends ZodSchema>(
    prompt: string,
    options: StructuredOutputOptions<S>
  ): Promise<S['_output']> {
    const googleConfig = getGoogleConfig();
    if (!googleConfig) {
      throw new Error('Google AI provider configuration not found.');
    }

    const adapter = new GoogleAIAdapter(googleConfig);
    return adapter.generateStructured(prompt, options.responseSchema, options);
  }

  async generateRawText(
    prompt: string,
    options?: GenerationOptions
  ): Promise<string> {
    const googleConfig = getGoogleConfig();
    if (!googleConfig) {
      throw new Error('Google AI provider configuration not found.');
    }

    const adapter = new GoogleAIAdapter(googleConfig);
    return adapter.generateText(prompt, options || {});
  }
}

export const llmService = new LLMAbstractionService();
