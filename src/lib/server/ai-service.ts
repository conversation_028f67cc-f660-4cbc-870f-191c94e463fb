import { z } from "zod";
import { GrammarPoint, VocabularyItem } from "@/types/content";
import { llmService } from "../server/llm-abstraction/llm-abstraction-service"; // Updated path
import { StructuredOutputOptions } from "../server/llm-abstraction/types"; // Updated path
import {
  ChatDialogueRequest,
  GeneratedDialogue,
  TranslationEvaluation,
  CharacterMistakeAnalysis
} from "@/types/chat";

// Define Difficulty type as it's used in the new function
export type StoryDifficulty = 'easy' | 'medium' | 'hard';

// Schemas for Story Adventure Game
const questionTypeEnum = z.enum(['multiple-choice', 'true-false']);

const questionSchema = z.object({
  id: z.string().describe("A unique ID for the question (e.g., 'q1', 'q2')."),
  type: questionTypeEnum,
  questionText: z.string().describe("The text of the question."),
  options: z.array(z.string()).optional().describe("An array of 3-4 options for multiple-choice questions."),
  correctAnswer: z.union([z.string(), z.boolean()]).describe("The correct answer. String for multiple-choice, boolean for true-false."),
});

export const storyDataSchema = z.object({
  storyText: z.string().describe("The generated story text."),
  theme: z.string().describe("The theme of the story (e.g., 'A Day at the Park')."),
  questions: z.array(questionSchema).min(3).max(5).describe("An array of 3-5 comprehension questions based on the story."),
  generatedHints: z.array(z.string()).optional().describe("An array of 2-3 AI-generated hints related to grammar points or vocabulary used in the story.")
});

export type StoryData = z.infer<typeof storyDataSchema>;
export type Question = z.infer<typeof questionSchema>;


export interface Exercise {
  prompt: string;
  options?: string[];
  correctAnswer: string;
  type: string;
  explanation: string;
  jumbledWords?: string[]; // Added for sentence-ordering
}



// Define schemas for AI responses
const exerciseTypesEnum = z.enum([
  "fill-in-blank",
  "multiple-choice",
  "translation",
  "sentence-formation",
  "sentence-ordering", // Added new type
]);

const exerciseSchema = z.object({
  type: exerciseTypesEnum,
  prompt: z.string().describe("The exercise prompt. For sentence-ordering, this might be an instruction like 'Arrange the words to form a correct sentence.' For translation, this will be the source sentence (e.g., in Chinese)."),
  options: z.array(z.string()).optional(), // Only for multiple-choice
  jumbledWords: z.array(z.string()).optional().describe("An array of jumbled words or phrases for sentence-ordering exercises."),
  correctAnswer: z.string().describe("The correct answer. For sentence-ordering, this is the correctly ordered sentence. For translation, this is the target translation (e.g., in English)."),
  explanation: z.string().describe("Explanation of the correct answer."),
});

const validationSchema = z.object({
  score: z.number().int().min(0).max(100).describe("Score between 0 and 100"),
  feedback: z.string().describe("Detailed feedback explaining the score"),
});

// Schemas for Interactive Chat
export const EvaluationSchema = z.object({
  score: z.number().min(0).max(100).describe("Score from 0 to 100 evaluating the user's application of the grammar point."),
  feedback: z.string().describe("Detailed feedback on the user's message, focusing on grammar usage and correctness."),
});

export const ChatTurnSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
  evaluation: EvaluationSchema.optional().describe("Evaluation of the user's message, if the role is 'user' and it has been evaluated."),
});

export const AIResponseSchema = z.object({
  evaluation: EvaluationSchema.optional().describe("Evaluation of the user's previous message. This is present when the AI is responding to a user."),
  nextAIMessage: z.string().describe("The AI's next message in the conversation to prompt further practice."),
});

// Schemas for Translation Chat Game
export const TranslationChatQuestionSchema = z.object({
  chineseSentence: z.string().describe("The Chinese sentence for the user to translate."),
  targetVocabulary: z.array(z.string()).optional().describe("Vocabulary items from the provided list used in the sentence."),
  targetGrammar: z.array(z.string()).optional().describe("Grammar points from the provided list used in the sentence."),
  hskLevel: z.number().int().min(1).max(6).optional().describe("The estimated HSK level of the sentence, primarily based on non-target vocabulary."),
});

export const TranslationChatEvaluationSchema = z.object({
  score: z.number().int().min(0).max(100).describe("Score between 0 and 100 for the translation accuracy."),
  feedback: z.string().describe("Detailed feedback on the user's translation, including correctness, vocabulary, and grammar usage."),
  vocabularyMastery: z.record(z.string(), z.number().int().min(0).max(100)).optional().describe("Mastery score for specific vocabulary items used in the sentence."),
  grammarMastery: z.record(z.string(), z.number().int().min(0).max(100)).optional().describe("Mastery score for specific grammar points used in the sentence."),
});

export const TranslationChatAIResponseSchema = z.object({
  question: TranslationChatQuestionSchema.optional().describe("The next Chinese sentence for the user to translate."),
  evaluation: TranslationChatEvaluationSchema.optional().describe("Evaluation of the user's previous translation."),
  nextAIMessage: z.string().optional().describe("An optional conversational message from the AI, e.g., encouragement or a transition."),
});

export const TranslationChatGameSummarySchema = z.object({
  overallFeedback: z.string().describe("Overall feedback on the user's performance throughout the game."),
  vocabularyProgressSummary: z.record(z.string(), z.object({
    correctAttempts: z.number().int(),
    totalAttempts: z.number().int(),
    masteryScore: z.number().int().min(0).max(100),
  })).optional().describe("Summary of progress for each vocabulary item."),
  grammarProgressSummary: z.record(z.string(), z.object({
    correctAttempts: z.number().int(),
    totalAttempts: z.number().int(),
    masteryScore: z.number().int().min(0).max(100),
  })).optional().describe("Summary of progress for each grammar point."),
});
// End Schemas for Translation Chat Game

interface GenerateExerciseParams {
  item: GrammarPoint | VocabularyItem;
  type: string; // "grammar" or "vocabulary"
}

interface ValidateAnswerParams {
  exercise: Exercise;
  userAnswer: string;
}

export async function generateExercise(params: GenerateExerciseParams) {
  const { item, type } = params;

  // Define type guards within function scope
  const isGrammarItem = (item: unknown): item is GrammarPoint => (
    !!item &&
    typeof item === "object" &&
    "title" in item &&
    "explanation" in item
  );

  const isVocabularyItem = (item: unknown): item is VocabularyItem => (
    !!item &&
    typeof item === "object" &&
    "word" in item &&
    "definitions" in item
  );

  const availableExerciseTypes = [
    "fill-in-blank",
    "multiple-choice",
    "translation",
    "sentence-formation",
    "sentence-ordering", // Added new type
  ];
  const selectedExerciseType =
    availableExerciseTypes[Math.floor(Math.random() * availableExerciseTypes.length)];

  let promptText = "";

  const basePromptInfo = `
    Grammar point: ${isGrammarItem(item) ? item.title : "N/A"}
    Explanation: ${isGrammarItem(item) ? item.explanation : "N/A"}
    Examples:
    ${isGrammarItem(item) ? item.examples.map((ex) => `- ${ex.chinese} (${ex.pinyin}): ${ex.english}`).join("\n") : "N/A"}

    Word: ${isVocabularyItem(item) ? item.word : "N/A"}
    Pinyin: ${isVocabularyItem(item) ? item.pinyin : "N/A"}
    Part of speech: ${isVocabularyItem(item) ? item.partOfSpeech : "N/A"}
    Definitions: ${isVocabularyItem(item) && item.definitions ? item.definitions.join(", ") : "N/A"}
    Vocabulary Examples:
    ${isVocabularyItem(item) && item.examples ? item.examples.map((ex) => `- ${ex.chinese} (${ex.pinyin}): ${ex.english}`).join("\n") : "N/A"}
  `;

  if (selectedExerciseType === "sentence-ordering") {
    promptText = `
      Generate a "sentence-ordering" exercise based on the following ${type} content:
      ${basePromptInfo}

      The exercise should consist of a set of jumbled Chinese words or phrases that, when correctly arranged, form a meaningful sentence related to the provided ${type} content.

      Provide the following in a single JSON object:
      1. "type": "sentence-ordering"
      2. "prompt": A clear instruction for the user, e.g., "Unscramble the following words to form a correct Chinese sentence."
      3. "jumbledWords": An array of strings, where each string is a word or phrase in Chinese.
      4. "correctAnswer": The correctly ordered Chinese sentence.
      5. "explanation": A brief explanation of why the sentence is correct, or a translation of the correct sentence.

      Ensure the jumbled words are genuinely mixed up and the correct sentence is grammatically sound and relevant.
      The JSON schema is: { "type": "sentence-ordering", "prompt": string, "jumbledWords": string[], "correctAnswer": string, "explanation": string }.
    `;
  } else if (selectedExerciseType === "translation") {
    promptText = `
      Generate a "translation" exercise based on the following ${type} content:
      ${basePromptInfo}

      The exercise should require the user to translate a Chinese sentence into English.

      Provide the following in a single JSON object:
      1. "type": "translation"
      2. "prompt": A Chinese sentence that is relevant to the provided ${type} content. This is the sentence the user needs to translate.
      3. "correctAnswer": The correct English translation of the Chinese sentence provided in "prompt".
      4. "explanation": A brief explanation of any nuances in the translation, or context for the Chinese sentence.

      Ensure the Chinese sentence is natural and clearly demonstrates the ${type} content. The English translation must be accurate.
      The JSON schema is: { "type": "translation", "prompt": string, "correctAnswer": string, "explanation": string }.
    `;
  }
  else {
    // Existing logic for other exercise types
    if (type === "grammar" && isGrammarItem(item)) {
      promptText = `
        Generate a ${selectedExerciseType} exercise for the following Chinese grammar point:
        ${basePromptInfo}
        Generate a single JSON object for the exercise, matching the schema: { "type": "${selectedExerciseType}", "prompt": string, ${selectedExerciseType === "multiple-choice" ? '"options": string[], ' : ''}"correctAnswer": string, "explanation": string }.
      `;
    } else if (type === "vocabulary" && isVocabularyItem(item)) {
      promptText = `
        Generate a ${selectedExerciseType} exercise for the following Chinese vocabulary word:
        ${basePromptInfo}
        Generate a single JSON object for the exercise, matching the schema: { "type": "${selectedExerciseType}", "prompt": string, ${selectedExerciseType === "multiple-choice" ? '"options": string[], ' : ''}"correctAnswer": string, "explanation": string }.
      `;
    } else {
      throw new Error("Invalid content type or item structure");
    }
  }

  const options: StructuredOutputOptions<typeof exerciseSchema> = {
    responseSchema: exerciseSchema,
    temperature: 0.7,
  };
  return llmService.generateStructuredOutput(promptText, options);
}

export async function validateAnswer(params: ValidateAnswerParams) {
  const { exercise, userAnswer } = params;

  // For multiple-choice, validation is done client-side
  if (exercise.type === "multiple-choice") {
    const isAnswerCorrect = userAnswer === exercise.correctAnswer;
    return {
      score: isAnswerCorrect ? 100 : 0,
      feedback: isAnswerCorrect
        ? "Correct! Well done."
        : `Incorrect. The correct answer is: ${exercise.correctAnswer}`,
    };
  }

  // For sentence-ordering and other AI-validated types
  let validationPromptText = "";
  if (exercise.type === "sentence-ordering") {
    validationPromptText = `
      Exercise Type: Sentence Ordering
      Task: The user was asked to arrange a set of jumbled words/phrases into a correct Chinese sentence.
      Jumbled Words (if available, for context, though not strictly needed for validation if correctAnswer is robust): ${exercise.jumbledWords ? exercise.jumbledWords.join(", ") : "Not provided"}
      Correctly Ordered Sentence: ${exercise.correctAnswer}
      User's Attempted Sentence: ${userAnswer}

      Evaluate the user's attempted sentence against the correctly ordered sentence.
      Provide:
      1. "score": An integer score from 0 to 100.
         - 100 if the user's sentence exactly matches the correct answer or is a perfectly valid alternative.
         - Partial scores for sentences that are mostly correct but have minor errors in word order or missing/extra minor words.
         - Lower scores for significant deviations.
      2. "feedback": Detailed feedback explaining the score.
         - If correct, confirm and perhaps offer a translation or a note on why it's correct.
         - If incorrect or partially correct, pinpoint the errors in word order. For example: "You have the first few words correct, but the order of [word X] and [word Y] should be swapped." or "The phrase '[user's incorrect phrase]' should be '[correct phrase]'."
         - Be encouraging and constructive.

      Return the evaluation as a single JSON object matching the schema: { "score": number, "feedback": string }.
    `;
  } else if (exercise.type === "translation") {
    validationPromptText = `
      Exercise Type: Translation (Chinese to English)
      Task: The user was asked to translate a Chinese sentence into English.
      Chinese Sentence (Source): ${exercise.prompt}
      Correct English Translation (Target): ${exercise.correctAnswer}
      User's Submitted English Translation: ${userAnswer}

      Evaluate the user's submitted English translation against the correct English translation.
      Provide:
      1. "score": An integer score from 0 to 100.
         - 100 for a perfect or near-perfect translation that accurately conveys the meaning.
         - High partial scores (70-99) for translations that are largely correct but may have minor grammatical errors, slightly awkward phrasing, or minor inaccuracies that don't significantly alter the core meaning.
         - Medium partial scores (40-69) for translations that capture some of the meaning but have significant grammatical errors, incorrect word choices, or miss key nuances.
         - Low scores (0-39) for translations that are mostly incorrect, miss the main idea, or are unintelligible.
      2. "feedback": Detailed feedback explaining the score.
         - If correct (score 90-100), confirm and praise. You can also offer alternative correct phrasings if appropriate.
         - If partially correct, pinpoint specific errors in grammar, word choice, or meaning. For example: "Your translation captures the main idea, but the verb tense is incorrect here." or "The word '[user's word]' isn't the best fit; consider '[better word]' for '[nuance]'." or "The phrase '[user's phrase]' changes the meaning slightly; the original implies [original nuance]."
         - Provide corrections for key errors.
         - Be encouraging and constructive, focusing on helping the user learn.

      Return the evaluation as a single JSON object matching the schema: { "score": number, "feedback": string }.
    `;
  }
  else {
    // Existing prompt for other AI-validated types
    validationPromptText = `
      Exercise type: ${exercise.type}
      Exercise prompt: ${exercise.prompt}
      User's answer: ${userAnswer}
      Correct answer: ${exercise.correctAnswer}

      Evaluate if the user's answer is correct. Consider variations and alternative correct answers.
      Provide a score from 0-100 and specific feedback on any errors.

      Return the evaluation as a single JSON object matching the schema: { "score": number, "feedback": string }.
    `;
  }

  const options: StructuredOutputOptions<typeof validationSchema> = {
    responseSchema: validationSchema,
    temperature: 0.3,
  };
  return llmService.generateStructuredOutput(validationPromptText, options);
}

// Functions for Interactive Chat

/**
 * Starts a chat exercise for a given grammar point.
 * The AI will initiate the conversation.
 */
export async function startChatExercise(
  grammarPoint: GrammarPoint,
): Promise<z.infer<typeof AIResponseSchema>> {
  const systemPrompt = `You are an AI language tutor. Your goal is to help the user practice a specific Chinese grammar point through an interactive conversation.
The user wants to practice:
Grammar Point: ${grammarPoint.title}
Explanation: ${grammarPoint.explanation}
Examples:
${grammarPoint.examples.map((ex) => `- ${ex.chinese} (${ex.pinyin}): ${ex.english}`).join("\n")}

Initiate a conversation with the user. Your first message should be engaging and designed to naturally lead the user to apply the grammar point: "${grammarPoint.title}".
Do not evaluate anything yet, as this is the start of the conversation.
Your response must be a JSON object matching the following schema:
{
  "nextAIMessage": "Your engaging opening message to the user"
}
The "evaluation" field should be omitted for this initial message.`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: AIResponseSchema,
  });
}

/**
 * Continues a chat exercise based on the history and user's latest answer.
 * The AI will evaluate the user's answer and provide the next conversational turn.
 */
export async function continueChat(
  grammarPoint: GrammarPoint,
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  userAnswer: string,
): Promise<z.infer<typeof AIResponseSchema>> {
  const formattedChatHistory = chatHistory
    .map(
      (turn) =>
        `${turn.role === "user" ? "User" : "Assistant"}: ${turn.content}${
          turn.evaluation
            ? ` (Evaluation: Score ${turn.evaluation.score}, Feedback: ${turn.evaluation.feedback})`
            : ""
        }`
    )
    .join("\n");

  const systemPrompt = `You are an AI language tutor. You are continuing a conversation with a user to help them practice a specific Chinese grammar point.

Grammar Point to Practice: ${grammarPoint.title}
Explanation: ${grammarPoint.explanation}
Examples:
${grammarPoint.examples.map((ex) => `- ${ex.chinese} (${ex.pinyin}): ${ex.english}`).join("\n")}

Conversation History:
${formattedChatHistory}

User's Latest Message: "${userAnswer}"

Your tasks are:
1.  Evaluate the user's latest message ("${userAnswer}") based on:
    *   Correctness of Chinese grammar.
    *   Relevance to the conversation.
    *   Effective and accurate use of the target grammar point: "${grammarPoint.title}".
    *   If the user did not attempt to use the grammar point, gently guide them to do so.
2.  Formulate an "evaluation" object for the user's message. This object must include:
    *   "score": An integer from 0 to 100. 100 for perfect use of grammar and relevance, lower scores for errors or lack of attempt.
    *   "feedback": Detailed, constructive feedback. Explain the score. If there are errors, explain them clearly. If the grammar point was used well, acknowledge it. If it wasn't used, suggest how it could be incorporated.
3.  Formulate a "nextAIMessage". This message should:
    *   Naturally continue the conversation.
    *   Subtly guide the user to further practice the grammar point "${grammarPoint.title}" in a new context or a variation.
    *   Be engaging and encouraging.

Your response MUST be a JSON object matching the following schema:
{
  "evaluation": {
    "score": number (0-100),
    "feedback": "string"
  },
  "nextAIMessage": "string"
}

Focus on providing high-quality, specific, and actionable feedback.
If the user's message is very short or doesn't give much to evaluate regarding the grammar point, provide general feedback and try to elicit a more substantial response that uses the grammar.
`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: AIResponseSchema,
  });
}

/**
 * Starts a chat exercise for a given vocabulary item.
 * The AI will initiate the conversation.
 */
export async function startVocabularyChatExercise(
  word: string,
  pinyin: string,
  definitions: string[],
  examples: { chinese: string; pinyin: string; english: string; }[],
): Promise<z.infer<typeof AIResponseSchema>> {
  const systemPrompt = `You are an AI language tutor. Your goal is to help the user practice a specific Chinese vocabulary item through an interactive conversation.
The user wants to practice:
Vocabulary Word: ${word}
Pinyin: ${pinyin}
Definitions: ${definitions.join(", ")}
Examples:
${examples.map((ex) => `- ${ex.chinese} (${ex.pinyin}): ${ex.english}`).join("\n")}

Initiate a conversation with the user. Your first message should be engaging and designed to naturally lead the user to apply the vocabulary word: "${word}".
Do not evaluate anything yet, as this is the start of the conversation.
Your response must be a JSON object matching the following schema:
{
  "nextAIMessage": "Your engaging opening message to the user"
}
The "evaluation" field should be omitted for this initial message.`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: AIResponseSchema,
  });
}

/**
 * Starts a translation chat game.
 * The AI will generate the first Chinese sentence for the user to translate.
 */
export async function startTranslationChatGame(
  category: string,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
): Promise<z.infer<typeof TranslationChatAIResponseSchema>> {
  const vocabList = vocabulary.map(v => `${v.word} (${v.pinyin} - ${v.definitions ? v.definitions.join('/') : 'N/A'})`).join(', ');
  const grammarList = grammarPoints.map(g => `${g.title} (${g.explanation})`).join(', ');

  const systemPrompt = `You are an AI language tutor specializing in Chinese translation. Your goal is to provide sentences for the user to translate, adapting difficulty based on their performance.
The user is practicing vocabulary and grammar from the "${category}" category.

Available Vocabulary: ${vocabList}
Available Grammar Points: ${grammarList}

Generate the first Chinese sentence for the user to translate.
The sentence should:
1.  Be a natural Chinese sentence.
2.  Primarily use vocabulary and grammar from the provided lists.
3.  Use words mostly from HSK 3 level, allowing the user to focus on the target vocabulary and grammar.
4.  Be suitable for a beginner/intermediate learner.

Your response MUST be a JSON object matching the TranslationChatAIResponseSchema.
The "evaluation" field should be omitted for this initial message.
The "nextAIMessage" can be an encouraging opening message.
`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: TranslationChatAIResponseSchema,
  });
}

/**
 * Continues a translation chat game, evaluating the user's answer and generating the next question.
 */
export async function continueTranslationChatGame(
  category: string,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  userAnswer: string,
  previousChineseSentence: string,
): Promise<z.infer<typeof TranslationChatAIResponseSchema>> {
  const vocabList = vocabulary.map(v => `${v.word} (${v.pinyin} - ${v.definitions ? v.definitions.join('/') : 'N/A'})`).join(', ');
  const grammarList = grammarPoints.map(g => `${g.title} (${g.explanation})`).join(', ');

  const formattedChatHistory = chatHistory
    .map(
      (turn) =>
        `${turn.role === "user" ? "User" : "Assistant"}: ${turn.content}${
          turn.evaluation
            ? ` (Evaluation: Score ${turn.evaluation.score}, Feedback: ${turn.evaluation.feedback})`
            : ""
        }`
    )
    .join("\n");

  const systemPrompt = `You are an AI language tutor specializing in Chinese translation. You are continuing an interactive translation chat with a user.

The user is practicing vocabulary and grammar from the "${category}" category.
Available Vocabulary: ${vocabList}
Available Grammar Points: ${grammarList}

Conversation History:
${formattedChatHistory}

Previous Chinese Sentence (User just translated this): "${previousChineseSentence}"
User's Latest English Translation: "${userAnswer}"

Your tasks are:
1.  **Evaluate the user's latest English translation** ("${userAnswer}") against the "Previous Chinese Sentence".
    *   Focus on accuracy, naturalness, and correct application of target vocabulary and grammar from the provided lists.
    *   Provide a score (0-100) and detailed feedback.
    *   Identify specific vocabulary items and grammar points from the provided lists that were used or missed, and assign a mastery score (0-100) for each.
2.  **Generate the next Chinese sentence** for the user to translate.
    *   Adapt the difficulty of the next sentence based on the user's performance in the current and previous turns (from chat history). If the user struggled, simplify; if they excelled, increase complexity or introduce new target items.
    *   Ensure the sentence uses vocabulary and grammar from the provided lists, and other words are mostly HSK 3 level.
    *   Make sure the sentence is different from previous sentences.
3.  **Formulate an optional "nextAIMessage"**: This can be an encouraging remark, a transition, or a hint.

Your response MUST be a JSON object matching the TranslationChatAIResponseSchema:
{
  "question": {
    "chineseSentence": "The next Chinese sentence for translation",
    "targetVocabulary": ["vocab1", "vocab2"], // Optional: list of target vocab used
    "targetGrammar": ["grammar1"], // Optional: list of target grammar used
    "hskLevel": 3 // Optional: estimated HSK level
  },
  "evaluation": {
    "score": number (0-100),
    "feedback": "string",
    "vocabularyMastery": { "vocabId1": score, "vocabId2": score }, // Optional
    "grammarMastery": { "grammarId1": score } // Optional
  },
  "nextAIMessage": "Optional conversational message"
}

Focus on providing high-quality, specific, and actionable feedback.
Ensure the generated sentences are natural and relevant to the learning context.
`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: TranslationChatAIResponseSchema,
  });
}

/**
 * Ends a translation chat game and provides an overall summary of the user's progress.
 */
export async function endTranslationChatGame(
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
): Promise<z.infer<typeof TranslationChatGameSummarySchema>> {
  const formattedChatHistory = chatHistory
    .map(
      (turn) =>
        `${turn.role === "user" ? "User" : "Assistant"}: ${turn.content}${
          turn.evaluation
            ? ` (Evaluation: Score ${turn.evaluation.score}, Feedback: ${turn.evaluation.feedback})`
            : ""
        }`
    )
    .join("\n");

  const vocabList = vocabulary.map(v => `${v.word} (${v.pinyin} - ${v.definitions ? v.definitions.join('/') : 'N/A'})`).join(', ');
  const grammarList = grammarPoints.map(g => `${g.title} (${g.explanation})`).join(', ');

  const systemPrompt = `You are an AI language tutor. The user has completed a Chinese translation chat game.
Review the entire conversation history and provide an overall evaluation of their progress.

Conversation History:
${formattedChatHistory}

Vocabulary items covered in this game: ${vocabList}
Grammar points covered in this game: ${grammarList}

Your tasks are:
1.  Provide an "overallFeedback" summarizing the user's performance, strengths, and areas for improvement in translation, vocabulary usage, and grammar application during the game.
2.  Generate a "vocabularyProgressSummary" object. For each vocabulary item from the provided list that was encountered and evaluated in the chat, provide:
    *   \`correctAttempts\`: Number of times the user correctly translated sentences using this vocabulary.
    *   \`totalAttempts\`: Total number of times this vocabulary was featured in a sentence for translation.
    *   \`masteryScore\`: An aggregated mastery score (0-100) for this vocabulary item based on all evaluations.
3.  Generate a "grammarProgressSummary" object. For each grammar point from the provided list that was encountered and evaluated in the chat, provide:
    *   \`correctAttempts\`: Number of times the user correctly translated sentences using this grammar point.
    *   \`totalAttempts\`: Total number of times this grammar point was featured in a sentence for translation.
    *   \`masteryScore\`: An aggregated mastery score (0-100) for this grammar point based on all evaluations.

Your response MUST be a JSON object matching the TranslationChatGameSummarySchema:
{
  "overallFeedback": "string",
  "vocabularyProgressSummary": {
    "vocabId1": { "correctAttempts": number, "totalAttempts": number, "masteryScore": number },
    // ...
  },
  "grammarProgressSummary": {
    "grammarId1": { "correctAttempts": number, "totalAttempts": number, "masteryScore": number },
    // ...
  }
}

If a vocabulary item or grammar point was not explicitly evaluated or featured, it can be omitted from the summary.
Focus on providing actionable insights for the user's continued learning.
`;

  return llmService.generateStructuredOutput(systemPrompt, {
    responseSchema: TranslationChatGameSummarySchema,
  });
}

// Schemas for Chat Feature
export const DialogueSegmentSchema = z.object({
  speaker: z.string().describe("The speaker name (e.g., 'Person A', 'Person B')"),
  chineseText: z.string().describe("The Chinese text for this dialogue segment"),
  englishText: z.string().describe("The English translation of the Chinese text"),
  pinyin: z.string().optional().describe("Pinyin pronunciation guide"),
  targetVocabulary: z.array(z.string()).optional().describe("Key vocabulary words used in this segment"),
  targetGrammar: z.array(z.string()).optional().describe("Grammar points demonstrated in this segment"),
  difficulty: z.number().int().min(1).max(10).describe("Difficulty level of this segment (1-10)"),
});

export const GeneratedDialogueSchema = z.object({
  topic: z.string().describe("The topic of the conversation"),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).describe("Overall difficulty level"),
  speakers: z.array(z.string()).describe("List of speaker names in the dialogue"),
  segments: z.array(DialogueSegmentSchema).min(5).max(15).describe("Array of dialogue segments"),
  vocabulary: z.array(z.string()).describe("Key vocabulary words used throughout the dialogue"),
  grammarPoints: z.array(z.string()).describe("Grammar points demonstrated in the dialogue"),
  culturalContext: z.string().optional().describe("Cultural context or background information"),
});

export const CharacterMistakeAnalysisSchema = z.object({
  character: z.string().describe("The character the user wrote incorrectly"),
  expectedCharacter: z.string().describe("The correct character that should have been used"),
  position: z.number().int().describe("Position in the user's translation where the mistake occurred"),
  mistakeType: z.enum(['substitution', 'omission', 'addition', 'tone', 'context', 'grammar']).describe("Type of mistake made"),
  severity: z.enum(['minor', 'moderate', 'major']).describe("Severity of the mistake"),
  explanation: z.string().describe("Explanation of why this mistake occurred and how to correct it"),
  similarCharacters: z.array(z.string()).optional().describe("Characters that might cause confusion"),
  contextualUsage: z.string().optional().describe("How this character should be used in this context"),
});

export const TranslationEvaluationSchema = z.object({
  score: z.number().int().min(0).max(100).describe("Score from 0-100 for translation accuracy"),
  isCorrect: z.boolean().describe("Whether the translation is considered correct"),
  feedback: z.string().describe("Detailed feedback on the translation"),
  correctTranslation: z.string().describe("The correct Chinese translation"),
  mistakeAnalysis: z.array(CharacterMistakeAnalysisSchema).describe("Analysis of character-level mistakes"),
  suggestions: z.array(z.string()).describe("Suggestions for improvement"),
  contextTips: z.array(z.string()).optional().describe("Tips about context and usage"),
});

// Chat Feature Functions

/**
 * Generates a multi-person Chinese dialogue based on a topic and user preferences
 */
export async function generateChatDialogue(
  request: ChatDialogueRequest
): Promise<GeneratedDialogue> {
  const { topic, difficulty, targetCharacters, conversationLength, speakers } = request;

  const targetCharactersText = targetCharacters && targetCharacters.length > 0
    ? `Focus especially on using these characters that the user has struggled with: ${targetCharacters.join(', ')}`
    : '';

  const systemPrompt = `You are an AI language tutor specializing in creating realistic Chinese conversations for language learning.

Create a natural, engaging ${speakers}-person dialogue in Chinese about the topic: "${topic}"

Requirements:
- Difficulty level: ${difficulty}
- Number of dialogue segments: ${conversationLength}
- Number of speakers: ${speakers}
- Each speaker should have a distinct voice and perspective
- The conversation should flow naturally and cover different aspects of the topic
- Include a variety of sentence structures and vocabulary appropriate for ${difficulty} level
- ${targetCharactersText}

The dialogue should be educational but feel like a real conversation between native speakers.
Include cultural context where appropriate.
Ensure each segment has clear Chinese text, accurate English translation, and appropriate difficulty progression.

Your response MUST be a JSON object matching the GeneratedDialogueSchema.`;

  const options: StructuredOutputOptions<typeof GeneratedDialogueSchema> = {
    responseSchema: GeneratedDialogueSchema,
    temperature: 0.8,
  };

  return llmService.generateStructuredOutput(systemPrompt, options);
}

/**
 * Evaluates a user's Chinese translation with detailed character-level analysis
 */
export async function evaluateTranslation(
  englishText: string,
  userTranslation: string,
  correctTranslation: string,
  context?: string
): Promise<TranslationEvaluation> {
  const contextText = context ? `Context: ${context}` : '';

  const systemPrompt = `You are an AI language tutor specializing in Chinese translation evaluation.

Task: Evaluate the user's Chinese translation with detailed character-level analysis.

English Source Text: "${englishText}"
User's Chinese Translation: "${userTranslation}"
Correct Chinese Translation: "${correctTranslation}"
${contextText}

Provide a comprehensive evaluation including:
1. Overall score (0-100) based on accuracy, naturalness, and appropriateness
2. Whether the translation is considered correct (allowing for valid alternatives)
3. Detailed feedback explaining strengths and areas for improvement
4. Character-level mistake analysis for each error, including:
   - What character was used vs. what should have been used
   - Type of mistake (substitution, omission, addition, tone, context, grammar)
   - Severity level and explanation
   - Similar characters that might cause confusion
5. Specific suggestions for improvement
6. Context tips for better understanding

Be encouraging but thorough in identifying areas for improvement.
Focus on helping the user understand not just what was wrong, but why it was wrong and how to improve.

Your response MUST be a JSON object matching the TranslationEvaluationSchema.`;

  const options: StructuredOutputOptions<typeof TranslationEvaluationSchema> = {
    responseSchema: TranslationEvaluationSchema,
    temperature: 0.3,
  };

  return llmService.generateStructuredOutput(systemPrompt, options);
}
