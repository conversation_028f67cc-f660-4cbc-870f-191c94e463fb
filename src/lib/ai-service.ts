import { z } from "zod";
import { GrammarPoint, VocabularyItem } from "@/types/content";

// Define Difficulty type as it's used in the new function
export type StoryDifficulty = 'easy' | 'medium' | 'hard';

// Schemas for Story Adventure Game
const questionTypeEnum = z.enum(['multiple-choice', 'true-false']);

const questionSchema = z.object({
  id: z.string().describe("A unique ID for the question (e.g., 'q1', 'q2')."),
  type: questionTypeEnum,
  questionText: z.string().describe("The text of the question."),
  options: z.array(z.string()).optional().describe("An array of 3-4 options for multiple-choice questions."),
  correctAnswer: z.union([z.string(), z.boolean()]).describe("The correct answer. String for multiple-choice, boolean for true-false."),
});

export const storyDataSchema = z.object({
  storyText: z.string().describe("The generated story text."),
  theme: z.string().describe("The theme of the story (e.g., 'A Day at the Park')."),
  questions: z.array(questionSchema).min(3).max(5).describe("An array of 3-5 comprehension questions based on the story."),
  generatedHints: z.array(z.string()).optional().describe("An array of 2-3 AI-generated hints related to grammar points or vocabulary used in the story.")
});

export type StoryData = z.infer<typeof storyDataSchema>;
export type Question = z.infer<typeof questionSchema>;


export interface Exercise {
  prompt: string;
  options?: string[];
  correctAnswer: string;
  type: string;
  explanation: string;
  jumbledWords?: string[]; // Added for sentence-ordering
}


// Schemas for Interactive Chat
export const EvaluationSchema = z.object({
  score: z.number().min(0).max(100).describe("Score from 0 to 100 evaluating the user's application of the grammar point."),
  feedback: z.string().describe("Detailed feedback on the user's message, focusing on grammar usage and correctness."),
});

export const ChatTurnSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
  evaluation: EvaluationSchema.optional().describe("Evaluation of the user's message, if the role is 'user' and it has been evaluated."),
});

export const AIResponseSchema = z.object({
  evaluation: EvaluationSchema.optional().describe("Evaluation of the user's previous message. This is present when the AI is responding to a user."),
  nextAIMessage: z.string().describe("The AI's next message in the conversation to prompt further practice."),
});

// Schemas for Translation Chat Game
export const TranslationChatQuestionSchema = z.object({
  chineseSentence: z.string().describe("The Chinese sentence for the user to translate."),
  targetVocabulary: z.array(z.string()).optional().describe("Vocabulary items from the provided list used in the sentence."),
  targetGrammar: z.array(z.string()).optional().describe("Grammar points from the provided list used in the sentence."),
  hskLevel: z.number().int().min(1).max(6).optional().describe("The estimated HSK level of the sentence, primarily based on non-target vocabulary."),
});

export const TranslationChatEvaluationSchema = z.object({
  score: z.number().int().min(0).max(100).describe("Score between 0 and 100 for the translation accuracy."),
  feedback: z.string().describe("Detailed feedback on the user's translation, including correctness, vocabulary, and grammar usage."),
  vocabularyMastery: z.record(z.string(), z.number().int().min(0).max(100)).optional().describe("Mastery score for specific vocabulary items used in the sentence."),
  grammarMastery: z.record(z.string(), z.number().int().min(0).max(100)).optional().describe("Mastery score for specific grammar points used in the sentence."),
});

export const TranslationChatAIResponseSchema = z.object({
  question: TranslationChatQuestionSchema.optional().describe("The next Chinese sentence for the user to translate."),
  evaluation: TranslationChatEvaluationSchema.optional().describe("Evaluation of the user's previous translation."),
  nextAIMessage: z.string().optional().describe("An optional conversational message from the AI, e.g., encouragement or a transition."),
});

export const TranslationChatGameSummarySchema = z.object({
  overallFeedback: z.string().describe("Overall feedback on the user's performance throughout the game."),
  vocabularyProgressSummary: z.record(z.string(), z.object({
    correctAttempts: z.number().int(),
    totalAttempts: z.number().int(),
    masteryScore: z.number().int().min(0).max(100),
  })).optional().describe("Summary of progress for each vocabulary item."),
  grammarProgressSummary: z.record(z.string(), z.object({
    correctAttempts: z.number().int(),
    totalAttempts: z.number().int(),
    masteryScore: z.number().int().min(0).max(100),
  })).optional().describe("Summary of progress for each grammar point."),
});
// End Schemas for Translation Chat Game

interface GenerateExerciseParams {
  item: GrammarPoint | VocabularyItem;
  type: string; // "grammar" or "vocabulary"
}

interface ValidateAnswerParams {
  exercise: Exercise;
  userAnswer: string;
}

export async function generateExercise(params: GenerateExerciseParams) {
  const response = await fetch('/api/ai/generate-exercise', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to generate exercise');
  }

  return response.json();
}

export async function validateAnswer(params: ValidateAnswerParams) {
  const response = await fetch('/api/ai/validate-answer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to validate answer');
  }

  return response.json();
}

// Functions for Interactive Chat

/**
 * Starts a chat exercise for a given grammar point.
 * The AI will initiate the conversation.
 */
export async function startChatExercise(
  grammarPoint: GrammarPoint,
): Promise<z.infer<typeof AIResponseSchema>> {
  const response = await fetch('/api/ai/start-chat-exercise', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ grammarPoint }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to start chat exercise');
  }

  return response.json();
}

/**
 * Continues a chat exercise based on the history and user's latest answer.
 * The AI will evaluate the user's answer and provide the next conversational turn.
 */
export async function continueChat(
  grammarPoint: GrammarPoint,
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  userAnswer: string,
): Promise<z.infer<typeof AIResponseSchema>> {
  const response = await fetch('/api/ai/continue-chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ grammarPoint, chatHistory, userAnswer }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to continue chat');
  }

  return response.json();
}

/**
 * Starts a chat exercise for a given vocabulary item.
 * The AI will initiate the conversation.
 */
export async function startVocabularyChatExercise(
  word: string,
  pinyin: string,
  definitions: string[],
  examples: { chinese: string; pinyin: string; english: string; }[],
): Promise<z.infer<typeof AIResponseSchema>> {
  const response = await fetch('/api/ai/start-vocabulary-chat-exercise', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ word, pinyin, definitions, examples }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to start vocabulary chat exercise');
  }

  return response.json();
}

/**
 * Starts a translation chat game.
 * The AI will generate the first Chinese sentence for the user to translate.
 */
export async function startTranslationChatGame(
  category: string,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
): Promise<z.infer<typeof TranslationChatAIResponseSchema>> {
  const response = await fetch('/api/ai/start-translation-chat-game', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ category, vocabulary, grammarPoints }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to start translation chat game');
  }

  return response.json();
}

/**
 * Continues a translation chat game, evaluating the user's answer and generating the next question.
 */
export async function continueTranslationChatGame(
  category: string,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  userAnswer: string,
  previousChineseSentence: string,
): Promise<z.infer<typeof TranslationChatAIResponseSchema>> {
  const response = await fetch('/api/ai/continue-translation-chat-game', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ category, vocabulary, grammarPoints, chatHistory, userAnswer, previousChineseSentence }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to continue translation chat game');
  }

  return response.json();
}

/**
 * Ends a translation chat game and provides an overall summary of the user's progress.
 */
export async function endTranslationChatGame(
  chatHistory: Array<z.infer<typeof ChatTurnSchema>>,
  vocabulary: VocabularyItem[],
  grammarPoints: GrammarPoint[],
): Promise<z.infer<typeof TranslationChatGameSummarySchema>> {
  const response = await fetch('/api/ai/end-translation-chat-game', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ chatHistory, vocabulary, grammarPoints }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to end translation chat game');
  }

  return response.json();
}

// Chat Feature Functions

/**
 * Generate a dialogue for chat practice
 */
export async function generateChatDialogue(request: any) {
  const response = await fetch('/api/ai/generate-chat-dialogue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to generate dialogue');
  }

  return response.json();
}

/**
 * Evaluate a user's translation
 */
export async function evaluateUserTranslation(
  englishText: string,
  userTranslation: string,
  correctTranslation: string,
  context?: string
) {
  const response = await fetch('/api/ai/evaluate-translation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      englishText,
      userTranslation,
      correctTranslation,
      context
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to evaluate translation');
  }

  return response.json();
}

/**
 * Save chat session performance data
 */
export async function saveChatPerformance(
  sessionId: string,
  userId: string,
  performanceData: any[]
) {
  const response = await fetch('/api/chat/performance', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      sessionId,
      userId,
      performanceData
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to save performance data');
  }

  return response.json();
}

/**
 * Get adaptive learning profile
 */
export async function getAdaptiveLearningProfile(userId: string) {
  const response = await fetch(`/api/chat/performance?userId=${encodeURIComponent(userId)}`, {
    method: 'GET',
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to get adaptive learning profile');
  }

  return response.json();
}

/**
 * Generate adaptive dialogue request
 */
export async function generateAdaptiveDialogueRequest(
  userId: string,
  baseTopic: string,
  userPreferences?: any
) {
  const response = await fetch('/api/chat/adaptive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      userId,
      baseTopic,
      userPreferences
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to generate adaptive dialogue request');
  }

  return response.json();
}
