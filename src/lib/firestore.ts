import { collection, getDocs, doc, getDoc, query, where, orderBy } from "firebase/firestore";
import { db } from "./firebase";
import { VocabularyItem, GrammarPoint, GrammarGame, CategoryStructureMetadata as Metadata } from "../types/content"; // Import interfaces from types file

/**
 * Fetches all vocabulary items from Firestore.
 * @returns A promise that resolves with an array of VocabularyItem objects.
 */
export async function getAllVocabularyItems(): Promise<VocabularyItem[]> {
  const vocabularyCollection = collection(db, "vocabulary");
  const vocabularySnapshot = await getDocs(vocabularyCollection);
  return vocabularySnapshot.docs.map(doc => ({ ...doc.data() as VocabularyItem, id: doc.id }));
}

/**
 * Fetches vocabulary items for a specific category and chapter.
 * @param category The category of the vocabulary items.
 * @param chapter The chapter of the vocabulary items.
 * @returns A promise that resolves with an array of VocabularyItem objects.
 */
export async function getVocabularyItemsByCategoryAndChapter(category: string, chapter: string): Promise<VocabularyItem[]> {
  const vocabularyCollection = collection(db, "vocabulary");
  const q = query(vocabularyCollection, where("category", "==", category), orderBy("chapter"));
  const vocabularySnapshot = await getDocs(q);
  return vocabularySnapshot.docs.map(doc => ({ ...doc.data() as VocabularyItem, id: doc.id }));
}

/**
 * Fetches a single vocabulary item by its document ID.
 * @param docId The document ID of the vocabulary item.
 * @returns A promise that resolves with the VocabularyItem object or undefined if not found.
 */
export async function getVocabularyItemById(docId: string): Promise<VocabularyItem | undefined> {
  const docRef = doc(db, "vocabulary", docId);
  const docSnap = await getDoc(docRef);
  console.log(docSnap.exists());
  if (docSnap.exists()) {
    return { ...docSnap.data() as VocabularyItem, id: docSnap.id };
  } else {
    return undefined;
  }
}


/**
 * Fetches all grammar points from Firestore.
 * @returns A promise that resolves with an array of GrammarPoint objects.
 */
export async function getAllGrammarPoints(): Promise<GrammarPoint[]> {
  const grammarCollection = collection(db, "grammar");
  const grammarSnapshot = await getDocs(grammarCollection);
  return grammarSnapshot.docs.map(doc => ({ ...doc.data() as GrammarPoint, id: doc.id }));
}

/**
 * Fetches grammar points for a specific category and chapter.
 * @param category The category of the grammar points.
 * @param chapter The chapter of the grammar points.
 * @returns A promise that resolves with an array of GrammarPoint objects.
 */
export async function getGrammarPointsByCategoryAndChapter(category: string, chapter: string): Promise<GrammarPoint[]> {
  const grammarCollection = collection(db, "grammar");
  const q = query(grammarCollection, where("category", "==", category), orderBy("chapter"));
  const grammarSnapshot = await getDocs(q);
  console.log(grammarSnapshot.docs.length);
  return grammarSnapshot.docs.map(doc => ({ ...doc.data() as GrammarPoint, id: doc.id }));
}

/**
 * Fetches a single grammar point by its document ID.
 * @param docId The document ID of the grammar point.
 * @returns A promise that resolves with the GrammarPoint object or undefined if not found.
 */
export async function getGrammarPointById(docId: string): Promise<GrammarPoint | undefined> {
  const docRef = doc(db, "grammar", docId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return { ...docSnap.data() as GrammarPoint, id: docSnap.id };
  } else {
    return undefined;
  }
}

/**
 * Fetches all grammar games from Firestore.
 * @returns A promise that resolves with an array of GrammarGame objects.
 */
export async function getAllGrammarGames(): Promise<GrammarGame[]> {
  const grammarGamesCollection = collection(db, "grammarGames");
  const grammarGamesSnapshot = await getDocs(grammarGamesCollection);
  return grammarGamesSnapshot.docs.map(doc => doc.data() as GrammarGame);
}

/**
 * Fetches grammar games for a specific category.
 * @param category The category of the grammar games.
 * @returns A promise that resolves with an array of GrammarGame objects.
 */
export async function getGrammarGamesByCategory(category: string): Promise<GrammarGame[]> {
  const grammarGamesCollection = collection(db, "grammarGames");
  const q = query(grammarGamesCollection, where("category", "==", category));
  const grammarGamesSnapshot = await getDocs(q);
  return grammarGamesSnapshot.docs.map(doc => doc.data() as GrammarGame);
}

/**
 * Fetches a single grammar game by its document ID.
 * @param docId The document ID of the grammar game.
 * @returns A promise that resolves with the GrammarGame object or undefined if not found.
 */
export async function getGrammarGameById(docId: string): Promise<GrammarGame | undefined> {
  const docRef = doc(db, "grammarGames", docId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return docSnap.data() as GrammarGame;
  } else {
    return undefined;
  }
}

/**
 * Fetches the metadata document from Firestore.
 * @returns A promise that resolves with the Metadata object or undefined if not found.
 */
export async function getMetadata(): Promise<Metadata | undefined> {
  const docRef = doc(db, "metadata", "categoryStructure");
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return docSnap.data() as Metadata;
  } else {
    return undefined;
  }
}