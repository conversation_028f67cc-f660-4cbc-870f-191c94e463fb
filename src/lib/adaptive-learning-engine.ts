import {
  AdaptiveLearningPro<PERSON>le,
  ProblematicCharacter,
  ChatDialogueRequest,
  ChatTopic,
  GeneratedDialogue
} from '@/types/chat';
import { performanceTracker } from './performance-tracker';

export class AdaptiveLearningEngine {
  
  /**
   * Generate adaptive dialogue request based on user's learning profile
   */
  async generateAdaptiveDialogueRequest(
    userId: string,
    baseTopic: string,
    userPreferences?: Partial<ChatDialogueRequest>
  ): Promise<ChatDialogueRequest> {
    try {
      const profile = await performanceTracker.getAdaptiveLearningProfile(userId);
      
      if (!profile) {
        // No profile yet, return basic request
        return {
          topic: baseTopic,
          difficulty: 'beginner',
          conversationLength: 8,
          speakers: 2,
          ...userPreferences
        };
      }

      // Get problematic characters to focus on
      const targetCharacters = this.selectTargetCharacters(profile);
      
      // Determine appropriate difficulty
      const difficulty = this.determineDifficulty(profile);
      
      // Adjust conversation length based on performance
      const conversationLength = this.determineConversationLength(profile);
      
      // Select number of speakers based on difficulty
      const speakers = difficulty === 'beginner' ? 2 : difficulty === 'intermediate' ? 3 : 4;

      return {
        topic: baseTopic,
        difficulty,
        targetCharacters,
        conversationLength,
        speakers,
        ...userPreferences
      };
    } catch (error) {
      console.error('Failed to generate adaptive dialogue request:', error);
      
      // Fallback to basic request
      return {
        topic: baseTopic,
        difficulty: 'beginner',
        conversationLength: 8,
        speakers: 2,
        ...userPreferences
      };
    }
  }

  /**
   * Select target characters based on user's problematic areas
   */
  private selectTargetCharacters(profile: AdaptiveLearningProfile): string[] {
    const problematicChars = profile.problematicCharacters
      .filter(char => char.masteryScore < 80) // Focus on non-mastered characters
      .sort((a, b) => {
        // Prioritize by mistake frequency and recency
        const aScore = this.calculatePriorityScore(a);
        const bScore = this.calculatePriorityScore(b);
        return bScore - aScore;
      })
      .slice(0, 10) // Top 10 most problematic
      .map(char => char.character);

    return problematicChars;
  }

  /**
   * Calculate priority score for a problematic character
   */
  private calculatePriorityScore(char: ProblematicCharacter): number {
    const daysSinceLastMistake = (Date.now() - char.lastMistake.getTime()) / (1000 * 60 * 60 * 24);
    const recencyFactor = Math.max(0.1, 1 / (1 + daysSinceLastMistake / 7)); // Higher score for recent mistakes
    const frequencyFactor = Math.min(1, char.mistakeCount / 10); // Normalize mistake count
    const masteryFactor = (100 - char.masteryScore) / 100; // Higher score for lower mastery
    
    return (frequencyFactor * 0.4) + (masteryFactor * 0.4) + (recencyFactor * 0.2);
  }

  /**
   * Determine appropriate difficulty level
   */
  private determineDifficulty(profile: AdaptiveLearningProfile): 'beginner' | 'intermediate' | 'advanced' {
    const { averageScore, totalSessions, learningVelocity } = profile;
    
    // Consider multiple factors
    if (averageScore >= 85 && totalSessions >= 10 && learningVelocity > 0.5) {
      return 'advanced';
    } else if (averageScore >= 70 && totalSessions >= 5) {
      return 'intermediate';
    } else {
      return 'beginner';
    }
  }

  /**
   * Determine conversation length based on performance
   */
  private determineConversationLength(profile: AdaptiveLearningProfile): number {
    const { averageScore, learningVelocity } = profile;
    
    // Shorter conversations for struggling learners, longer for advanced
    if (averageScore < 60) {
      return 6; // Shorter sessions to avoid frustration
    } else if (averageScore >= 80 && learningVelocity > 0.3) {
      return 12; // Longer sessions for advanced learners
    } else {
      return 8; // Standard length
    }
  }

  /**
   * Recommend topics based on user's learning patterns
   */
  async recommendTopics(userId: string, availableTopics: ChatTopic[]): Promise<ChatTopic[]> {
    try {
      const profile = await performanceTracker.getAdaptiveLearningProfile(userId);
      
      if (!profile) {
        // No profile, return beginner-friendly topics
        return availableTopics
          .filter(topic => topic.difficulty === 'beginner')
          .sort((a, b) => b.popularity - a.popularity)
          .slice(0, 5);
      }

      // Score topics based on user's profile
      const scoredTopics = availableTopics.map(topic => ({
        topic,
        score: this.scoreTopicForUser(topic, profile)
      }));

      // Sort by score and return top recommendations
      return scoredTopics
        .sort((a, b) => b.score - a.score)
        .slice(0, 8)
        .map(item => item.topic);
    } catch (error) {
      console.error('Failed to recommend topics:', error);
      return availableTopics.slice(0, 5);
    }
  }

  /**
   * Score a topic for a specific user
   */
  private scoreTopicForUser(topic: ChatTopic, profile: AdaptiveLearningProfile): number {
    let score = 0;

    // Difficulty match
    if (topic.difficulty === profile.difficultyLevel) {
      score += 0.4;
    } else if (
      (topic.difficulty === 'intermediate' && profile.difficultyLevel === 'beginner' && profile.averageScore > 70) ||
      (topic.difficulty === 'advanced' && profile.difficultyLevel === 'intermediate' && profile.averageScore > 80)
    ) {
      score += 0.2; // Slight challenge is good
    }

    // Preferred topics
    if (profile.preferredTopics.includes(topic.name)) {
      score += 0.3;
    }

    // Vocabulary overlap with problematic characters
    const problematicChars = profile.problematicCharacters.map(c => c.character);
    const vocabularyOverlap = topic.keyVocabulary.filter(vocab => 
      problematicChars.some(char => vocab.includes(char))
    ).length;
    score += Math.min(0.2, vocabularyOverlap / topic.keyVocabulary.length);

    // Popularity factor (small influence)
    score += (topic.popularity / 100) * 0.1;

    return score;
  }

  /**
   * Analyze learning progress and provide insights
   */
  async analyzeLearningProgress(userId: string): Promise<{
    overallProgress: number;
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    nextGoals: string[];
  }> {
    try {
      const profile = await performanceTracker.getAdaptiveLearningProfile(userId);
      
      if (!profile) {
        return {
          overallProgress: 0,
          strengths: [],
          weaknesses: [],
          recommendations: ['Start with beginner-level conversations to build your foundation'],
          nextGoals: ['Complete your first conversation practice session']
        };
      }

      const masteredCharacters = profile.problematicCharacters.filter(c => c.masteryScore >= 80);
      const strugglingCharacters = profile.problematicCharacters.filter(c => c.masteryScore < 50);
      const improvingCharacters = profile.problematicCharacters.filter(c => c.improvementRate > 0.6);

      const overallProgress = Math.min(100, (profile.averageScore + (masteredCharacters.length * 2)) / 2);

      const strengths = [
        ...(profile.averageScore >= 80 ? ['Consistent high performance'] : []),
        ...(masteredCharacters.length >= 10 ? ['Strong character recognition'] : []),
        ...(profile.learningVelocity > 0.3 ? ['Fast learning pace'] : []),
        ...(improvingCharacters.length >= 5 ? ['Good improvement trend'] : [])
      ];

      const weaknesses = [
        ...(strugglingCharacters.length >= 5 ? ['Several challenging characters'] : []),
        ...(profile.averageScore < 60 ? ['Translation accuracy needs work'] : []),
        ...(profile.learningVelocity < 0.1 ? ['Learning pace could be faster'] : [])
      ];

      const recommendations = this.generateRecommendations(profile);
      const nextGoals = this.generateNextGoals(profile);

      return {
        overallProgress,
        strengths,
        weaknesses,
        recommendations,
        nextGoals
      };
    } catch (error) {
      console.error('Failed to analyze learning progress:', error);
      return {
        overallProgress: 0,
        strengths: [],
        weaknesses: [],
        recommendations: ['Continue practicing regularly'],
        nextGoals: ['Complete more conversation sessions']
      };
    }
  }

  /**
   * Generate personalized recommendations
   */
  private generateRecommendations(profile: AdaptiveLearningProfile): string[] {
    const recommendations: string[] = [];

    if (profile.averageScore < 60) {
      recommendations.push('Focus on shorter conversations to build confidence');
      recommendations.push('Review basic grammar patterns before practicing');
    }

    if (profile.problematicCharacters.length >= 10) {
      recommendations.push('Practice writing problematic characters by hand');
      recommendations.push('Use spaced repetition for difficult characters');
    }

    if (profile.learningVelocity < 0.2) {
      recommendations.push('Increase practice frequency to 3-4 sessions per week');
      recommendations.push('Try easier topics to build momentum');
    }

    if (profile.totalSessions < 5) {
      recommendations.push('Complete at least 10 sessions to establish learning patterns');
    }

    return recommendations.slice(0, 4); // Limit to top 4 recommendations
  }

  /**
   * Generate next learning goals
   */
  private generateNextGoals(profile: AdaptiveLearningProfile): string[] {
    const goals: string[] = [];

    const strugglingChars = profile.problematicCharacters.filter(c => c.masteryScore < 50);
    if (strugglingChars.length > 0) {
      goals.push(`Master ${Math.min(3, strugglingChars.length)} challenging characters: ${strugglingChars.slice(0, 3).map(c => c.character).join(', ')}`);
    }

    if (profile.averageScore < 80) {
      goals.push(`Achieve 80% average accuracy (currently ${Math.round(profile.averageScore)}%)`);
    }

    if (profile.difficultyLevel === 'beginner' && profile.averageScore > 70) {
      goals.push('Try intermediate-level conversations');
    }

    if (profile.totalSessions < 20) {
      goals.push(`Complete ${20 - profile.totalSessions} more practice sessions`);
    }

    return goals.slice(0, 3); // Limit to top 3 goals
  }
}

export const adaptiveLearningEngine = new AdaptiveLearningEngine();
