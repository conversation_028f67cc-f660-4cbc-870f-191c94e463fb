import { localDB, STORES } from './localdb';
import * as firestoreAPI from './firestore';
import type { VocabularyItem, GrammarPoint, GrammarGame, CategoryStructureMetadata as Metadata } from '../types/content';

interface Identifiable {
  id?: string;
}

/**
 * Wrapper class for handling course data with local-first strategy
 */
class CourseDataService {
  private async getLocalOrFetch<T extends Identifiable>(
    storeName: string,
    fetchFn: () => Promise<T[]>,
    forceRefresh = false
  ): Promise<T[]> {
    if (!forceRefresh) {
      const isFresh = await localDB.isDataFresh();
      if (isFresh) {
        const localData = await localDB.getAll<T>(storeName);
        if (localData.length > 0) {
          return localData;
        }
      }
    }

    const data = await fetchFn();
    await Promise.all(data.map(item => localDB.put(storeName, item)));
    return data;
  }

  private async getLocalOrFetchById<T extends Identifiable>(
    storeName: string,
    id: string,
    fetchFn: (id: string) => Promise<T | undefined>
  ): Promise<T | undefined> {
    const isFresh = await localDB.isDataFresh();
    if (isFresh) {
      const localData = await localDB.get<T>(storeName, id);
      if (localData) {
        return localData;
      }
    }

    const data = await fetchFn(id);
    if (data) {
      await localDB.put(storeName, data);
    }
    return data;
  }

  // Vocabulary methods
  async getAllVocabularyItems(forceRefresh = false): Promise<VocabularyItem[]> {
    return this.getLocalOrFetch<VocabularyItem>(
      STORES.VOCABULARY,
      firestoreAPI.getAllVocabularyItems,
      forceRefresh
    );
  }

  async getVocabularyItemById(id: string): Promise<VocabularyItem | undefined> {
    return this.getLocalOrFetchById<VocabularyItem>(
      STORES.VOCABULARY,
      id,
      firestoreAPI.getVocabularyItemById
    );
  }

  async getVocabularyItemsByCategoryAndChapter(
    category: string,
    chapter: string,
    forceRefresh = false
  ): Promise<VocabularyItem[]> {
    // For now, we'll fetch all items and filter locally
    const allItems = await this.getAllVocabularyItems(forceRefresh);
    return allItems.filter(
      item => item.category === category && item.chapter === chapter
    );
  }

  async getVocabularyItemsByCategoryAndTag(
    category?: string | null,
    tag?: string | null,
    forceRefresh = false
  ): Promise<VocabularyItem[]> {
    const allItems = await this.getAllVocabularyItems(forceRefresh);
    return allItems.filter(item => {
      const matchesCategory = !category || item.category === category;
      const matchesTag = !tag || (item.tags && item.tags.includes(tag));
      return matchesCategory && matchesTag;
    });
  }

  // Grammar methods
  async getAllGrammarPoints(forceRefresh = false): Promise<GrammarPoint[]> {
    return this.getLocalOrFetch<GrammarPoint>(
      STORES.GRAMMAR,
      firestoreAPI.getAllGrammarPoints,
      forceRefresh
    );
  }

  async getGrammarPointById(id: string): Promise<GrammarPoint | undefined> {
    return this.getLocalOrFetchById<GrammarPoint>(
      STORES.GRAMMAR,
      id,
      firestoreAPI.getGrammarPointById
    );
  }

  async getGrammarPointsByCategoryAndChapter(
    category: string,
    chapter: string,
    forceRefresh = false
  ): Promise<GrammarPoint[]> {
    const allPoints = await this.getAllGrammarPoints(forceRefresh);
    return allPoints.filter(
      point => point.category === category && point.chapter === chapter
    );
  }

  async getGrammarPointsByCategory(
    category: string,
    forceRefresh = false
  ): Promise<GrammarPoint[]> {
    const allPoints = await this.getAllGrammarPoints(forceRefresh);
    return allPoints.filter(point => point.category === category);
  }

  // Grammar Games methods
  async getAllGrammarGames(forceRefresh = false): Promise<GrammarGame[]> {
    return this.getLocalOrFetch<GrammarGame>(
      STORES.GRAMMAR_GAMES,
      firestoreAPI.getAllGrammarGames,
      forceRefresh
    );
  }

  async getGrammarGameById(id: string): Promise<GrammarGame | undefined> {
    return this.getLocalOrFetchById<GrammarGame>(
      STORES.GRAMMAR_GAMES,
      id,
      firestoreAPI.getGrammarGameById
    );
  }

  async getGrammarGamesByCategory(
    category: string,
    forceRefresh = false
  ): Promise<GrammarGame[]> {
    const allGames = await this.getAllGrammarGames(forceRefresh);
    return allGames.filter(game => game.category === category);
  }

  // Metadata methods
  async getMetadata(forceRefresh = false): Promise<Metadata | undefined> {
    const isFresh = await localDB.isDataFresh();
    if (!forceRefresh && isFresh) {
      const localData = await localDB.get<Metadata>(STORES.METADATA, 'categoryStructure');
      if (localData) {
        return localData;
      }
    }

    const data = await firestoreAPI.getMetadata();
    if (data) {
      await localDB.put(STORES.METADATA, { ...data, id: 'categoryStructure' });
    }
    return data;
  }

  // Sync mechanism
  async syncCourseMaterials(): Promise<void> {
    try {
      // Fetch all data from Firestore
      const [
        vocabulary,
        grammar,
        grammarGames,
        metadata
      ] = await Promise.all([
        firestoreAPI.getAllVocabularyItems(),
        firestoreAPI.getAllGrammarPoints(),
        firestoreAPI.getAllGrammarGames(),
        firestoreAPI.getMetadata()
      ]);

      // Clear existing data
      await Promise.all([
        localDB.clear(STORES.VOCABULARY),
        localDB.clear(STORES.GRAMMAR),
        localDB.clear(STORES.GRAMMAR_GAMES),
        localDB.clear(STORES.METADATA)
      ]);

      // Store new data with proper ID handling
      await Promise.all([
        ...vocabulary.map(item => localDB.put(STORES.VOCABULARY, item)),
        ...grammar.map(item => localDB.put(STORES.GRAMMAR, item)),
        ...grammarGames.map(item => localDB.put(STORES.GRAMMAR_GAMES, { ...item, id: item.gameId })),
        metadata && localDB.put(STORES.METADATA, { ...metadata, id: 'categoryStructure' })
      ]);

      // Update metadata with sync timestamp
      await localDB.updateMetadata({
        lastSynced: Date.now(),
        version: '1.0'
      });
    } catch (error) {
      console.error('Error syncing course materials:', error);
      throw error;
    }
  }
}

export const courseData = new CourseDataService();
