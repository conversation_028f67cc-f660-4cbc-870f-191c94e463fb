import { SuperMemoItem, SuperMemoGrade, VocabularyProgress, GrammarProgress } from '@/types/progress';
import dayjs from 'dayjs';

// Initialize a new vocabulary progress item
export function initializeProgress(id: string): VocabularyProgress {
  return {
    id,
    interval: 0,
    repetition: 0,
    efactor: 2.5,
    lastReviewDate: dayjs().toISOString(),
    dueDate: dayjs().toISOString(),
  };
}

// Define mastery levels for vocabulary
export type VocabularyMasteryLevel = 'Novice' | 'Beginner' | 'Intermediate' | 'Advanced' | 'Mastered';

export function getVocabularyMasteryLevel(progress: VocabularyProgress): VocabularyMasteryLevel {
  const { repetition, efactor } = progress;

  if (repetition <= 1 && efactor < 1.8) {
    return 'Novice';
  } else if (repetition <= 3 && efactor < 2.0) {
    return 'Beginner';
  } else if (repetition <= 5 && efactor < 2.3) {
    return 'Intermediate';
  } else if (repetition > 5 && efactor >= 2.3 && efactor < 2.5) {
    return 'Advanced';
  } else if (repetition > 5 && efactor >= 2.5) {
    return 'Mastered';
  }
  return 'Novice'; // Default or fallback
}

// Calculate next review parameters using SM-2 algorithm
export function calculateNextReview(item: SuperMemoItem, grade: SuperMemoGrade): SuperMemoItem {
  let { interval, repetition, efactor } = item;

  // Calculate new efactor
  efactor = Math.max(
    1.3,
    efactor + (0.1 - (5 - grade) * (0.08 + (5 - grade) * 0.02))
  );

  if (grade < 3) {
    // Reset progress for failed items
    repetition = 0;
    interval = 1;
  } else {
    // Calculate next interval
    if (repetition === 0) {
      interval = 1;
    } else if (repetition === 1) {
      interval = 6;
    } else {
      interval = Math.round(interval * efactor);
    }
    repetition++;
  }

  return { interval, repetition, efactor };
}

// Update vocabulary progress with new review
export function updateProgress(
  progress: VocabularyProgress,
  grade: SuperMemoGrade
): VocabularyProgress {
  const now = dayjs();
  const { interval, repetition, efactor } = calculateNextReview(progress, grade);

  return {
    ...progress,
    interval,
    repetition,
    efactor,
    lastReviewDate: now.toISOString(),
    dueDate: now.add(interval, 'day').toISOString(),
  };
}

// Get weighted priority score for a vocabulary item
export function getPriorityScore(progress: VocabularyProgress): number {
  const now = dayjs();
  const dueDate = dayjs(progress.dueDate);
  const daysPastDue = Math.max(0, now.diff(dueDate, 'day'));
  
  // Higher priority for:
  // 1. Items that are past due (weighted by days overdue)
  // 2. Items with lower efactor (indicating difficulty)
  // 3. Items with fewer successful repetitions
  const duePriority = daysPastDue * 2;
  const difficultyPriority = (2.5 - progress.efactor) * 10;
  const repetitionPriority = Math.max(0, 5 - progress.repetition);
  // Add a significant boost for new items (repetition 0)
  const newListPriority = progress.repetition === 0 ? 20 : 0;

  return duePriority + difficultyPriority + repetitionPriority + newListPriority;
}

// Define mastery levels for grammar
export type GrammarMasteryLevel = 'Novice' | 'Beginner' | 'Intermediate' | 'Advanced' | 'Mastered';

export function getGrammarMasteryLevel(overallMastery: number): GrammarMasteryLevel {
  if (overallMastery < 0.2) {
    return 'Novice';
  } else if (overallMastery < 0.4) {
    return 'Beginner';
  } else if (overallMastery < 0.6) {
    return 'Intermediate';
  } else if (overallMastery < 0.8) {
    return 'Advanced';
  } else {
    return 'Mastered';
  }
}