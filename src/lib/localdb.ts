import { openDB, IDBPDatabase } from 'idb';

interface CourseMetadata {
  lastSynced: number;
  version: string;
}

interface VocabularyItem {
  id: string;
  character: string;
  pinyin: string;
  meaning: string;
  exampleSentences: { chinese: string; english: string }[];
  // Add other vocabulary fields as needed
}

interface GrammarItem {
  id: string;
  ruleName: string;
  explanation: string;
  examples: { chinese: string; english: string }[];
  difficultyLevel: 'easy' | 'medium' | 'hard';
  // Add other grammar fields as needed
}

interface GrammarGameItem {
  id: string;
  // Add other grammar game fields as needed
}

interface UserProfile {
  userId: string;
  lastSyncedWithFirebase?: number;
}

interface ChatSession {
  id: string;
  userId: string;
  topic: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  createdAt: number; // timestamp
  completedAt?: number; // timestamp
  status: 'active' | 'completed' | 'abandoned';
  totalSegments: number;
  completedSegments: number;
  overallScore: number;
  targetCharacters?: string[];
}

interface ChatPerformance {
  id: string;
  sessionId: string;
  segmentIndex: number;
  userTranslation: string;
  correctTranslation: string;
  isCorrect: boolean;
  score: number;
  feedback: string;
  timeSpent: number;
  attempts: number;
  createdAt: number; // timestamp
}

interface AdaptiveLearning {
  id: string;
  userId: string;
  problematicCharacters: Record<string, {
    mistakeCount: number;
    totalEncounters: number;
    mistakeTypes: string[];
    lastMistake: number; // timestamp
    masteryScore: number;
    contexts: string[];
  }>;
  learningPatterns: {
    commonMistakeTypes: string[];
    difficultyProgression: number;
    preferredTopics: string[];
  };
  lastUpdated: number; // timestamp
}

interface ChatTopic {
  id: string;
  name: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  estimatedDuration: number;
  keyVocabulary: string[];
  culturalContext?: string;
  popularity: number;
}

const DB_NAME = 'chineseAppDB';
const DB_VERSION = 3; // Increment DB_VERSION for schema changes - Added chat feature stores

const STORES = {
  VOCABULARY: 'vocabulary',
  VOCABULARY_PROGRESS: 'vocabularyProgress',
  GRAMMAR: 'grammar',
  GRAMMAR_PROGRESS: 'grammarProgress',
  GRAMMAR_GAMES: 'grammarGames',
  METADATA: 'metadata',
  USER_PROFILE: 'userProfile',
  CHAT_SESSIONS: 'chatSessions',
  CHAT_PERFORMANCE: 'chatPerformance',
  ADAPTIVE_LEARNING: 'adaptiveLearning',
  CHAT_TOPICS: 'chatTopics',
} as const;

class LocalDB {
  private db: IDBPDatabase | null = null;

  async init() {
    if (this.db) return this.db;

    this.db = await openDB(DB_NAME, DB_VERSION, {
      upgrade(db) {
        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains(STORES.VOCABULARY)) {
          db.createObjectStore(STORES.VOCABULARY, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.VOCABULARY_PROGRESS)) {
          db.createObjectStore(STORES.VOCABULARY_PROGRESS, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.GRAMMAR)) {
          db.createObjectStore(STORES.GRAMMAR, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.GRAMMAR_PROGRESS)) {
          db.createObjectStore(STORES.GRAMMAR_PROGRESS, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.GRAMMAR_GAMES)) {
          db.createObjectStore(STORES.GRAMMAR_GAMES, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.METADATA)) {
          db.createObjectStore(STORES.METADATA, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.USER_PROFILE)) {
          db.createObjectStore(STORES.USER_PROFILE, { keyPath: 'userId' });
        }
        // Chat feature stores
        if (!db.objectStoreNames.contains(STORES.CHAT_SESSIONS)) {
          db.createObjectStore(STORES.CHAT_SESSIONS, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.CHAT_PERFORMANCE)) {
          db.createObjectStore(STORES.CHAT_PERFORMANCE, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.ADAPTIVE_LEARNING)) {
          db.createObjectStore(STORES.ADAPTIVE_LEARNING, { keyPath: 'id' });
        }
        if (!db.objectStoreNames.contains(STORES.CHAT_TOPICS)) {
          db.createObjectStore(STORES.CHAT_TOPICS, { keyPath: 'id' });
        }
      },
    });

    return this.db;
  }

  // Generic CRUD operations
  private async getStore(storeName: string) {
    const db = await this.init();
    return db.transaction(storeName, 'readwrite').objectStore(storeName);
  }

  async add<T>(storeName: string, item: T) {
    const store = await this.getStore(storeName);
    return store.add(item);
  }

  async get<T>(storeName: string, id: string): Promise<T | undefined> {
    const store = await this.getStore(storeName);
    return store.get(id);
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    const store = await this.getStore(storeName);
    return store.getAll();
  }

  async put<T extends { id?: string }>(storeName: string, item: T) {
    if (!item.id) {
      throw new Error(`Cannot store item in ${storeName}: missing required 'id' field`);
    }
    const store = await this.getStore(storeName);
    return store.put(item);
  }

  async delete(storeName: string, id: string) {
    const store = await this.getStore(storeName);
    return store.delete(id);
  }

  async clear(storeName: string) {
    const store = await this.getStore(storeName);
    return store.clear();
  }

  // Metadata operations
  async updateMetadata(metadata: Partial<CourseMetadata>) {
    const store = await this.getStore(STORES.METADATA);
    const currentMetadata = await store.get('courseMetadata') || {};
    const updatedMetadata = { ...currentMetadata, ...metadata };
    return store.put({ id: 'courseMetadata', ...updatedMetadata });
  }

  async getMetadata(): Promise<CourseMetadata | undefined> {
    const store = await this.getStore(STORES.METADATA);
    return store.get('courseMetadata');
  }

  // Data freshness check
  async isDataFresh(): Promise<boolean> {
    const metadata = await this.getMetadata();
    if (!metadata?.lastSynced) return false;

    // Consider data fresh if synced within the last 24 hours
    const ONE_DAY = 24 * 60 * 60 * 1000;
    return Date.now() - metadata.lastSynced < ONE_DAY;
  }
}

export const localDB = new LocalDB();
export { STORES };
export type {
  VocabularyItem,
  GrammarItem,
  GrammarGameItem,
  CourseMetadata,
  UserProfile,
  ChatSession,
  ChatPerformance,
  AdaptiveLearning,
  ChatTopic
};