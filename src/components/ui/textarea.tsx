import * as React from "react";
import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import useSpeechToText from "@/hooks/useSpeechToText";
import { Mic, MicOff } from 'lucide-react';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  enableStt?: boolean;
  sttLang?: string;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, enableStt = false, sttLang = 'zh-CN', onChange, value, ...props }, ref) => {
    const {
      isListening,
      transcript,
      error: sttError,
      startListening,
      stopListening,
      clearError: clearSttError,
      browserSupportsSpeechRecognition,
    } = useSpeechToText({ lang: sttLang });

    const internalRef = useRef<HTMLTextAreaElement>(null);
    React.useImperativeHandle(ref, () => internalRef.current as HTMLTextAreaElement);

    useEffect(() => {
      if (sttError) clearSttError(); // Clear error when user types
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]); // Clear error when the input value changes externally too

    useEffect(() => {
      if (enableStt && transcript) {
        if (internalRef.current) {
          const event = {
            target: { value: transcript },
            currentTarget: { value: transcript },
          } as React.ChangeEvent<HTMLTextAreaElement>;
          onChange?.(event);
          if (sttError) clearSttError(); // Clear error once transcript is applied
        }
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [transcript, enableStt, onChange]);

    const handleMicClick = () => {
      if (!browserSupportsSpeechRecognition) return;
      if (sttError) clearSttError(); // Clear error before toggling mic
      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    };

    return (
      <div className="w-full"> 
      <div className={cn("relative w-full")}>
        <textarea
          ref={internalRef}
          value={enableStt ? (isListening ? transcript : value) : value}
          onChange={onChange}
          className={cn(
            "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            {
              "pr-10": enableStt && browserSupportsSpeechRecognition // Add padding for the icon if enabled
            },
            className
          )}
          {...props}
        />
        {enableStt && browserSupportsSpeechRecognition && (
          <button
            type="button"
            onClick={handleMicClick}
            className={cn(
              "absolute right-2 top-2 p-1 rounded-full hover:bg-accent", // Adjusted position for textarea
              isListening ? "text-primary" : "text-muted-foreground"
            )}
            aria-label={isListening ? "Stop listening" : "Start listening"}
          >
            {isListening ? <MicOff size={18} /> : <Mic size={18} />}
          </button>
        )}
      </div>
      {enableStt && sttError && (
        <p className="text-xs text-destructive mt-1">{sttError}</p>
      )}
      </div>
    );
  }
);
Textarea.displayName = "Textarea";

export { Textarea };