import * as React from "react";
import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import useSpeechToText from "@/hooks/useSpeechToText"; // Assuming the hook is in this path
import { <PERSON><PERSON>, <PERSON>cO<PERSON> } from 'lucide-react'; // Import microphone icons

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  enableStt?: boolean;
  sttLang?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, enableStt = false, sttLang = 'zh-CN', onChange, value, ...props }, ref) => {
    const {
      isListening,
      transcript,
      error: sttError,
      startListening,
      stopListening,
      clearError: clearSttError,
      browserSupportsSpeechRecognition,
    } = useSpeechToText({ lang: sttLang });

    const internalRef = useRef<HTMLInputElement>(null);
    React.useImperativeHandle(ref, () => internalRef.current as HTMLInputElement);

    // Update input value when transcript changes
    useEffect(() => {
      if (sttError) clearSttError(); // Clear error when user types or transcript updates
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]); // Clear error when the input value changes externally too

    useEffect(() => {
      if (enableStt && transcript) {
        if (internalRef.current) {
          // Create a synthetic event for onChange
          const event = {
            target: { value: transcript },
            currentTarget: { value: transcript },
          } as React.ChangeEvent<HTMLInputElement>;
          onChange?.(event);
          if (sttError) clearSttError(); // Clear error once transcript is applied
        }
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [transcript, enableStt, onChange]);

    const handleMicClick = () => {
      if (!browserSupportsSpeechRecognition) return;
      if (sttError) clearSttError(); // Clear error before toggling mic
      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    };

    return (
      <div className="w-full">
      <div className={cn("relative flex items-center w-full", {
        "pr-10": enableStt && browserSupportsSpeechRecognition // Add padding for the icon
      })}>
        <input
          type={type}
          ref={internalRef}
          value={enableStt ? (isListening ? transcript : value) : value} // Show transcript while listening or if it's the source of truth
          onChange={onChange} // Ensure original onChange is still callable
          data-slot="input"
          className={cn(
            "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
            "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
            className
          )}
          {...props}
        />
        {enableStt && browserSupportsSpeechRecognition && (
          <button
            type="button"
            onClick={handleMicClick}
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-accent",
              isListening ? "text-primary" : "text-muted-foreground"
            )}
            aria-label={isListening ? "Stop listening" : "Start listening"}
          >
            {isListening ? <MicOff size={18} /> : <Mic size={18} />}
          </button>
        )}
      </div>
      {enableStt && sttError && (
        <p className="text-xs text-destructive mt-1 ml-1">{sttError}</p>
      )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
