"use client";

import React, { ReactNode } from 'react';
import useTextToSpeech from '@/hooks/useTextToSpeech';
import { Volume2, VolumeX } from 'lucide-react'; // Speaker icons
import { cn } from '@/lib/utils';

interface TextWithTTSProps {
  text?: string;
  children?: ReactNode;
  lang?: string; // Optional: override default language for this instance
  className?: string;
  iconSize?: number;
  iconClassName?: string;
}

const TextWithTTS: React.FC<TextWithTTSProps> = ({
  text,
  children,
  lang = 'zh-CN', // Default to Chinese
  className,
  iconSize = 18,
  iconClassName
}) => {
  const { isPlaying, speak, cancel, error: ttsError, clearError: clearTtsError, browserSupportsSpeechSynthesis } = useTextToSpeech({ defaultLang: lang });

  const content = text || children;

  if (!browserSupportsSpeechSynthesis || !content) {
    return <span className={className}>{content}</span>;
  }

  const handleSpeakerClick = () => { // Removed 'e' parameter as it's no longer used
    // e.stopPropagation(); // Removed to allow event bubbling for game interaction
    clearTtsError(); // Clear any previous errors
    if (typeof content === 'string' && content.trim() !== '') {
      if (isPlaying) {
        cancel();
      } else {
        speak(content, lang);
      }
    } else if (React.isValidElement(content)) {
        // Attempt to extract text from children if it's a simple case
        let textToSpeak = '';
        React.Children.forEach(content, child => {
            if (typeof child === 'string') {
                textToSpeak += child;
            } else if (React.isValidElement(child) && child.props && typeof child.props.children === 'string') {
                textToSpeak += child.props.children;
            }
            // Add more complex extraction logic if needed, e.g., for nested elements
        });
        if (textToSpeak.trim() !== '') {
            if (isPlaying) {
                cancel();
            } else {
                speak(textToSpeak, lang);
            }
        }
    }
  };

  // Determine if content is purely textual or contains other React elements
  // const isTextOnly = typeof content === 'string' || (Array.isArray(content) && content.every(c => typeof c === 'string')); // Removed as unused

  return (
    <span className="inline-flex flex-col">
      <span className={cn("inline-flex items-center", className)}>
        {content}
        <span
          role="button"
          tabIndex={0}
          onClick={() => handleSpeakerClick()}
          onKeyDown={(e: React.KeyboardEvent) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleSpeakerClick();
            }
          }}
          className={cn(
            "ml-1 p-0.5 rounded-full hover:bg-accent focus:outline-none focus:ring-1 focus:ring-ring",
            isPlaying ? "text-primary" : "text-muted-foreground",
            iconClassName
          )}
          aria-label={isPlaying ? "Stop speaking" : "Speak text"}
          title={isPlaying ? "Stop speaking" : "Speak text"}
        >
          {isPlaying ? <VolumeX size={iconSize} /> : <Volume2 size={iconSize} />}
        </span>
    </span>
    {ttsError && (
        <p className="text-xs text-red-500 mt-1 ml-1" role="alert">
          {ttsError}
        </p>
      )}
    </span>
  );
};

export default TextWithTTS;