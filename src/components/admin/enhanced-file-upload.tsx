"use client";

import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UploadIcon, FileTextIcon, XIcon, Loader2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";

interface EnhancedFileUploadProps {
  onFileUpload: (file: File) => Promise<void>;
  isProcessing: boolean;
  acceptedFileTypes?: string;
  maxFileSizeMB?: number;
}

export function EnhancedFileUpload({ 
  onFileUpload, 
  isProcessing, 
  acceptedFileTypes = ".txt,.md,.docx,.pdf,.json",
  maxFileSizeMB = 10
}: EnhancedFileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxFileSizeBytes = maxFileSizeMB * 1024 * 1024;

  const validateFile = (file: File): boolean => {
    // Reset error state
    setError(null);
    
    // Check file size
    if (file.size > maxFileSizeBytes) {
      setError(`File size exceeds the maximum limit of ${maxFileSizeMB}MB`);
      return false;
    }
    
    // Check file type if acceptedFileTypes is provided
    if (acceptedFileTypes) {
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
      const acceptedTypes = acceptedFileTypes.split(',');
      
      if (!acceptedTypes.includes(fileExtension) && !acceptedTypes.includes('.*')) {
        setError(`File type not supported. Accepted types: ${acceptedFileTypes}`);
        return false;
      }
    }
    
    return true;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        // Reset progress when a new file is selected
        setUploadProgress(0);
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        // Reset progress when a new file is dropped
        setUploadProgress(0);
      }
    }
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    setError(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleSubmit = async () => {
    if (selectedFile) {
      try {
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return prev;
            }
            return prev + 10;
          });
        }, 300);

        // Process the file
        await onFileUpload(selectedFile);
        
        // Complete the progress
        clearInterval(progressInterval);
        setUploadProgress(100);
      } catch (error) {
        setError(`Error processing file: ${error instanceof Error ? error.message : String(error)}`);
        setUploadProgress(0);
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload File</CardTitle>
        <CardDescription>
          Upload a file to process with AI
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label>Upload File</Label>
          <div
            className={`border-2 border-dashed rounded-md p-6 text-center transition-colors ${
              dragActive ? "border-primary bg-primary/5" : 
              selectedFile ? "border-primary" : 
              error ? "border-destructive" : "border-gray-300"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {!selectedFile ? (
              <div className="space-y-2">
                <div className="flex justify-center">
                  <UploadIcon className={`h-10 w-10 ${error ? "text-destructive" : "text-gray-400"}`} />
                </div>
                <div className="text-sm text-gray-600">
                  <p>Drag and drop a file here, or</p>
                  <Button
                    variant="ghost"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-primary"
                  >
                    browse for a file
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Supported formats: {acceptedFileTypes}
                </p>
                <p className="text-xs text-gray-500">
                  Maximum file size: {maxFileSizeMB}MB
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={acceptedFileTypes}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileTextIcon className="h-6 w-6 text-primary" />
                    <div className="text-sm text-left">
                      <p className="font-medium">{selectedFile.name}</p>
                      <p className="text-xs text-gray-500">
                        {(selectedFile.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFile}
                    disabled={isProcessing}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                </div>
                
                {uploadProgress > 0 && (
                  <div className="space-y-1">
                    <Progress value={uploadProgress} className="h-2" />
                    <p className="text-xs text-right text-gray-500">{uploadProgress}%</p>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {error && (
            <p className="text-sm text-destructive mt-2">{error}</p>
          )}
        </div>

        <Button
          onClick={handleSubmit}
          disabled={!selectedFile || isProcessing}
          className="w-full"
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            "Process with AI"
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

export default EnhancedFileUpload;
