"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { VocabularyItem, isVocabularyItem } from "@/types/content";

interface VocabularyTableProps {
  items: unknown[];
  isLoading: boolean;
  onEdit: (item: VocabularyItem) => void;
  onDelete: (item: VocabularyItem) => void;
}

export function VocabularyTable({ items, isLoading, onEdit, onDelete }: VocabularyTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Chapter</TableHead>
          <TableHead>Word</TableHead>
          <TableHead>Pinyin</TableHead>
          <TableHead>Part of Speech</TableHead>
          <TableHead className="w-[150px]">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.length === 0 ? (
          <TableRow>
            <TableCell colSpan={5} className="text-center py-4">
              {isLoading ? "Loading..." : "No vocabulary items found"}
            </TableCell>
          </TableRow>
        ) : (
          items.map((item) => {
            if (!isVocabularyItem(item)) return null;
            return (
              <TableRow key={item.id}>
                <TableCell>{item.chapterNumber}</TableCell>
                <TableCell>{item.word}</TableCell>
                <TableCell>{item.pinyin}</TableCell>
                <TableCell>{item.partOfSpeech}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => onEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => onDelete(item)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })
        )}
      </TableBody>
    </Table>
  );
}

export default VocabularyTable;
