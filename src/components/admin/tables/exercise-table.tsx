"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { Exercise, isExercise } from "@/types/content";

interface ExerciseTableProps {
  items: unknown[];
  isLoading: boolean;
  onEdit: (item: Exercise) => void;
  onDelete: (item: Exercise) => void;
}

export function ExerciseTable({ items, isLoading, onEdit, onDelete }: ExerciseTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Type</TableHead>
          <TableHead>Prompt</TableHead>
          <TableHead>Difficulty</TableHead>
          <TableHead className="w-[150px]">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center py-4">
              {isLoading ? "Loading..." : "No exercises found"}
            </TableCell>
          </TableRow>
        ) : (
          items.map((item) => {
            if (!isExercise(item)) return null;
            return (
              <TableRow key={item.id}>
                <TableCell>{item.type}</TableCell>
                <TableCell>{typeof item.prompt === 'string' ? item.prompt.substring(0, 50) + '...' : ''}</TableCell>
                <TableCell>{item.difficulty}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => onEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => onDelete(item)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })
        )}
      </TableBody>
    </Table>
  );
}

export default ExerciseTable;
