"use client";

import { Table, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { GrammarPoint, isGrammarPoint } from "@/types/content";

interface GrammarTableProps {
  items: unknown[];
  isLoading: boolean;
  onEdit: (item: GrammarPoint) => void;
  onDelete: (item: GrammarPoint) => void;
}

export function GrammarTable({ items, isLoading, onEdit, onDelete }: GrammarTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Chapter</TableHead>
          <TableHead>Title</TableHead>
          <TableHead>Difficulty</TableHead>
          <TableHead className="w-[150px]">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center py-4">
              {isLoading ? "Loading..." : "No grammar points found"}
            </TableCell>
          </TableRow>
        ) : (
          items.map((item) => {
            if (!isGrammarPoint(item)) return null;
            return (
              <TableRow key={item.id}>
                <TableCell>{item.chapterNumber}</TableCell>
                <TableCell>{item.title}</TableCell>
                <TableCell>{item.difficulty}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => onEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => onDelete(item)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })
        )}
      </TableBody>
    </Table>
  );
}

export default GrammarTable;
