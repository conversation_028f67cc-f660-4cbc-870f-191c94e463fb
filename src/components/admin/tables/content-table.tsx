"use client";

import { ContentItem, ContentTab, GrammarPoint, VocabularyItem, Exercise } from "@/types/content";
import { GrammarTable } from "./grammar-table";
import { VocabularyTable } from "./vocabulary-table";
import { ExerciseTable } from "./exercise-table";

interface ContentTableProps {
  contentType: ContentTab;
  items: ContentItem[];
  isLoading: boolean;
  onEdit: (item: ContentItem) => void;
  onDelete: (item: ContentItem) => void;
}

export function ContentTable({ contentType, items, isLoading, onEdit, onDelete }: ContentTableProps) {
  switch (contentType) {
    case 'grammar':
      return (
        <GrammarTable 
          items={items} 
          isLoading={isLoading} 
          onEdit={(item) => onEdit(item)} 
          onDelete={(item) => onDelete(item)} 
        />
      );
    case 'vocabulary':
      return (
        <VocabularyTable 
          items={items} 
          isLoading={isLoading} 
          onEdit={(item) => onEdit(item)} 
          onDelete={(item) => onDelete(item)} 
        />
      );
    case 'exercises':
      return (
        <ExerciseTable 
          items={items} 
          isLoading={isLoading} 
          onEdit={(item) => onEdit(item)} 
          onDelete={(item) => onDelete(item)} 
        />
      );
    default:
      return null;
  }
}

export default ContentTable;
