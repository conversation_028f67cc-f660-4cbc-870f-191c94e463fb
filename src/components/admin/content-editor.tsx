"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { GrammarPoint, VocabularyItem } from "@/lib/llm-service";

// Define ContentItem type for compatibility with admin content page
interface ContentItem {
  id: string;
  chapterNumber: number;
  chapterTitle: string;
  [key: string]: any;
}

// Create a type alias for the union type
type ContentArray = ContentItem[] | GrammarPoint[] | VocabularyItem[];

interface ContentEditorProps {
  isOpen: boolean;
  onClose: () => void;
  content: ContentArray;
  contentType: 'grammar' | 'vocabulary' | 'exercises';
  onSave: (content: ContentArray) => void;
}

export function ContentEditor({
  isOpen,
  onClose,
  content,
  contentType,
  onSave,
}: Readonly<ContentEditorProps>) {
  const [editedContent, setEditedContent] = useState<ContentArray>([]);
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);
  const [jsonMode, setJsonMode] = useState(false);
  const [jsonValue, setJsonValue] = useState("");
  const [jsonError, setJsonError] = useState("");

  useEffect(() => {
    if (isOpen) {
      setEditedContent(JSON.parse(JSON.stringify(content))); // Deep copy
      setJsonValue(JSON.stringify(content, null, 2));
      setSelectedItemIndex(0);
      setJsonMode(false);
      setJsonError("");
    }
  }, [isOpen, content]);

  const handleSave = () => {
    if (jsonMode) {
      try {
        const parsedContent = JSON.parse(jsonValue);
        onSave(parsedContent);
        onClose();
      } catch (error) {
        console.error("JSON parsing error:", error);
        setJsonError("Invalid JSON format. Please check your syntax.");
      }
    } else {
      onSave(editedContent);
      onClose();
    }
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    const newContent = [...editedContent];
    (newContent[index] as any)[field] = value;
    setEditedContent(newContent as ContentArray);
    setJsonValue(JSON.stringify(newContent, null, 2));
  };

  const handleExampleChange = (itemIndex: number, exampleIndex: number, field: string, value: string) => {
    const newContent = [...editedContent];
    (newContent[itemIndex] as any).examples[exampleIndex][field] = value;
    setEditedContent(newContent as ContentArray);
    setJsonValue(JSON.stringify(newContent, null, 2));
  };

  const handleDefinitionChange = (itemIndex: number, defIndex: number, value: string) => {
    const newContent = [...editedContent];
    (newContent[itemIndex] as VocabularyItem).definitions[defIndex] = value;
    setEditedContent(newContent as ContentArray);
    setJsonValue(JSON.stringify(newContent, null, 2));
  };

  const renderGrammarPointEditor = (point: GrammarPoint, index: number) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={point.title}
              onChange={(e) => handleItemChange(index, "title", e.target.value)}
              enableStt={true}
              sttLang="zh-CN"
            />
          </div>
          <div>
            <Label htmlFor="difficulty">Difficulty</Label>
            <select
              id="difficulty"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={point.difficulty}
              onChange={(e) => handleItemChange(index, "difficulty", e.target.value)}
            >
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>

        <div>
          <Label htmlFor="explanation">Explanation</Label>
          <Textarea
            id="explanation"
            className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={point.explanation}
            onChange={(e) => handleItemChange(index, "explanation", e.target.value)}
            enableStt={true}
            sttLang="zh-CN"
          />
        </div>

        <div>
          <Label>Examples</Label>
          {point.examples.map((example, exampleIndex) => (
            <div key={`grammar-example-${index}-${exampleIndex}`} className="space-y-2 mt-2 p-3 border rounded-md">
              <div>
                <Label htmlFor={`chinese-${index}-${exampleIndex}`}>Chinese</Label>
                <Input
                  id={`chinese-${index}-${exampleIndex}`}
                  value={example.chinese}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "chinese", e.target.value)}
                  enableStt={true}
                  sttLang="zh-CN"
                />
              </div>
              <div>
                <Label htmlFor={`pinyin-${index}-${exampleIndex}`}>Pinyin</Label>
                <Input
                  id={`pinyin-${index}-${exampleIndex}`}
                  value={example.pinyin}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "pinyin", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor={`english-${index}-${exampleIndex}`}>English</Label>
                <Input
                  id={`english-${index}-${exampleIndex}`}
                  value={example.english}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "english", e.target.value)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderVocabularyItemEditor = (item: VocabularyItem, index: number) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="word">Word</Label>
            <Input
              id="word"
              value={item.word}
              onChange={(e) => handleItemChange(index, "word", e.target.value)}
              enableStt={true}
              sttLang="zh-CN"
            />
          </div>
          <div>
            <Label htmlFor="pinyin">Pinyin</Label>
            <Input
              id="pinyin"
              value={item.pinyin}
              onChange={(e) => handleItemChange(index, "pinyin", e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="partOfSpeech">Part of Speech</Label>
            <Input
              id="partOfSpeech"
              value={item.partOfSpeech}
              onChange={(e) => handleItemChange(index, "partOfSpeech", e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="difficulty">Difficulty</Label>
            <select
              id="difficulty"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={item.difficulty}
              onChange={(e) => handleItemChange(index, "difficulty", e.target.value)}
            >
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>

        <div>
          <Label>Definitions</Label>
          {item.definitions.map((definition, defIndex) => (
            <div key={`vocab-def-${index}-${defIndex}`} className="mt-2">
              <Input
                value={definition}
                onChange={(e) => handleDefinitionChange(index, defIndex, e.target.value)}
                enableStt={true}
                sttLang="zh-CN"
              />
            </div>
          ))}
        </div>

        <div>
          <Label>Examples</Label>
          {item.examples.map((example, exampleIndex) => (
            <div key={`vocab-example-${index}-${exampleIndex}`} className="space-y-2 mt-2 p-3 border rounded-md">
              <div>
                <Label htmlFor={`vocab-chinese-${index}-${exampleIndex}`}>Chinese</Label>
                <Input
                  id={`vocab-chinese-${index}-${exampleIndex}`}
                  value={example.chinese}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "chinese", e.target.value)}
                  enableStt={true}
                  sttLang="zh-CN"
                />
              </div>
              <div>
                <Label htmlFor={`vocab-pinyin-${index}-${exampleIndex}`}>Pinyin</Label>
                <Input
                  id={`vocab-pinyin-${index}-${exampleIndex}`}
                  value={example.pinyin}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "pinyin", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor={`vocab-english-${index}-${exampleIndex}`}>English</Label>
                <Input
                  id={`vocab-english-${index}-${exampleIndex}`}
                  value={example.english}
                  onChange={(e) => handleExampleChange(index, exampleIndex, "english", e.target.value)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderJsonEditor = () => {
    return (
      <div className="space-y-4">
        <textarea
          className="flex min-h-[400px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm font-mono ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          value={jsonValue}
          onChange={(e) => {
            setJsonValue(e.target.value);
            setJsonError("");
          }}
        />
        {jsonError && <p className="text-destructive text-sm">{jsonError}</p>}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Edit {contentType === 'grammar' ? 'Grammar Points' : 'Vocabulary Items'}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="form" onValueChange={(value) => setJsonMode(value === 'json')}>
          <TabsList>
            <TabsTrigger value="form">Form View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>

          <TabsContent value="form" className="space-y-4 mt-4">
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {editedContent.map((item, index) => {
                // Extract the title display logic to avoid nested ternary
                let displayText = '';
                if (contentType === 'grammar') {
                  const title = (item as GrammarPoint).title;
                  displayText = title.substring(0, 15);
                  if (title.length > 15) {
                    displayText += '...';
                  }
                } else {
                  displayText = (item as VocabularyItem).word;
                }

                // Generate a more unique key using index and content
                const itemKey = contentType === 'grammar'
                  ? `grammar-${index}-${(item as GrammarPoint).pointId || index}`
                  : `vocab-${index}-${(item as VocabularyItem).itemId || index}`;

                return (
                  <Button
                    key={itemKey}
                    variant={selectedItemIndex === index ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedItemIndex(index)}
                  >
                    {displayText}
                  </Button>
                );
              })}
            </div>

            {editedContent.length > 0 && (
              contentType === 'grammar'
                ? renderGrammarPointEditor(editedContent[selectedItemIndex] as GrammarPoint, selectedItemIndex)
                : renderVocabularyItemEditor(editedContent[selectedItemIndex] as VocabularyItem, selectedItemIndex)
            )}
          </TabsContent>

          <TabsContent value="json" className="mt-4">
            {renderJsonEditor()}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default ContentEditor;
