"use client";

import { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UploadIcon, FileTextIcon, XIcon } from "lucide-react";

interface FileUploadProps {
  onFileUpload: (file: File, contentType: 'grammar' | 'vocabulary', chapterNumber: number) => void;
  isProcessing: boolean;
}

export function FileUpload({ onFileUpload, isProcessing }: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [contentType, setContentType] = useState<'grammar' | 'vocabulary'>('grammar');
  const [chapterNumber, setChapterNumber] = useState<number>(1);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleSubmit = () => {
    if (selectedFile) {
      onFileUpload(selectedFile, contentType, chapterNumber);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Learning Material</CardTitle>
        <CardDescription>
          Upload a file containing grammar points or vocabulary items to process with AI
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label>Content Type</Label>
          <div className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="grammar"
                name="contentType"
                value="grammar"
                checked={contentType === 'grammar'}
                onChange={() => setContentType('grammar')}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="grammar" className="text-sm font-normal">Grammar Points</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="vocabulary"
                name="contentType"
                value="vocabulary"
                checked={contentType === 'vocabulary'}
                onChange={() => setContentType('vocabulary')}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="vocabulary" className="text-sm font-normal">Vocabulary Items</Label>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="chapterNumber">Chapter Number</Label>
          <Input
            id="chapterNumber"
            type="number"
            min="1"
            max="20"
            value={chapterNumber}
            onChange={(e) => setChapterNumber(parseInt(e.target.value) || 1)}
          />
        </div>

        <div className="space-y-2">
          <Label>Upload File</Label>
          <div
            className={`border-2 border-dashed rounded-md p-6 text-center ${
              selectedFile ? "border-primary" : "border-gray-300"
            }`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {!selectedFile ? (
              <div className="space-y-2">
                <div className="flex justify-center">
                  <UploadIcon className="h-10 w-10 text-gray-400" />
                </div>
                <div className="text-sm text-gray-600">
                  <p>Drag and drop a file here, or</p>
                  <Button
                    variant="ghost"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-primary"
                  >
                    browse for a file
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Supported formats: .txt, .md, .docx, .pdf
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".txt,.md,.docx,.pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileTextIcon className="h-6 w-6 text-primary" />
                  <div className="text-sm text-left">
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500">
                      {(selectedFile.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFile}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>

        <Button
          onClick={handleSubmit}
          disabled={!selectedFile || isProcessing}
          className="w-full"
        >
          {isProcessing ? "Processing..." : "Process with AI"}
        </Button>
      </CardContent>
    </Card>
  );
}

export default FileUpload;
