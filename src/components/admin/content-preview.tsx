"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GrammarPoint, VocabularyItem } from "@/lib/llm-service";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

interface ContentPreviewProps {
  content: GrammarPoint[] | VocabularyItem[];
  contentType: 'grammar' | 'vocabulary';
  onEdit: (content: GrammarPoint[] | VocabularyItem[]) => void;
  onSave: () => void;
  isSaving: boolean;
}

export function ContentPreview({ 
  content, 
  contentType, 
  onEdit, 
  onSave, 
  isSaving 
}: ContentPreviewProps) {
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'json'>('list');

  const handleItemClick = (index: number) => {
    setSelectedItemIndex(index);
  };

  const renderGrammarPointsList = (grammarPoints: GrammarPoint[]) => {
    return (
      <div className="space-y-4">
        {grammarPoints.map((point, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer ${selectedItemIndex === index ? 'border-primary' : ''}`}
            onClick={() => handleItemClick(index)}
          >
            <CardHeader className="p-4">
              <CardTitle className="text-lg">{point.title}</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <p className="text-sm text-muted-foreground mb-2">
                Chapter {point.chapterNumber} | {point.difficulty}
              </p>
              <p className="text-sm mb-2">{point.explanation.substring(0, 150)}...</p>
              {point.examples.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium">Example:</p>
                  <p className="text-sm">{point.examples[0].chinese}</p>
                  <p className="text-sm text-muted-foreground">{point.examples[0].pinyin}</p>
                  <p className="text-sm">{point.examples[0].english}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderVocabularyItemsList = (vocabularyItems: VocabularyItem[]) => {
    return (
      <div className="space-y-4">
        {vocabularyItems.map((item, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer ${selectedItemIndex === index ? 'border-primary' : ''}`}
            onClick={() => handleItemClick(index)}
          >
            <CardHeader className="p-4">
              <CardTitle className="text-lg">{item.word} <span className="text-sm text-muted-foreground">({item.pinyin})</span></CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <p className="text-sm text-muted-foreground mb-2">
                Chapter {item.chapterNumber} | {item.partOfSpeech} | {item.difficulty}
              </p>
              <div className="mb-2">
                {item.definitions.map((def, i) => (
                  <p key={i} className="text-sm">{i + 1}. {def}</p>
                ))}
              </div>
              {item.examples.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium">Example:</p>
                  <p className="text-sm">{item.examples[0].chinese}</p>
                  <p className="text-sm text-muted-foreground">{item.examples[0].pinyin}</p>
                  <p className="text-sm">{item.examples[0].english}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderJsonView = () => {
    return (
      <div className="relative">
        <pre className="p-4 bg-muted rounded-md overflow-auto max-h-[500px] text-xs">
          {JSON.stringify(content, null, 2)}
        </pre>
        <Button 
          variant="outline" 
          size="sm" 
          className="absolute top-2 right-2"
          onClick={() => {
            navigator.clipboard.writeText(JSON.stringify(content, null, 2));
          }}
        >
          Copy
        </Button>
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          Preview {contentType === 'grammar' ? 'Grammar Points' : 'Vocabulary Items'}
        </CardTitle>
        <Tabs defaultValue="list" onValueChange={(value) => setViewMode(value as 'list' | 'json')}>
          <TabsList>
            <TabsTrigger value="list">List View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        {content.length === 0 ? (
          <div className="flex justify-center py-8">
            <p>No content to preview</p>
          </div>
        ) : viewMode === 'list' ? (
          contentType === 'grammar' 
            ? renderGrammarPointsList(content as GrammarPoint[])
            : renderVocabularyItemsList(content as VocabularyItem[])
        ) : (
          renderJsonView()
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={() => onEdit(content)}
          disabled={isSaving}
        >
          Edit Content
        </Button>
        <Button 
          onClick={onSave}
          disabled={content.length === 0 || isSaving}
        >
          {isSaving ? 'Saving...' : 'Save to Database'}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default ContentPreview;
