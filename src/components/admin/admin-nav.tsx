"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";

export function AdminNav() {
  const pathname = usePathname();

  const navItems = [
    {
      title: "Dashboard",
      href: "/admin/dashboard",
      description: "Overview of admin functions"
    },
    {
      title: "Learning Materials",
      href: "/admin/materials",
      description: "Manage learning materials"
    },
    {
      title: "Content Processor",
      href: "/admin/content-processor",
      description: "Process any content with AI"
    },
    {
      title: "Content Management",
      href: "/admin/content",
      description: "View and edit existing content"
    }
  ];

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-1">
          <h3 className="text-sm font-medium">Admin Navigation</h3>
          <nav className="grid gap-1">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`flex flex-col space-y-1 rounded-md p-3 text-sm transition-colors ${
                  pathname === item.href
                    ? "bg-muted font-medium"
                    : "hover:bg-muted"
                }`}
              >
                <div className="font-medium">{item.title}</div>
                <div className="text-xs text-muted-foreground">
                  {item.description}
                </div>
              </Link>
            ))}
          </nav>
        </div>
      </CardContent>
    </Card>
  );
}

export default AdminNav;
