'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { VocabularyItem as OriginalVocabularyItem } from '../../types/content';
import { startVocabularyChatExercise, continueVocabularyChat, ChatTurnSchema, AIResponseSchema } from '../../lib/ai-service';
import { z } from 'zod';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import TextWithTTS from '../ui/TextWithTTS';

type ChatTurn = z.infer<typeof ChatTurnSchema>;

// Define a serializable version of VocabularyItem for client components
interface SerializableVocabularyItem extends Omit<OriginalVocabularyItem, 'createdAt' | 'word' | 'pinyin'> {
  createdAt: string | null;
  word: string;
  pinyin: string;
  definitions: string[];
  examples: { chinese: string; pinyin: string; english: string; }[];
}

interface InteractiveVocabularyChatProps {
  vocabularyItem: SerializableVocabularyItem;
  onEndChat: () => void;
}

const InteractiveVocabularyChat: React.FC<InteractiveVocabularyChatProps> = ({ vocabularyItem, onEndChat }) => {
  const [chatHistory, setChatHistory] = useState<ChatTurn[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeChat = useCallback(async () => {
    if (!vocabularyItem) return;
    setIsLoading(true);
    setError(null);
    try {
      const initialResponse = await startVocabularyChatExercise(
        vocabularyItem.word,
        vocabularyItem.pinyin,
        vocabularyItem.definitions,
        vocabularyItem.examples,
        { responseSchema: AIResponseSchema }
      );
      setChatHistory([{ role: 'assistant', content: initialResponse.nextAIMessage }]);
    } catch (e) {
      console.error("Failed to start vocabulary chat exercise:", e);
      setError(e instanceof Error ? e.message : 'Failed to start chat. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [vocabularyItem]);

  useEffect(() => {
    initializeChat();
  }, [initializeChat]);

  const handleSendMessage = async () => {
    if (!userInput.trim()) return;

    const newUserTurn: ChatTurn = { role: 'user', content: userInput };
    const currentChatHistory = [...chatHistory, newUserTurn];
    setChatHistory(currentChatHistory);
    setUserInput('');
    setIsLoading(true);
    setError(null);

    try {
      const aiResponse = await continueVocabularyChat(
        vocabularyItem.word,
        vocabularyItem.pinyin,
        vocabularyItem.definitions,
        vocabularyItem.examples,
        currentChatHistory,
        userInput,
        { responseSchema: AIResponseSchema }
      );
      
      const updatedHistoryWithEvaluation = currentChatHistory.map((turn, index) => {
        if (index === currentChatHistory.length - 1 && turn.role === 'user') {
          return { ...turn, evaluation: aiResponse.evaluation };
        }
        return turn;
      });

      setChatHistory([
        ...updatedHistoryWithEvaluation,
        { role: 'assistant', content: aiResponse.nextAIMessage },
      ]);
    } catch (e) {
      console.error("Failed to get AI response:", e);
      setError(e instanceof Error ? e.message : 'Failed to get response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full p-4 space-y-4 bg-gray-50 rounded-lg shadow">
      <div className="flex-grow overflow-y-auto space-y-2 p-2 border rounded-md bg-white">
        {isLoading && chatHistory.length === 0 && !error && (
          <div className="flex justify-center items-center h-full">
            <p className="text-gray-500">Initializing chat with AI assistant...</p>
          </div>
        )}
        {!isLoading && chatHistory.length === 0 && !error && (
          <div className="flex justify-center items-center h-full">
            <p className="text-gray-500">Chat started. Send a message to begin!</p>
          </div>
        )}
        {chatHistory.map((turn, index) => (
          <div key={index} className={`p-2 rounded-md ${turn.role === 'user' ? 'bg-blue-100 ml-auto' : 'bg-green-100 mr-auto'}`} style={{ maxWidth: '80%' }}>
            <p className="text-sm font-semibold">{turn.role === 'user' ? 'You' : 'AI Assistant'}</p>
            <TextWithTTS text={turn.content} lang="zh-CN" className="whitespace-pre-wrap" iconSize={16} />
            {turn.role === 'user' && turn.evaluation && (
              <div className="mt-1 p-2 border-t border-gray-300 bg-yellow-50 rounded-b-md">
                <p className="text-xs font-bold">Evaluation:</p>
                <p className="text-xs">Score: {turn.evaluation.score}/100</p>
                <p className="text-xs">Feedback: {turn.evaluation.feedback}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {error && (
        <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
          <p><span className="font-semibold">Chat Error:</span> {error}</p>
          {chatHistory.length === 0 && (
            <p className="mt-1">Could not initialize chat. Please try ending the chat and starting again, or refresh the page.</p>
          )}
        </div>
      )}
      
      <div className="flex items-center space-x-2">
        <Textarea
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          placeholder="Type your message..."
          className="flex-grow"
          rows={2}
          enableStt={true}
          sttLang="zh-CN"
          onKeyPress={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
          disabled={isLoading}
        />
        <Button onClick={handleSendMessage} disabled={isLoading || !userInput.trim()}>
          {isLoading ? 'Sending...' : 'Send'}
        </Button>
      </div>
      
      <Button onClick={onEndChat} variant="outline" className="mt-auto">
        End Chat
      </Button>
    </div>
  );
};

export default InteractiveVocabularyChat;
