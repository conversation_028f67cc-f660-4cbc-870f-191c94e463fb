import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import InteractiveChat from './InteractiveChat';
import { GrammarPoint } from '../../types/content';
import * as aiService from '../../lib/ai-service';
import TextWithTTS from '../ui/TextWithTTS';

// Mock the AI service
jest.mock('../../lib/ai-service');
const mockStartChatExercise = aiService.startChatExercise as jest.MockedFunction<typeof aiService.startChatExercise>;
const mockContinueChat = aiService.continueChat as jest.MockedFunction<typeof aiService.continueChat>;

// Mock TextWithTTS to check its props and rendering
jest.mock('../ui/TextWithTTS', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return jest.fn((props: any) => (
    <div data-testid="mock-text-with-tts">
      <span data-testid="mock-tts-text">{props.text}</span>
      {/* We can add more checks here if needed, e.g., for lang prop */}
    </div>
  ));
});
const MockedTextWithTTS = TextWithTTS as jest.MockedFunction<typeof TextWithTTS>;


const mockGrammarPoint: GrammarPoint = {
  id: 'test-gp',
  title: 'Test Grammar Point',
  explanation: 'This is a test.',
  examples: [{ chinese: '你好', pinyin: 'nǐ hǎo', english: 'Hello' }],
  // level: 1, // level is not a direct property of GrammarPoint, it's part of a broader system perhaps
  // topic: 'Greetings', // topic is not a direct property
  // related_structures: [], // related_structures is not a direct property, it's relatedPoints
  relatedPoints: [],
  // exercises: [], // exercises is not a direct property
  category: 'HSK1', // Assuming category is used for grouping
  // difficulty: 'Beginner', // difficulty is not a direct property
  tags: ['test'],
  // GrammarPoint also requires chapter and createdAt
  chapter: 'Chapter 1', // Added mock chapter
  createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any, // Added mock createdAt, cast to any to satisfy Timestamp
};

const mockOnEndChat = jest.fn();

describe('InteractiveChat TTS Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    MockedTextWithTTS.mockClear(); // Clear mock calls for TextWithTTS

    // Default mock implementation for startChatExercise
    mockStartChatExercise.mockResolvedValue({
      nextAIMessage: "你好，这是一个AI的开场白。", // AI message with Chinese
      // evaluation is not part of AIResponseSchema for startChatExercise
    });

    // Default mock implementation for continueChat
    mockContinueChat.mockResolvedValue({
      evaluation: {
        score: 8,
        feedback: "Good job!",
        // corrected_sentence: "你的句子很好。" // Removed as it's not in EvaluationSchema
      },
      nextAIMessage: "这是AI的另一个中文回复。", // AI message with Chinese
    });
  });

  it('should render TextWithTTS for AI messages containing Chinese text', async () => {
    render(<InteractiveChat grammarPoint={mockGrammarPoint} onEndChat={mockOnEndChat} />);

    // Wait for the initial AI message
    await waitFor(() => {
      expect(screen.getByText('你好，这是一个AI的开场白。')).toBeInTheDocument();
    });
    
    // Check if TextWithTTS was called for the AI message
    expect(MockedTextWithTTS).toHaveBeenCalledWith(
      expect.objectContaining({ text: "你好，这是一个AI的开场白。" }),
      {}
    );
  });

  it('should render TextWithTTS for user messages containing Chinese text', async () => {
    render(<InteractiveChat grammarPoint={mockGrammarPoint} onEndChat={mockOnEndChat} />);

    // Wait for initial AI message to load
    await waitFor(() => expect(mockStartChatExercise).toHaveBeenCalled());

    const userInput = "我的中文消息";
    fireEvent.change(screen.getByPlaceholderText('Type your message...'), { target: { value: userInput } });
    fireEvent.click(screen.getByRole('button', { name: 'Send' }));

    // Wait for the user message to appear and AI to respond
    await waitFor(() => {
      expect(screen.getByText(userInput)).toBeInTheDocument();
      expect(mockContinueChat).toHaveBeenCalled();
    });

    // Check if TextWithTTS was called for the user message
    // The user message "我的中文消息" should be the second call to TextWithTTS (first is initial AI message)
    // The AI response "这是AI的另一个中文回复。" will be the third.
    expect(MockedTextWithTTS).toHaveBeenCalledWith(
      expect.objectContaining({ text: userInput }),
      {}
    );
  });

  it('should render TextWithTTS for AI messages without Chinese text (if TextWithTTS handles non-Chinese gracefully)', async () => {
    mockStartChatExercise.mockResolvedValueOnce({ nextAIMessage: "Hello, this is an AI greeting." });
    render(<InteractiveChat grammarPoint={mockGrammarPoint} onEndChat={mockOnEndChat} />);

    await waitFor(() => {
      expect(screen.getByText("Hello, this is an AI greeting.")).toBeInTheDocument();
    });
    
    expect(MockedTextWithTTS).toHaveBeenCalledWith(
      expect.objectContaining({ text: "Hello, this is an AI greeting." }),
      {}
    );
  });

  it('should render TextWithTTS for user messages without Chinese text (if TextWithTTS handles non-Chinese gracefully)', async () => {
    render(<InteractiveChat grammarPoint={mockGrammarPoint} onEndChat={mockOnEndChat} />);
    await waitFor(() => expect(mockStartChatExercise).toHaveBeenCalled());

    const userInput = "My English message";
    fireEvent.change(screen.getByPlaceholderText('Type your message...'), { target: { value: userInput } });
    fireEvent.click(screen.getByRole('button', { name: 'Send' }));

    await waitFor(() => {
      expect(screen.getByText(userInput)).toBeInTheDocument();
      expect(mockContinueChat).toHaveBeenCalled();
    });
    
    expect(MockedTextWithTTS).toHaveBeenCalledWith(
      expect.objectContaining({ text: userInput }),
      {}
    );
  });


  it('should pass the correct text content to TextWithTTS for multiple turns', async () => {
    render(<InteractiveChat grammarPoint={mockGrammarPoint} onEndChat={mockOnEndChat} />);

    // Initial AI message
    const initialAIMessage = "你好，这是一个AI的开场白。";
    await waitFor(() => expect(screen.getByText(initialAIMessage)).toBeInTheDocument());
    expect(MockedTextWithTTS).toHaveBeenCalledWith(expect.objectContaining({ text: initialAIMessage }), {});

    // User sends a message
    const userMessage1 = "这是我的第一个中文消息。";
    fireEvent.change(screen.getByPlaceholderText('Type your message...'), { target: { value: userMessage1 } });
    fireEvent.click(screen.getByRole('button', { name: 'Send' }));
    
    const aiResponse1 = "很好，这是AI的第一个回复。";
    mockContinueChat.mockResolvedValueOnce({
        evaluation: { score: 9, feedback: "Great!" /* corrected_sentence: userMessage1 */ }, // Removed corrected_sentence
        nextAIMessage: aiResponse1,
    });

    await waitFor(() => expect(screen.getByText(userMessage1)).toBeInTheDocument());
    expect(MockedTextWithTTS).toHaveBeenCalledWith(expect.objectContaining({ text: userMessage1 }), {});
    
    await waitFor(() => expect(screen.getByText(aiResponse1)).toBeInTheDocument());
    expect(MockedTextWithTTS).toHaveBeenCalledWith(expect.objectContaining({ text: aiResponse1 }), {});

    // User sends another message
    const userMessage2 = "My second message, in English this time.";
     fireEvent.change(screen.getByPlaceholderText('Type your message...'), { target: { value: userMessage2 } });
    fireEvent.click(screen.getByRole('button', { name: 'Send' }));

    const aiResponse2 = "Okay, AI responds in English too.";
    mockContinueChat.mockResolvedValueOnce({
        evaluation: { score: 7, feedback: "Okay." /* corrected_sentence: userMessage2 */ }, // Removed corrected_sentence
        nextAIMessage: aiResponse2,
    });

    await waitFor(() => expect(screen.getByText(userMessage2)).toBeInTheDocument());
    expect(MockedTextWithTTS).toHaveBeenCalledWith(expect.objectContaining({ text: userMessage2 }), {});

    await waitFor(() => expect(screen.getByText(aiResponse2)).toBeInTheDocument());
    expect(MockedTextWithTTS).toHaveBeenCalledWith(expect.objectContaining({ text: aiResponse2 }), {});

    // Verify total calls to TextWithTTS
    // Initial AI + User1 + AI1 + User2 + AI2 = 5 calls
    expect(MockedTextWithTTS).toHaveBeenCalledTimes(5);
  });
});