import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SelfAssessmentInputProps {
  currentScore: number | null;
  onUpdate: (score: number) => void;
  min: number;
  max: number;
  step?: number;
}

const SelfAssessmentInput: React.FC<SelfAssessmentInputProps> = ({
  currentScore,
  onUpdate,
  min,
  max,
  step = 1,
}) => {
  const [score, setScore] = useState<number | string>(currentScore ?? '');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      setScore('');
    } else {
      const numValue = Number(value);
      if (!isNaN(numValue) && numValue >= min && numValue <= max) {
        setScore(numValue);
      }
    }
  };

  const handleSubmit = () => {
    if (typeof score === 'number') {
      onUpdate(score);
    }
  };

  return (
    <Card className="w-full max-w-sm">
      <CardHeader>
        <CardTitle>Self-Assess Mastery</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div className="flex flex-col space-y-2">
            <Label htmlFor="self-assessment-score">Your Score ({min}-{max})</Label>
            <Input
              id="self-assessment-score"
              type="number"
              value={score}
              onChange={handleChange}
              min={min}
              max={max}
              step={step}
              placeholder={currentScore != null ? String(currentScore) : "Enter score"}
            />
          </div>
          <Button onClick={handleSubmit} disabled={typeof score !== 'number'}>
            Update Score
          </Button>
          {currentScore != null && (
            <p className="text-sm text-muted-foreground">Current: {currentScore}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SelfAssessmentInput;