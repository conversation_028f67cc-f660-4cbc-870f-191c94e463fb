import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { GrammarProgress } from '@/types/progress';

interface GrammarExercisePerformanceDisplayProps {
  performance: GrammarProgress['exercisePerformance'];
}

const GrammarExercisePerformanceDisplay: React.FC<GrammarExercisePerformanceDisplayProps> = ({ performance }) => {
  if (!performance || Object.keys(performance).length === 0) {
    return <p className="text-sm text-muted-foreground">No exercise performance data available.</p>;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Exercise Performance Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Exercise Type</TableHead>
              <TableHead>Correct</TableHead>
              <TableHead>Incorrect</TableHead>
              <TableHead>Accuracy</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(performance).map(([type, stats]) => {
              const totalAttempts = stats.correct + stats.incorrect;
              const accuracy = totalAttempts > 0 ? (stats.correct / totalAttempts) * 100 : 0;
              return (
                <TableRow key={type}>
                  <TableCell className="font-medium">{type}</TableCell>
                  <TableCell>{stats.correct}</TableCell>
                  <TableCell>{stats.incorrect}</TableCell>
                  <TableCell>{accuracy.toFixed(1)}%</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default GrammarExercisePerformanceDisplay;