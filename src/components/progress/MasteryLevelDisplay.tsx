import React from 'react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface MasteryLevelDisplayProps {
  level: string; // e.g., "<PERSON><PERSON>", "Be<PERSON>ner", "Intermediate", "Advanced", "Mastered"
  progress: number; // 0-100
  className?: string;
}

const MasteryLevelDisplay: React.FC<MasteryLevelDisplayProps> = ({ level, progress, className }) => {
  let progressColorClass = 'bg-gray-400'; // Default

  if (progress < 20) {
    progressColorClass = 'bg-red-500';
  } else if (progress < 40) {
    progressColorClass = 'bg-orange-500';
  } else if (progress < 60) {
    progressColorClass = 'bg-yellow-500';
  } else if (progress < 80) {
    progressColorClass = 'bg-blue-500';
  } else {
    progressColorClass = 'bg-green-500';
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <span className="text-sm font-medium min-w-[80px]">{level}</span>
      <Progress value={progress} className="w-[100px]" indicatorClassName={progressColorClass} />
      <span className="text-sm text-muted-foreground">{progress}%</span>
    </div>
  );
};

export default MasteryLevelDisplay;