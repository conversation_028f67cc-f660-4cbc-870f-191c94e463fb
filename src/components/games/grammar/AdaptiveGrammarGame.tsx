'use client';

import { useEffect, useState } from 'react';
import { GrammarPoint } from '@/types/content';
import { GrammarProgress, SuperMemoGrade } from '@/types/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface AdaptiveGrammarGameProps {
  grammarPoints: GrammarPoint[];
  grammarProgressMap: Record<string, GrammarProgress>;
  updateGrammarExercisePerformance: (
    grammarItemId: string,
    exerciseType: string,
    isCorrect: boolean,
    grade: SuperMemoGrade
  ) => Promise<void>;
  getPrioritizedGrammarRules: () => string[];
}

export function AdaptiveGrammarGame({
  grammarPoints,
  grammarProgressMap,
  updateGrammarExercisePerformance,
  getPrioritizedGrammarRules,
}: AdaptiveGrammarGameProps) {
  const [currentGrammarPoint, setCurrentGrammarPoint] = useState<GrammarPoint | null>(null);
  const [questionIndex, setQuestionIndex] = useState(0);
  const [feedback, setFeedback] = useState<string | null>(null);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [prioritizedRuleIds, setPrioritizedRuleIds] = useState<string[]>([]);

  useEffect(() => {
    // Fetch prioritized rules when the component mounts or grammarProgressMap changes
    const rules = getPrioritizedGrammarRules();
    setPrioritizedRuleIds(rules);
    if (rules.length > 0) {
      const firstRule = grammarPoints.find(gp => gp.id === rules[0]);
      setCurrentGrammarPoint(firstRule || null);
    }
  }, [grammarProgressMap, grammarPoints, getPrioritizedGrammarRules]);

  const handleAnswer = async (selectedAnswer: string, correctAnswer: string) => {
    const correct = selectedAnswer === correctAnswer;
    setIsCorrect(correct);
    setFeedback(correct ? 'Correct!' : `Incorrect. The answer was: ${correctAnswer}`);

    if (currentGrammarPoint) {
      // This is a placeholder for actual exercise type and grade.
      // In a real game, you'd determine these based on the specific exercise.
      const exerciseType = 'general_practice';
      const grade: SuperMemoGrade = correct ? 5 : 0;
      await updateGrammarExercisePerformance(currentGrammarPoint.id, exerciseType, correct, grade);
    }
  };

  const handleNextQuestion = () => {
    setFeedback(null);
    setIsCorrect(null);
    const nextIndex = questionIndex + 1;
    if (nextIndex < prioritizedRuleIds.length) {
      setQuestionIndex(nextIndex);
      const nextRule = grammarPoints.find(gp => gp.id === prioritizedRuleIds[nextIndex]);
      setCurrentGrammarPoint(nextRule || null);
    } else {
      // Game complete logic
      setCurrentGrammarPoint(null);
      setFeedback("Game session complete!");
    }
  };

  if (!currentGrammarPoint) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Grammar Practice</CardTitle>
        </CardHeader>
        <CardContent>
          {prioritizedRuleIds.length === 0 ? (
            <p>Loading grammar points or no prioritized rules available.</p>
          ) : (
            <p>{feedback || "Game session complete! No more prioritized grammar rules for now."}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  // Placeholder for a simple question display
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Grammar Practice: {currentGrammarPoint.title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-4">{currentGrammarPoint.explanation}</p>
        {currentGrammarPoint.examples && currentGrammarPoint.examples.length > 0 && (
          <div className="mb-4">
            <strong>Example:</strong>
            <p>{currentGrammarPoint.examples[0].chinese} - {currentGrammarPoint.examples[0].english}</p>
          </div>
        )}

        {/* Simple answer selection - replace with actual game logic */}
        <div className="flex flex-col space-y-2">
          <Button onClick={() => handleAnswer('correct', 'correct')} disabled={feedback !== null}>
            Simulate Correct Answer
          </Button>
          <Button onClick={() => handleAnswer('incorrect', 'correct')} disabled={feedback !== null}>
            Simulate Incorrect Answer
          </Button>
        </div>

        {feedback && (
          <div className={`mt-4 p-2 rounded ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {feedback}
            <Button onClick={handleNextQuestion} className="ml-4">Next</Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}