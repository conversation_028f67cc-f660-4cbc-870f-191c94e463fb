'use client';

import { useEffect } from 'react';
import { useMachine } from '@xstate/react';

import {
  adaptiveVocabularyGameMachine,
  SuperMemoGrade,
  AdaptiveVocabularyGameContext,
  VocabularyItem,
  GameType // Keep GameType for QuestionDisplay prop
} from '@/features/adaptiveVocabularyGame';
import { VocabularyProgress } from '@/types/progress';
import { IGameLogic } from '@/features/adaptiveVocabularyGame/gameLogic.interface';

// Import new presentational components
import { LoadingDisplay } from './ui/LoadingDisplay';
import { MessageDisplay } from './ui/MessageDisplay';
import { QuestionDisplay } from './ui/QuestionDisplay';
import { FeedbackDisplay } from './ui/FeedbackDisplay';
import { GameSummaryDisplay } from './ui/GameSummaryDisplay';
// Keep Card imports for the final fallback case
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AdaptiveVocabularyGameProps {
  vocabulary: VocabularyItem[];
  gameLogic: IGameLogic;
  vocabularyProgressMap: Record<string, VocabularyProgress>;
  updateVocabularyItemProgress: (itemId: string, grade: SuperMemoGrade) => Promise<void>;
  getPrioritizedVocabulary: () => VocabularyItem[];
}

export function AdaptiveVocabularyGame({
  vocabulary,
  gameLogic,
  vocabularyProgressMap,
  updateVocabularyItemProgress,
  getPrioritizedVocabulary,
}: AdaptiveVocabularyGameProps) {
  const [current, send] = useMachine(adaptiveVocabularyGameMachine, {
    input: {
      vocabularyProgressMap,
      updateVocabularyItemProgress,
      getPrioritizedVocabulary,
    },
  });

  useEffect(() => {
    // console.log('[AdaptiveVocabularyGame] XState machine state changed:', current.value);
    // console.log('[AdaptiveVocabularyGame] XState machine context:', current.context);
  }, [current]);

  useEffect(() => {
    if (current.matches('initializing') && vocabulary && gameLogic) {
      send({ type: 'LOAD_VOCABULARY', vocabulary, gameLogic });
    }
  }, [current, vocabulary, gameLogic, send]);

  const {
    currentItem,
    options,
    score,
    totalQuestions,
    feedbackMessage,
    isCorrect,
    selectedAnswer,
    correctAnswerText,
    currentQuestionIndex,
    finalScore,
    totalTimeTaken,
    errorMessage,
    lastReviewResult,
  } = current.context as AdaptiveVocabularyGameContext;

  if (current.matches('initializing')) {
    return (
      <LoadingDisplay
        title="Initializing Game..."
        message="Getting things ready..."
        progressValue={10}
      />
    );
  }

  if (current.matches('loadingVocabulary')) {
    return (
      <LoadingDisplay
        title="Loading Vocabulary..."
        message="Preparing your questions..."
        progressValue={50}
      />
    );
  }

  if (current.matches('gameEmpty')) {
    return (
      <MessageDisplay
        title="No Vocabulary Available"
        message="There are no vocabulary items to practice for the selected game type. Please add some vocabulary or try a different set."
      />
    );
  }

  if (current.matches('error')) {
    return (
      <MessageDisplay
        title="Error Loading Game"
        message={errorMessage || 'An unexpected error occurred.'}
        onAction={() => send({ type: 'RETRY_LOADING' })}
        actionLabel="Try Again"
        isError={true}
      />
    );
  }

  if (current.matches('gameComplete')) {
    return (
      <GameSummaryDisplay
        score={score}
        finalScore={finalScore}
        totalQuestions={totalQuestions}
        totalTimeTaken={totalTimeTaken}
        onPlayAgain={() => send({ type: 'PLAY_AGAIN' })}
      />
    );
  }

  if (current.matches('presentingQuestion') || current.matches('questionDisplayed')) {
    if (!currentItem || !options || options.length === 0 || !current.context.gameLogic) {
      return <LoadingDisplay title="Preparing Question..." message="Please wait." />;
    }
    return (
      <QuestionDisplay
        currentItem={currentItem}
        options={options}
        gameType={current.context.gameLogic.gameType as GameType} // Cast to GameType
        onAnswerSelected={(selectedOption) => send({ type: 'ANSWER_SELECTED', selectedOption })}
        score={score}
        totalQuestions={totalQuestions}
        currentQuestionIndex={currentQuestionIndex}
        questionPrompt={current.context.gameLogic.getQuestionPrompt(currentItem, current.context)}
      />
    );
  }

  if (current.matches('showingFeedback')) {
    if (!currentItem || !selectedAnswer || typeof correctAnswerText === 'undefined' || !options || !current.context.gameLogic) {
       // Fallback if essential data for feedback is missing
      return <LoadingDisplay title="Loading Feedback..." message="Please wait." />;
    }
    return (
      <FeedbackDisplay
        currentItem={currentItem}
        options={options}
        selectedAnswer={selectedAnswer}
        isCorrect={!!isCorrect}
        correctAnswerText={correctAnswerText}
        feedbackMessage={feedbackMessage || (isCorrect ? "Correct!" : "Incorrect.")}
        onContinue={() => send({ type: 'CONTINUE_AFTER_FEEDBACK' })}
        score={score}
        totalQuestions={totalQuestions}
        currentQuestionIndex={currentQuestionIndex}
        questionPrompt={current.context.gameLogic.getQuestionPrompt(currentItem, current.context)}
        lastReviewResult={lastReviewResult}
        vocabularyProgressMap={vocabularyProgressMap}
      />
    );
  }

  // Fallback for any unhandled state
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Game Status Unknown</CardTitle>
      </CardHeader>
      <CardContent>
        <p>The game is in an unrecognized state: {String(current.value)}</p>
      </CardContent>
    </Card>
  );
}