import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AdaptiveVocabularyGame } from './AdaptiveVocabularyGame';
import { VocabularyItem, GameType, AdaptiveVocabularyGameContext } from '@/features/adaptiveVocabularyGame';
import { Timestamp } from 'firebase/firestore';
// SnapshotFrom is not strictly needed if we define a simpler mock type
// import { SnapshotFrom } from 'xstate';

// Mock the useMachine hook
const mockSend = jest.fn();

// Define a simpler type for the mock machine state
interface MockMachineState {
  value: string;
  context: AdaptiveVocabularyGameContext;
  matches: (stateValue: string | Record<string, string>) => boolean;
  // Add other properties if the component directly uses them from the snapshot, e.g., status
  status?: 'active' | 'done' | 'error' | 'stopped';
}
let mockMachineState: MockMachineState;


jest.mock('@xstate/react', () => ({
  ...jest.requireActual('@xstate/react'),
  useMachine: () => [mockMachineState, mockSend],
}));

// Mock presentational components to verify they are rendered with correct props
const mockLoadingDisplay = jest.fn(({ title, message }) => <div>Loading: {title} - {message}</div>);
const mockMessageDisplay = jest.fn(({ title, message, actionLabel }) => (
  <div>
    Message: {title} - {message}
    {actionLabel && <button>{actionLabel}</button>}
  </div>
));
const mockQuestionDisplay = jest.fn(({ questionPrompt }) => <div>Question: {questionPrompt}</div>);
const mockFeedbackDisplay = jest.fn(({ feedbackMessage }) => <div>Feedback: {feedbackMessage}</div>);
const mockGameSummaryDisplay = jest.fn(({ finalScore }) => <div>Summary: Score {finalScore}</div>);

jest.mock('./ui/LoadingDisplay', () => ({ LoadingDisplay: mockLoadingDisplay }));
jest.mock('./ui/MessageDisplay', () => ({ MessageDisplay: mockMessageDisplay }));
jest.mock('./ui/QuestionDisplay', () => ({ QuestionDisplay: mockQuestionDisplay }));
jest.mock('./ui/FeedbackDisplay', () => ({ FeedbackDisplay: mockFeedbackDisplay }));
jest.mock('./ui/GameSummaryDisplay', () => ({ GameSummaryDisplay: mockGameSummaryDisplay }));


const mockTimestamp = {
  now: () => ({ seconds: Date.now() / 1000, nanoseconds: 0 }),
  fromDate: (date: Date) => ({ seconds: date.getTime() / 1000, nanoseconds: 0 }),
} as unknown as Timestamp;

const mockVocabulary: VocabularyItem[] = [
  { id: '1', word: '你好', pinyin: 'nǐ hǎo', character: '你好', meaning: 'hello', exampleSentences: [], partOfSpeech: 'greeting', definitions: ['hello'], examples: [], category: 'Common', chapter: '1', createdAt: mockTimestamp, tags: ['greeting'] },
  { id: '2', word: '谢谢', pinyin: 'xièxie', character: '谢谢', meaning: 'thank you', exampleSentences: [], partOfSpeech: 'verb', definitions: ['thank you'], examples: [], category: 'Common', chapter: '1', createdAt: mockTimestamp, tags: ['politeness'] },
];
const mockGameType: GameType = 'hanzi';

// Helper to create a base context for mocks
const createMockContext = (overrides: Partial<AdaptiveVocabularyGameContext> = {}): AdaptiveVocabularyGameContext => ({
  prioritizedVocabulary: [],
  currentQuestionIndex: 0,
  options: [],
  score: 0,
  totalQuestions: 0,
  gameType: mockGameType,
  initialVocabulary: [],
  vocabularyProgressMap: {}, // Initialize vocabularyProgressMap
  updateVocabularyItemProgress: jest.fn(), // Mock the function
  getPrioritizedVocabulary: jest.fn(() => []), // Mock the function
  currentItem: undefined,
  correctAnswerText: undefined,
  selectedAnswer: undefined,
  isCorrect: undefined,
  feedbackMessage: undefined,
  finalScore: undefined,
  gameStartTime: undefined,
  questionStartTime: undefined,
  totalTimeTaken: undefined,
  errorMessage: undefined,
  lastReviewResult: undefined, // Add this as it's part of the context
  ...overrides,
});


describe('AdaptiveVocabularyGame Component', () => {
  beforeEach(() => {
    mockSend.mockClear();
    // Clear mocks of presentational components
    mockLoadingDisplay.mockClear();
    mockMessageDisplay.mockClear();
    mockQuestionDisplay.mockClear();
    mockFeedbackDisplay.mockClear();
    mockGameSummaryDisplay.mockClear();
  });

  it('sends LOAD_VOCABULARY event on mount with props', () => {
    mockMachineState = {
      value: 'initializing',
      context: createMockContext(),
      matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'initializing',
    };

    render(<AdaptiveVocabularyGame
      vocabulary={mockVocabulary}
      gameType={mockGameType}
      vocabularyProgressMap={{}} // Provide a mock empty map
      updateVocabularyItemProgress={jest.fn()} // Provide a mock function
      getPrioritizedVocabulary={jest.fn(() => mockVocabulary)} // Provide a mock function
    />);
    
    expect(mockSend).toHaveBeenCalledWith({
      type: 'LOAD_VOCABULARY',
      vocabulary: mockVocabulary,
      gameType: mockGameType,
    });
  });

  it('renders LoadingDisplay when machine is in "initializing" state', () => {
    mockMachineState = {
      value: 'initializing',
      context: createMockContext(),
      matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'initializing',
    };
    render(<AdaptiveVocabularyGame
      vocabulary={mockVocabulary}
      gameType={mockGameType}
      vocabularyProgressMap={{}}
      updateVocabularyItemProgress={jest.fn()}
      getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
    />);
    expect(screen.getByText('Loading: Initializing Game... - Getting things ready...')).toBeInTheDocument();
    expect(mockLoadingDisplay).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Initializing Game..." }),
      {}
    );
  });

  it('renders LoadingDisplay when machine is in "loadingVocabulary" state', () => {
    mockMachineState = {
      value: 'loadingVocabulary',
      context: createMockContext({ initialVocabulary: mockVocabulary, gameType: mockGameType }),
      matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'loadingVocabulary',
    };
    render(<AdaptiveVocabularyGame
      vocabulary={mockVocabulary}
      gameType={mockGameType}
      vocabularyProgressMap={{}}
      updateVocabularyItemProgress={jest.fn()}
      getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
    />);
    expect(screen.getByText('Loading: Loading Vocabulary... - Preparing your questions...')).toBeInTheDocument();
    expect(mockLoadingDisplay).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Loading Vocabulary..." }),
      {}
    );
  });

  it('renders MessageDisplay when machine is in "gameEmpty" state', () => {
    mockMachineState = {
      value: 'gameEmpty',
      context: createMockContext({ errorMessage: undefined }),
      matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'gameEmpty',
      status: 'done', // gameEmpty is a final state
      // other snapshot properties if needed by the component, but typically not for simple mocks
    };
    render(<AdaptiveVocabularyGame
      vocabulary={mockVocabulary}
      gameType={mockGameType}
      vocabularyProgressMap={{}}
      updateVocabularyItemProgress={jest.fn()}
      getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
    />);
    expect(screen.getByText('Message: No Vocabulary Available - There are no vocabulary items to practice for the selected game type. Please add some vocabulary or try a different set.')).toBeInTheDocument();
    expect(mockMessageDisplay).toHaveBeenCalledWith(
      expect.objectContaining({ title: "No Vocabulary Available" }),
      {} // This closes the toHaveBeenCalledWith
    ); // This closes the expect
  }); // This closes the "it" block for gameEmpty

  describe('State: presentingQuestion', () => {
    const currentItemMock: VocabularyItem = {
      id: '1', word: '测试', pinyin: 'cèshì', character: '测试', meaning: 'test', exampleSentences: [], definitions: ['test'],
      partOfSpeech: 'verb', examples: [], category: 'Tech', chapter: '1', createdAt: mockTimestamp
    };
    const optionsMock = ['test', 'trial', 'experiment', 'quiz'];

    beforeEach(() => {
      mockMachineState = {
        value: 'presentingQuestion',
        context: createMockContext({
          currentItem: currentItemMock,
          options: optionsMock,
          correctAnswerText: 'test', // Machine's prepareQuestion would set this
          currentQuestionIndex: 0,
          totalQuestions: 5,
          score: 10,
          gameType: 'meaning', // Example gameType
        }),
        matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'presentingQuestion',
        status: 'active',
      };
    });

    it('renders QuestionDisplay with correct props', () => {
      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      // The getQuestionPrompt in the component will determine the exact prompt.
      // For 'meaning' gameType and currentItem.word '测试', prompt should be '测试'.
      expect(screen.getByText('Question: 测试')).toBeInTheDocument();
      expect(mockQuestionDisplay).toHaveBeenCalledWith(
        expect.objectContaining({
          currentItem: currentItemMock,
          options: optionsMock,
          score: 10,
          totalQuestions: 5,
          currentQuestionIndex: 0,
          gameType: 'meaning',
          questionPrompt: '测试', // Based on getQuestionPrompt logic for 'meaning'
        }),
        {}
      );
    });

    it('sends ANSWER_SELECTED event when an option is chosen in QuestionDisplay', () => {
      // To test the callback, we capture it from the mock's props.
      let capturedOnAnswerSelected: (option: string) => void = () => {};
      mockQuestionDisplay.mockImplementationOnce(({ onAnswerSelected, questionPrompt }) => {
        capturedOnAnswerSelected = onAnswerSelected; // Capture the function
        return <div>Question: {questionPrompt}</div>;
      });
      
      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      expect(capturedOnAnswerSelected).toBeInstanceOf(Function);
      act(() => {
        capturedOnAnswerSelected('test');
      });

      expect(mockSend).toHaveBeenCalledWith({ type: 'ANSWER_SELECTED', selectedOption: 'test' });
describe('State: showingFeedback', () => {
    const currentItemMock: VocabularyItem = {
      id: '1', word: '反馈', pinyin: 'fǎnkuì', character: '反馈', meaning: 'feedback', exampleSentences: [], definitions: ['feedback'],
      partOfSpeech: 'noun', examples: [], category: 'General', chapter: '2', createdAt: mockTimestamp
    };
    const optionsMock = ['feedback', 'comment', 'reply', 'note'];

    beforeEach(() => {
      mockMachineState = {
        value: 'showingFeedback',
        context: createMockContext({
          currentItem: currentItemMock,
          options: optionsMock,
          selectedAnswer: 'feedback',
          isCorrect: true,
          correctAnswerText: 'feedback',
          feedbackMessage: 'Correct!',
          score: 20,
          currentQuestionIndex: 1,
          totalQuestions: 5,
          gameType: 'meaning',
        }),
        matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'showingFeedback',
        status: 'active',
      };
    });

    it('renders FeedbackDisplay with correct props', () => {
      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      expect(screen.getByText('Feedback: Correct!')).toBeInTheDocument();
      expect(mockFeedbackDisplay).toHaveBeenCalledWith(
        expect.objectContaining({
          currentItem: currentItemMock,
          options: optionsMock,
          selectedAnswer: 'feedback',
          isCorrect: true,
          correctAnswerText: 'feedback',
          feedbackMessage: 'Correct!',
          score: 20,
          questionPrompt: '反馈', // Based on getQuestionPrompt for 'meaning' and currentItem.word
        }),
        {}
      );
    });

    it('sends CONTINUE_AFTER_FEEDBACK event when onContinue is called from FeedbackDisplay', () => {
      let capturedOnContinue: () => void = () => {};
      mockFeedbackDisplay.mockImplementationOnce(({ onContinue, feedbackMessage }) => {
        capturedOnContinue = onContinue;
        return <div>Feedback: {feedbackMessage}</div>;
      });
describe('State: gameComplete', () => {
    beforeEach(() => {
      mockMachineState = {
        value: 'gameComplete',
        context: createMockContext({
          score: 80,
          finalScore: 80,
          totalQuestions: 10,
          totalTimeTaken: 120000, // 2 minutes in ms
        }),
        matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'gameComplete',
        status: 'active', // Or 'done' if gameComplete is a final state in some configurations, but typically it allows PLAY_AGAIN
      };
    });

    it('renders GameSummaryDisplay with correct props', () => {
      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      expect(screen.getByText('Summary: Score 80')).toBeInTheDocument();
      expect(mockGameSummaryDisplay).toHaveBeenCalledWith(
        expect.objectContaining({
          score: 80,
          finalScore: 80,
          totalQuestions: 10,
          totalTimeTaken: 120000,
        }),
        {}
      );
    });

    it('sends PLAY_AGAIN event when onPlayAgain is called from GameSummaryDisplay', () => {
      let capturedOnPlayAgain: () => void = () => {};
      mockGameSummaryDisplay.mockImplementationOnce(({ onPlayAgain, finalScore }) => {
        capturedOnPlayAgain = onPlayAgain;
        return <div>Summary: Score {finalScore}</div>;
      });

      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      expect(capturedOnPlayAgain).toBeInstanceOf(Function);
      act(() => {
        capturedOnPlayAgain();
      });

      expect(mockSend).toHaveBeenCalledWith({ type: 'PLAY_AGAIN' });
    });
  });

      render(<AdaptiveVocabularyGame
        vocabulary={mockVocabulary}
        gameType={mockGameType}
        vocabularyProgressMap={{}}
        updateVocabularyItemProgress={jest.fn()}
        getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
      />);
      
      expect(capturedOnContinue).toBeInstanceOf(Function);
      act(() => {
        capturedOnContinue();
      });

      expect(mockSend).toHaveBeenCalledWith({ type: 'CONTINUE_AFTER_FEEDBACK' });
    });
  });
    });
  }); // This closes the describe block for presentingQuestion

  it('renders MessageDisplay with error and retry button when machine is in "error" state', () => {
    const testErrorMessage = "Network failed";
    mockMachineState = {
      value: 'error',
      context: createMockContext({ errorMessage: testErrorMessage }),
      matches: (stateValue: string | Record<string, string>) => typeof stateValue === 'string' && stateValue === 'error',
      // other snapshot properties
    };
    render(<AdaptiveVocabularyGame
      vocabulary={mockVocabulary}
      gameType={mockGameType}
      vocabularyProgressMap={{}}
      updateVocabularyItemProgress={jest.fn()}
      getPrioritizedVocabulary={jest.fn(() => mockVocabulary)}
    />);
    expect(screen.getByText(`Message: Error Loading Game - ${testErrorMessage}`)).toBeInTheDocument();
    expect(mockMessageDisplay).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error Loading Game", message: testErrorMessage, actionLabel: "Try Again" }),
      {}
    );
    const retryButton = screen.getByRole('button', { name: 'Try Again' });
    expect(retryButton).toBeInTheDocument();
    fireEvent.click(retryButton);
    expect(mockSend).toHaveBeenCalledWith({ type: 'RETRY_LOADING' });
  });

  // More tests will follow for other states and interactions
});
