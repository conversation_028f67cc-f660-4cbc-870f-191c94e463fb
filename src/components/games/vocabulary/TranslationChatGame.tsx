'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { VocabularyItem, GrammarPoint } from '@/types/content';
import { courseData } from '@/lib/course-data';
import { z } from 'zod'; // Import z from zod
import {
  startTranslationChatGame,
  continueTranslationChatGame,
  endTranslationChatGame,
  TranslationChatAIResponseSchema,
  TranslationChatGameSummarySchema,
} from '@/lib/ai-service';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'; // Import CardDescription
import { ScrollArea } from '@/components/ui/scroll-area';
import { TranslationChatTurn, TranslationChatGameContext } from '@/features/adaptiveVocabularyGame/gameTypes/translationChatGameLogic';
import { Loader2 } from 'lucide-react'; // For loading spinner

const TranslationChatGame: React.FC = () => {
  const searchParams = useSearchParams();
  const category = searchParams.get('category') || 'default'; // Fallback to 'default'
  const tag = searchParams.get('tag'); // Optional tag

  const [chatHistory, setChatHistory] = useState<TranslationChatTurn[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isLoadingAIResponse, setIsLoadingAIResponse] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [allVocabularyItems, setAllVocabularyItems] = useState<VocabularyItem[]>([]);
  const [allGrammarPoints, setAllGrammarPoints] = useState<GrammarPoint[]>([]);
  const [currentChineseSentence, setCurrentChineseSentence] = useState<string | undefined>(undefined);
  const [gameSummary, setGameSummary] = useState<z.infer<typeof TranslationChatGameSummarySchema> | undefined>(undefined);
  const [isGameEnded, setIsGameEnded] = useState(false);

  const loadCourseData = useCallback(async () => {
    try {
      const vocab = await courseData.getVocabularyItemsByCategoryAndTag(category, tag);
      const grammar = await courseData.getGrammarPointsByCategory(category);
      setAllVocabularyItems(vocab);
      setAllGrammarPoints(grammar);
      return { vocab, grammar };
    } catch (e) {
      console.error("Failed to load course data:", e);
      setAiError(e instanceof Error ? e.message : 'Failed to load course data.');
      return { vocab: [], grammar: [] };
    }
  }, [category, tag]);

  const initializeChatGame = useCallback(async (vocab: VocabularyItem[], grammar: GrammarPoint[]) => {
    setIsLoadingAIResponse(true);
    setAiError(null);
    try {
      const initialResponse = await startTranslationChatGame(category, vocab, grammar, { responseSchema: TranslationChatAIResponseSchema });
      if (initialResponse.question) {
        setCurrentChineseSentence(initialResponse.question.chineseSentence);
        setChatHistory([{ role: 'assistant', content: initialResponse.nextAIMessage || 'Translate the following sentence.', question: initialResponse.question }]);
      } else {
        throw new Error("AI did not provide an initial question.");
      }
    } catch (e) {
      console.error("Failed to start translation chat game:", e);
      setAiError(e instanceof Error ? e.message : 'Failed to start chat. Please try again.');
    } finally {
      setIsLoadingAIResponse(false);
    }
  }, [category]);

  useEffect(() => {
    const init = async () => {
      const { vocab, grammar } = await loadCourseData();
      if (vocab.length > 0 || grammar.length > 0) {
        initializeChatGame(vocab, grammar);
      } else {
        setAiError("No vocabulary or grammar found for this category/tag. Please select another.");
      }
    };
    init();
  }, [loadCourseData, initializeChatGame]);

  const handleSendMessage = async () => {
    if (!userInput.trim() || !currentChineseSentence || isGameEnded) return;

    const newUserTurn: TranslationChatTurn = { role: 'user', content: userInput };
    const currentChatHistory = [...chatHistory, newUserTurn];
    setChatHistory(currentChatHistory);
    setUserInput('');
    setIsLoadingAIResponse(true);
    setAiError(null);

    try {
      const aiResponse = await continueTranslationChatGame(
        category,
        allVocabularyItems,
        allGrammarPoints,
        currentChatHistory, // Pass current history for context
        userInput,
        currentChineseSentence, // Pass the sentence that was just translated
        { responseSchema: TranslationChatAIResponseSchema }
      );

      // Update the last user turn with the evaluation
      const updatedHistoryWithEvaluation = currentChatHistory.map((turn, index) => {
        if (index === currentChatHistory.length - 1 && turn.role === 'user') {
          return { ...turn, evaluation: aiResponse.evaluation };
        }
        return turn;
      });

      setChatHistory([
        ...updatedHistoryWithEvaluation,
      ]);

      if (aiResponse.question) {
        setCurrentChineseSentence(aiResponse.question.chineseSentence);
        setChatHistory(prev => [...prev, { role: 'assistant', content: aiResponse.nextAIMessage || 'Next sentence:', question: aiResponse.question }]);
      } else {
        // If no new question, it might be the end of a segment or an error
        setCurrentChineseSentence(undefined); // No more sentences
        setChatHistory(prev => [...prev, { role: 'assistant', content: aiResponse.nextAIMessage || 'The conversation continues, but no new sentence was provided.' }]);
      }

    } catch (e) {
      console.error("Failed to get AI response for translation chat:", e);
      setAiError(e instanceof Error ? e.message : 'Failed to get response. Please try again.');
    } finally {
      setIsLoadingAIResponse(false);
    }
  };

  const handleEndGame = async () => {
    setIsLoadingAIResponse(true);
    setAiError(null);
    try {
      const summary = await endTranslationChatGame(chatHistory, allVocabularyItems, allGrammarPoints, { responseSchema: TranslationChatGameSummarySchema });
      setGameSummary(summary);
      setIsGameEnded(true);
    } catch (e) {
      console.error("Failed to finalize translation chat game:", e);
      setAiError(e instanceof Error ? e.message : 'Failed to get game summary. Please try again.');
    } finally {
      setIsLoadingAIResponse(false);
    }
  };

  return (
    <div className="flex flex-col h-full p-4 space-y-4 bg-gray-50 rounded-lg shadow">
      <h1 className="text-2xl font-bold text-gray-800">Translation Chat Game</h1>
      <p className="text-gray-600">Category: {category} {tag && `(Tag: ${tag})`}</p>

      <ScrollArea className="flex-grow overflow-y-auto space-y-2 p-2 border rounded-md bg-white">
        {isLoadingAIResponse && chatHistory.length === 0 && !aiError && (
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <p className="ml-2 text-gray-500">Initializing chat with AI assistant...</p>
          </div>
        )}
        {!isLoadingAIResponse && chatHistory.length === 0 && !aiError && (
          <div className="flex justify-center items-center h-full">
            <p className="text-gray-500">Chat started. Waiting for AI to generate the first sentence...</p>
          </div>
        )}
        {chatHistory.map((turn, index) => (
          <div key={index} className={`p-2 rounded-md ${turn.role === 'user' ? 'bg-blue-100 ml-auto' : 'bg-green-100 mr-auto'}`} style={{ maxWidth: '80%' }}>
            <p className="text-sm font-semibold">{turn.role === 'user' ? 'You' : 'AI Assistant'}</p>
            <TextWithTTS text={turn.content} lang="zh-CN" className="whitespace-pre-wrap" iconSize={16} />
            {turn.role === 'user' && turn.evaluation && (
              <div className="mt-1 p-2 border-t border-gray-300 bg-yellow-50 rounded-b-md">
                <p className="text-xs font-bold">Evaluation (Score: {turn.evaluation.score}/100):</p>
                <p className="text-xs">{turn.evaluation.feedback}</p>
                {turn.evaluation.vocabularyMastery && Object.keys(turn.evaluation.vocabularyMastery).length > 0 && (
                  <div className="mt-1">
                    <p className="text-xs font-bold">Vocabulary Mastery:</p>
                    {Object.entries(turn.evaluation.vocabularyMastery).map(([vocabId, score]) => (
                      <p key={vocabId} className="text-xs">- {vocabId}: {score}/100</p>
                    ))}
                  </div>
                )}
                {turn.evaluation.grammarMastery && Object.keys(turn.evaluation.grammarMastery).length > 0 && (
                  <div className="mt-1">
                    <p className="text-xs font-bold">Grammar Mastery:</p>
                    {Object.entries(turn.evaluation.grammarMastery).map(([grammarId, score]) => (
                      <p key={grammarId} className="text-xs">- {grammarId}: {score}/100</p>
                    ))}
                  </div>
                )}
              </div>
            )}
            {turn.role === 'assistant' && turn.question && (
              <div className="mt-1 p-2 border-t border-gray-300 bg-purple-50 rounded-b-md">
                <p className="text-xs font-bold">Sentence Details:</p>
                {turn.question.targetVocabulary && turn.question.targetVocabulary.length > 0 && (
                  <p className="text-xs">Target Vocab: {turn.question.targetVocabulary.join(', ')}</p>
                )}
                {turn.question.targetGrammar && turn.question.targetGrammar.length > 0 && (
                  <p className="text-xs">Target Grammar: {turn.question.targetGrammar.join(', ')}</p>
                )}
                {turn.question.hskLevel && (
                  <p className="text-xs">HSK Level: {turn.question.hskLevel}</p>
                )}
              </div>
            )}
          </div>
        ))}
      </ScrollArea>

      {aiError && (
        <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
          <p><span className="font-semibold">Chat Error:</span> {aiError}</p>
          {chatHistory.length === 0 && (
            <p className="mt-1">Could not initialize chat. Please try ending the chat and starting again, or refresh the page.</p>
          )}
        </div>
      )}
      
      {!isGameEnded && (
        <div className="flex items-center space-x-2">
          <Textarea
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder="Type your English translation..."
            className="flex-grow"
            rows={2}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            disabled={isLoadingAIResponse || !currentChineseSentence}
          />
          <Button onClick={handleSendMessage} disabled={isLoadingAIResponse || !userInput.trim() || !currentChineseSentence}>
            {isLoadingAIResponse ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Send Translation'}
          </Button>
        </div>
      )}

      {isGameEnded && gameSummary && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle>Game Summary</CardTitle>
            <CardDescription>Your progress in Translation Chat</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="font-semibold">Overall Feedback:</p>
            <p>{gameSummary.overallFeedback}</p>
            {gameSummary.vocabularyProgressSummary && Object.keys(gameSummary.vocabularyProgressSummary).length > 0 && (
              <div className="mt-2">
                <p className="font-semibold">Vocabulary Progress:</p>
                {Object.entries(gameSummary.vocabularyProgressSummary).map(([vocabId, stats]: [string, { correctAttempts: number; totalAttempts: number; masteryScore: number }]) => (
                  <p key={vocabId} className="text-sm">- {vocabId}: Correct {stats.correctAttempts}/{stats.totalAttempts} (Mastery: {stats.masteryScore}/100)</p>
                ))}
              </div>
            )}
            {gameSummary.grammarProgressSummary && Object.keys(gameSummary.grammarProgressSummary).length > 0 && (
              <div className="mt-2">
                <p className="font-semibold">Grammar Progress:</p>
                {Object.entries(gameSummary.grammarProgressSummary).map(([grammarId, stats]: [string, { correctAttempts: number; totalAttempts: number; masteryScore: number }]) => (
                  <p key={grammarId} className="text-sm">- {grammarId}: Correct {stats.correctAttempts}/{stats.totalAttempts} (Mastery: {stats.masteryScore}/100)</p>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {!isGameEnded && (
        <Button onClick={handleEndGame} variant="outline" className="mt-auto" disabled={isLoadingAIResponse}>
          End Chat and Get Summary
        </Button>
      )}
    </div>
  );
};

export default TranslationChatGame;
