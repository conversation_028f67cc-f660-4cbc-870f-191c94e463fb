import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface LoadingDisplayProps {
  title: string;
  message: string;
  progressValue?: number;
}

export function LoadingDisplay({ title, message, progressValue }: LoadingDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {progressValue !== undefined && <Progress value={progressValue} className="w-full" />}
        <p className="text-center mt-4">{message}</p>
      </CardContent>
    </Card>
  );
}