import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { VocabularyItem, GameType } from '@/features/adaptiveVocabularyGame';

interface QuestionDisplayProps {
  currentItem: VocabularyItem;
  options: string[];
  gameType: GameType;
  onAnswerSelected: (answer: string) => void;
  score: number;
  totalQuestions: number;
  currentQuestionIndex: number;
  questionPrompt: React.ReactNode; // Allow more flexible question prompt
}

export function QuestionDisplay({
  currentItem,
  options,
  gameType,
  onAnswerSelected,
  score,
  totalQuestions,
  currentQuestionIndex,
  questionPrompt,
}: QuestionDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {/* The main component will determine the actual question title based on gameType */}
            {/* This component just displays it. For example, if gameType is 'pinyin', questionPrompt might be the Hanzi. */}
            <TextWithTTS text={String(questionPrompt)} />
          </CardTitle>
          <div className="text-sm text-muted-foreground">
            Score: {score} / {totalQuestions}
          </div>
        </div>
        <CardDescription>
          Question {currentQuestionIndex + 1} of {totalQuestions}
        </CardDescription>
        <Progress
          value={totalQuestions > 0 ? ((currentQuestionIndex) / totalQuestions * 100) : 0}
          className="w-full mt-2"
        />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {options.map((option, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Button
                className="w-full h-auto min-h-[5rem] text-lg p-4 justify-center items-center flex"
                variant="outline"
                onClick={() => onAnswerSelected(option)}
              >
                <TextWithTTS text={option} />
              </Button>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}