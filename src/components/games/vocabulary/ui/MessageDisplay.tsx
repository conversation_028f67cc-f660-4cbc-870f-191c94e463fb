import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface MessageDisplayProps {
  title: string;
  message: string;
  onAction?: () => void;
  actionLabel?: string;
  isError?: boolean;
}

export function MessageDisplay({ title, message, onAction, actionLabel, isError }: MessageDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className={isError ? "text-red-500" : ""}>{message}</p>
        {onAction && actionLabel && (
          <Button onClick={onAction} className="mt-4">
            {actionLabel}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}