"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardFooter, // Import CardFooter
} from "@/components/ui/card";
import TextWithTTS from "@/components/ui/TextWithTTS";
// import { Separator } from "@/components/ui/separator"; // Removed as it doesn't exist
import { z } from "zod";

// Re-define AIEvaluationOutput Zod Schema and Type for this component's self-sufficiency
const AIEvaluationOutputSchema = z.object({
  overallFeedback: z.string(),
  corrections: z.array(
    z.object({
      originalText: z.string(),
      suggestedCorrection: z.string(),
      explanation: z.string().optional(),
    })
  ),
  vocabularySuggestions: z.array(
    z.object({
      wordUsed: z.string().optional(),
      suggestion: z.string(),
      relevantVocabulary: z.array(z.string()).optional(),
    })
  ),
  grammarSuggestions: z.array(
    z.object({
      area: z.string(),
      suggestion: z.string(),
      relevantGrammarRule: z.string().optional(),
    })
  ),
});

type AIEvaluationOutput = z.infer<typeof AIEvaluationOutputSchema>;

interface WritingFeedbackDisplayProps {
  topic: string;
  userContent: string;
  evaluationResult: AIEvaluationOutput;
  onStartNewTopic: () => void;
}

export function WritingFeedbackDisplay({
  topic,
  userContent,
  evaluationResult,
  onStartNewTopic,
}: WritingFeedbackDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>AI Writing Evaluation</CardTitle>
        <CardDescription>Detailed feedback on your writing.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="font-semibold text-lg mb-2">Your Submission</h3>
          <p className="text-sm text-muted-foreground">Topic: <TextWithTTS text={topic} /></p>
          <div className="border rounded p-4 bg-gray-50 dark:bg-gray-800 mt-2">
            <p className="whitespace-pre-wrap"><TextWithTTS text={userContent} /></p>
          </div>
        </div>

        <div className="border-b pb-4 mb-4" /> {/* Replaced Separator */}

        <div>
          <h3 className="font-semibold text-lg mb-2">Overall Feedback</h3>
          <p className="whitespace-pre-wrap"><TextWithTTS text={evaluationResult.overallFeedback} /></p>
        </div>

        {evaluationResult.corrections.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Corrections</h3>
            <div className="space-y-3">
              {evaluationResult.corrections.map((correction, index) => (
                <div key={index} className="p-3 border rounded bg-yellow-50 dark:bg-yellow-900/20">
                  <p className="text-sm">
                    <span className="font-medium">Original:</span>{" "}
                    <span className="line-through text-red-600 dark:text-red-400">
                      <TextWithTTS text={correction.originalText} />
                    </span>
                  </p>
                  <p className="text-sm mt-1">
                    <span className="font-medium">Correction:</span>{" "}
                    <span className="text-green-600 dark:text-green-400">
                      <TextWithTTS text={correction.suggestedCorrection} />
                    </span>
                  </p>
                  {correction.explanation && (
                    <p className="text-xs text-muted-foreground mt-1">
                      <TextWithTTS text={correction.explanation} />
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {evaluationResult.vocabularySuggestions.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Vocabulary Suggestions</h3>
            <ul className="list-disc pl-5 space-y-1">
              {evaluationResult.vocabularySuggestions.map((suggestion, index) => (
                <li key={index} className="text-sm">
                  <TextWithTTS text={suggestion.suggestion} />
                  {suggestion.wordUsed && (
                    <span className="text-muted-foreground ml-1">
                      {" "}
                      (Used: <TextWithTTS text={suggestion.wordUsed} />)
                    </span>
                  )}
                  {suggestion.relevantVocabulary && suggestion.relevantVocabulary.length > 0 && (
                    <span className="text-muted-foreground ml-1">
                      {" "}
                      (Relevant: {suggestion.relevantVocabulary.map(v => <TextWithTTS key={v} text={v} />).join(", ")})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {evaluationResult.grammarSuggestions.length > 0 && (
          <div>
            <h3 className="font-semibold text-lg mb-2">Grammar Suggestions</h3>
            <ul className="list-disc pl-5 space-y-1">
              {evaluationResult.grammarSuggestions.map((suggestion, index) => (
                <li key={index} className="text-sm">
                  <TextWithTTS text={suggestion.suggestion} />
                  {suggestion.relevantGrammarRule && (
                    <span className="text-muted-foreground ml-1">
                      {" "}
                      (Rule: <TextWithTTS text={suggestion.relevantGrammarRule} />)
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button className="w-full" onClick={onStartNewTopic}>
          Start New Topic
        </Button>
      </CardFooter>
    </Card>
  );
}
