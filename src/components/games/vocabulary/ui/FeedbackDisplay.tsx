import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { VocabularyItem } from '@/types/content';
import { VocabularyProgress } from '@/types/progress';
import { VocabularyExampleDisplay } from '../VocabularyExampleDisplay';
import MasteryLevelDisplay from '@/components/progress/MasteryLevelDisplay';
import { getVocabularyMasteryLevel } from '@/lib/spaced-repetition';
import { format } from 'date-fns';

interface FeedbackDisplayProps {
  currentItem: VocabularyItem;
  options: string[];
  selectedAnswer: string;
  isCorrect: boolean;
  correctAnswerText: string;
  feedbackMessage: string;
  onContinue: () => void;
  score: number;
  totalQuestions: number;
  currentQuestionIndex: number;
  questionPrompt: React.ReactNode;
  lastReviewResult?: { efactor: number; interval: number; repetition: number; dueDate: string; };
  vocabularyProgressMap: Record<string, VocabularyProgress>;
}

export function FeedbackDisplay({
  currentItem,
  options,
  selectedAnswer,
  isCorrect,
  correctAnswerText,
  feedbackMessage,
  onContinue,
  score,
  totalQuestions,
  currentQuestionIndex,
  questionPrompt,
  lastReviewResult,
  vocabularyProgressMap,
}: FeedbackDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
             <TextWithTTS text={String(questionPrompt)} />
          </CardTitle>
          <div className="text-sm text-muted-foreground">
            Score: {score} / {totalQuestions}
          </div>
        </div>
        <Progress
          value={totalQuestions > 0 ? ((currentQuestionIndex + 1) / totalQuestions * 100) : 0}
          className="w-full mt-2"
        />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {options.map((option, index) => (
            <Button
              key={index}
              className={`w-full h-auto min-h-[5rem] text-lg p-4 justify-center items-center flex ${
                option === correctAnswerText ? 'bg-green-500 hover:bg-green-600 border-green-500' :
                option === selectedAnswer ? 'bg-red-500 hover:bg-red-600 border-red-500' :
                'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
              }`}
              disabled={true}
              variant={option === correctAnswerText || option === selectedAnswer ? "default" : "outline"}
            >
              <TextWithTTS text={option} />
            </Button>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex-col items-center space-y-4 pt-4 border-t mt-4">
        {feedbackMessage && (
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`text-xl font-semibold ${isCorrect ? 'text-green-600' : 'text-red-600'}`}
          >
            {feedbackMessage}
          </motion.p>
        )}
        {lastReviewResult && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-muted-foreground mt-2 p-2 border rounded-md bg-gray-50 dark:bg-gray-800"
          >
            <p><strong>SM-2 Update:</strong></p>
            <p>Easiness Factor (EF): {lastReviewResult.efactor?.toFixed(2)}</p>
            <p>Interval: {lastReviewResult.interval} days</p>
            <p>Repetitions: {lastReviewResult.repetition}</p>
            <p>Next Review: {lastReviewResult.dueDate && new Date(lastReviewResult.dueDate).toString() !== 'Invalid Date' ? format(new Date(lastReviewResult.dueDate), 'PPP') : 'N/A'}</p>
          </motion.div>
        )}

        {console.log('FeedbackDisplay - currentItem:', currentItem, 'vocabularyProgressMap:', vocabularyProgressMap)}
        {currentItem && vocabularyProgressMap && currentItem.id && vocabularyProgressMap[currentItem.id] && (
          <div className="mt-4">
            <h3 className="text-md font-semibold mb-2">Current Mastery:</h3>
            <MasteryLevelDisplay
              level={getVocabularyMasteryLevel(vocabularyProgressMap[currentItem.id])}
              progress={
                // Calculate a numerical progress for the progress bar
                (() => {
                  const progress = vocabularyProgressMap[currentItem.id];
                  const masteryLevel = getVocabularyMasteryLevel(progress);
                  let masteryProgress = 0;
                  if (masteryLevel === 'Novice') masteryProgress = 10;
                  else if (masteryLevel === 'Beginner') masteryProgress = 30;
                  else if (masteryLevel === 'Intermediate') masteryProgress = 50;
                  else if (masteryLevel === 'Advanced') masteryProgress = 70;
                  else if (masteryLevel === 'Mastered') masteryProgress = 90;
                  masteryProgress += ((progress.efactor - 1.3) / (2.5 - 1.3)) * 10;
                  return Math.min(100, Math.max(0, masteryProgress));
                })()
              }
            />
          </div>
        )}

        <VocabularyExampleDisplay vocabulary={currentItem} />
        <Button onClick={onContinue} className="mt-2 w-full sm:w-auto" size="lg">
          {currentQuestionIndex < totalQuestions - 1 ? 'Next Question' : 'Show Summary'}
        </Button>
      </CardFooter>
    </Card>
  );
}