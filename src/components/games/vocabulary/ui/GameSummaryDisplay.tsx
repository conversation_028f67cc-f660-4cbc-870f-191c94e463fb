import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export interface GameSummaryDisplayProps {
  finalScore?: number; // Made optional as score from context might be used directly
  score: number; // Current score from context
  totalQuestions: number;
  totalTimeTaken?: number; // In milliseconds
  onPlayAgain: () => void;
  playAgainButtonText?: string; // Added for custom button text
  message?: string; // Added for custom message
  accuracy?: number; // Added for accuracy display
}

export function GameSummaryDisplay({
  finalScore,
  score,
  totalQuestions,
  totalTimeTaken,
  onPlayAgain,
}: GameSummaryDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-3xl font-bold text-center text-green-600">Game Complete!</CardTitle>
        <CardDescription className="text-center text-lg">
          Congratulations! Here&apos;s your summary:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 text-center">
        <div className="text-5xl font-bold text-blue-700">
          {score ?? finalScore} / {totalQuestions}
        </div>
        {totalTimeTaken !== undefined && (
          <p className="text-lg text-gray-600">
            Total Time: <span className="font-semibold">{(totalTimeTaken / 1000).toFixed(1)} seconds</span>
          </p>
        )}
        {/* You can add more stats here if needed, e.g., accuracy, longest streak */}
      </CardContent>
      <CardFooter>
        <Button
          className="w-full text-lg py-3"
          size="lg"
          onClick={onPlayAgain}
        >
          Play Again
        </Button>
      </CardFooter>
    </Card>
  );
}
