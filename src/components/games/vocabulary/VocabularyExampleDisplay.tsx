'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { VocabularyItem } from '@/types/content';
import { motion } from 'framer-motion';
import TextWithTTS from '@/components/ui/TextWithTTS';

interface VocabularyExampleDisplayProps {
  vocabulary: VocabularyItem;
  onContinue?: () => void; // Made optional
}

export function VocabularyExampleDisplay({ vocabulary, onContinue }: VocabularyExampleDisplayProps) {
  const [currentExampleIndex, setCurrentExampleIndex] = useState(0);

  const handleNextExample = () => {
    if (currentExampleIndex < vocabulary.examples.length - 1) {
      setCurrentExampleIndex(prev => prev + 1);
    } else if (onContinue) { // Call onContinue only if it's provided
      onContinue();
    }
    // If onContinue is not provided and it's the last example, the button will just show "Continue"
    // but won't do anything further, which is fine if it's embedded.
  };

  // Hide the button if onContinue is not provided and it's the last example
  const showButton = onContinue || currentExampleIndex < vocabulary.examples.length - 1;


  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-blue-600">
            <TextWithTTS text={vocabulary.word} /> (<TextWithTTS text={vocabulary.pinyin} />)
          </CardTitle>
          <p className="text-gray-600 italic mt-2">{vocabulary.partOfSpeech}</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Definitions:</h3>
            <ul className="list-disc ml-5 space-y-2">
              {vocabulary.definitions.map((def, index) => (
                <li key={index} className="text-gray-700">{def}</li>
              ))}
            </ul>
          </div>

          {vocabulary.examples.length > 0 && (
            <div className="space-y-4 bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold">Example {currentExampleIndex + 1} of {vocabulary.examples.length}:</h3>
              <div className="space-y-2">
                <p className="text-xl text-blue-600"><TextWithTTS text={vocabulary.examples[currentExampleIndex].chinese} /></p>
                <p className="text-gray-600 italic"><TextWithTTS text={vocabulary.examples[currentExampleIndex].pinyin} /></p>
                <p className="text-gray-700">{vocabulary.examples[currentExampleIndex].english}</p>
              </div>
            </div>
          )}

          {showButton && (
            <button
              onClick={handleNextExample}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {currentExampleIndex < vocabulary.examples.length - 1 ? 'Next Example' : 'Continue'}
            </button>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}