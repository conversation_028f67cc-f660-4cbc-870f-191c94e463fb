"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { VocabularyItem, GrammarPoint } from "@/types/content";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input"; // Import Input for custom topic
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"; // Import RadioGroup
import { Label } from "@/components/ui/label"; // Import Label
import { llmService } from "../../../lib/llm-abstraction/llm-abstraction-service";
import TextWithTTS from "@/components/ui/TextWithTTS";
import { courseData } from "../../../lib/course-data";
import { z } from "zod";
import { WritingFeedbackDisplay } from "./ui/WritingFeedbackDisplay";
import { LoadingDisplay } from "./ui/LoadingDisplay";
import { ScrollArea } from "@/components/ui/scroll-area";
import { VocabularyHighlighter } from "@/components/vocabulary/VocabularyHighlighter"; // Import VocabularyHighlighter

// Define AIEvaluationOutput Zod Schema and Type
const AIEvaluationOutputSchema = z.object({
  overallFeedback: z.string(),
  corrections: z.array(
    z.object({
      originalText: z.string(),
      suggestedCorrection: z.string(),
      explanation: z.string().optional(),
    })
  ),
  vocabularySuggestions: z.array(
    z.object({
      wordUsed: z.string().optional(),
      suggestion: z.string(),
      relevantVocabulary: z.array(z.string()).optional(),
    })
  ),
  grammarSuggestions: z.array(
    z.object({
      area: z.string(),
      suggestion: z.string(),
      relevantGrammarRule: z.string().optional(),
    })
  ),
});

// Define new schemas for AI suggestions and example writing
const AISuggestionsOutputSchema = z.object({
  suggestedVocabulary: z.array(
    z.object({
      word: z.string(),
      meaning: z.string(),
    })
  ),
  suggestedGrammar: z.array(
    z.object({
      ruleName: z.string(),
      explanation: z.string(),
    })
  ),
});

const AIExampleWritingOutputSchema = z.object({
  exampleWriting: z.string(),
});

type AIEvaluationOutput = z.infer<typeof AIEvaluationOutputSchema>;
type AISuggestionsOutput = z.infer<typeof AISuggestionsOutputSchema>;
type AIExampleWritingOutput = z.infer<typeof AIExampleWritingOutputSchema>;

interface WritingGameProps {
  vocabulary: VocabularyItem[];
  category: string;
  tag?: string;
}

interface WritingGameState {
  topic: string;
  content: string;
  isComplete: boolean;
  isEvaluating: boolean;
  evaluationError: string | null;
  evaluationResult: AIEvaluationOutput | null;
  topicSource: "ai" | "user"; // New: Source of the topic
  userCustomTopic: string; // New: User's custom topic input
  aiSuggestions: AISuggestionsOutput | null; // New: AI suggested vocab/grammar
  exampleWriting: string | null; // New: AI generated example writing
  isGeneratingSuggestions: boolean; // New: Loading state for suggestions
  isGeneratingExample: boolean; // New: Loading state for example writing
}

export function WritingGame({ vocabulary, category, tag }: WritingGameProps) {
  const [gameState, setGameState] = useState<WritingGameState>({
    topic: "Loading topic...",
    content: "",
    isComplete: false,
    isEvaluating: false,
    evaluationError: null,
    evaluationResult: null,
    topicSource: "ai", // Default to AI generated topic
    userCustomTopic: "",
    aiSuggestions: null,
    exampleWriting: null,
    isGeneratingSuggestions: false,
    isGeneratingExample: false,
  });
  const [topicLoading, setTopicLoading] = useState(true);
  const [grammarPoints, setGrammarPoints] = useState<GrammarPoint[]>([]); // State to store grammar points

  // Fetch grammar points on component mount
  useEffect(() => {
    const fetchGrammar = async () => {
      const points = await courseData.getGrammarPointsByCategory(category);
      setGrammarPoints(points);
    };
    fetchGrammar();
  }, [category]);

  const generateNewTopic = useCallback(
    async (
      currentCategory: string,
      vocabList: VocabularyItem[],
      grammarList: GrammarPoint[]
    ) => {
      setTopicLoading(true);
      try {
        const vocabularyWords = vocabList.map((item) => item.word).join(", ");
        const grammarRules = grammarList
          .map((item) => item.ruleName)
          .join(", ");

        const prompt = `
        You are a Chinese language learning assistant.
        Generate a single, engaging writing topic appropriate for a student learning Chinese.
        The topic should be suitable for the category: "${currentCategory}".
        The student has access to the following vocabulary: ${vocabularyWords}.
        The student has access to the following grammar points: ${grammarRules}.
        Generate a topic that encourages the use of a diverse range of these vocabulary words and grammar points, ensuring the topic is not too narrow and allows for creative application of the provided linguistic elements.
        The topic should be in Chinese.
        Provide only the topic text, no extra explanations or labels.
        Example: "描述你最喜欢的动物"
      `;

        const resultText = await llmService.generateRawText(prompt, {
          temperature: 0.7,
          maxTokens: 50,
        });
        setGameState((prev) => ({ ...prev, topic: resultText.trim() }));
      } catch (error) {
        console.error("Error generating topic with AI:", error);
        const fallbackTopics: { [key: string]: string[] } = {
          综合: ["我的日常生活", "介绍一个你的朋友", "你如何学习汉语？"],
          听说: ["模拟一次问路对话", "描述你今天的天气怎么样", "练习点餐"],
          阅读: [
            "写一篇关于你读过的书的简短摘要",
            "描述图片中的场景",
            "解释一个成语的意思和用法",
          ],
          写作: ["写一篇日记", "写一封信给你的朋友", "描述你的理想工作"],
        };
        const categoryTopics =
          fallbackTopics[currentCategory] || fallbackTopics["综合"];
        const randomTopic =
          categoryTopics[Math.floor(Math.random() * categoryTopics.length)];
        setGameState((prev) => ({ ...prev, topic: randomTopic }));
      } finally {
        setTopicLoading(false);
      }
    },
    [setGameState, llmService]
  );

  // Effect to generate topic based on topicSource
  useEffect(() => {
    if (gameState.topicSource === "ai") {
      generateNewTopic(category, vocabulary, grammarPoints);
    } else {
      setGameState((prev) => ({ ...prev, topic: prev.userCustomTopic }));
    }
  }, [
    category,
    vocabulary,
    grammarPoints,
    generateNewTopic,
    gameState.topicSource,
    gameState.userCustomTopic,
  ]);

  // New: Function to fetch AI suggestions
  const fetchAISuggestions = useCallback(async () => {
    setGameState((prev) => ({ ...prev, isGeneratingSuggestions: true }));
    try {
      const vocabularyList = vocabulary.map((item) => ({
        word: item.word || item.character,
        meaning: item.meaning,
      }));
      const grammarPointsList = grammarPoints.map((point) => ({
        ruleName: point.ruleName,
        explanation: point.explanation,
      }));

      const prompt = `
        You are an AI assistant helping a Chinese language learner.
        The current writing topic is: "${gameState.topic}".
        The student is learning in the "${category}" category.

        Here is the vocabulary available to the user for this category:
        ${vocabularyList.map((v) => `- ${v.word}: ${v.meaning}`).join("\n")}

        Here are relevant grammar points for this category:
        ${grammarPointsList.map((g) => `- ${g.ruleName}: ${g.explanation}`).join("\n")}

        Based on the topic and the provided vocabulary and grammar, suggest a few relevant vocabulary words and grammar points that the student could use to effectively write about the topic.
        Focus on suggestions that would enhance their writing and demonstrate their understanding of the category's linguistic elements.
        Provide the output in a structured JSON format according to the AISuggestionsOutputSchema:
        {
          "suggestedVocabulary": [
            { "word": "...", "meaning": "..." }
          ],
          "suggestedGrammar": [
            { "ruleName": "...", "explanation": "..." }
          ]
        }
        Ensure all required fields are present in the JSON output.
      `;

      const suggestions = await llmService.generateStructuredOutput(prompt, {
        responseSchema: AISuggestionsOutputSchema,
        temperature: 0.7,
        maxTokens: 500,
      });
      setGameState((prev) => ({ ...prev, aiSuggestions: suggestions }));
    } catch (error) {
      console.error("Error fetching AI suggestions:", error);
      setGameState((prev) => ({ ...prev, aiSuggestions: null })); // Clear suggestions on error
    } finally {
      setGameState((prev) => ({ ...prev, isGeneratingSuggestions: false }));
    }
  }, [category, vocabulary, grammarPoints, gameState.topic]);

  // New: Function to fetch AI example writing
  const fetchAIExampleWriting = useCallback(async () => {
    setGameState((prev) => ({ ...prev, isGeneratingExample: true }));
    try {
      const vocabularyList = vocabulary.map((item) => ({
        word: item.word || item.character,
        meaning: item.meaning,
      }));
      const grammarPointsList = grammarPoints.map((point) => ({
        ruleName: point.ruleName,
        explanation: point.explanation,
      }));

      const prompt = `
        You are an AI assistant providing an example writing for a Chinese language learner.
        The writing topic is: "${gameState.topic}".
        The student is learning in the "${category}" category.

        Here is the vocabulary available to the user for this category:
        ${vocabularyList.map((v) => `- ${v.word}: ${v.meaning}`).join("\n")}

        Here are relevant grammar points for this category:
        ${grammarPointsList.map((g) => `- ${g.ruleName}: ${g.explanation}`).join("\n")}

        Please provide an example writing that addresses the topic and effectively uses some of the provided vocabulary and grammar points.
        The example should be natural and appropriate for a learner.
        Provide the output in a structured JSON format according to the AIExampleWritingOutputSchema:
        {
          "exampleWriting": "..."
        }
        Ensure all required fields are present in the JSON output.
      `;

      const example = await llmService.generateStructuredOutput(prompt, {
        responseSchema: AIExampleWritingOutputSchema,
        temperature: 0.7,
        maxTokens: 500,
      });
      setGameState((prev) => ({ ...prev, exampleWriting: example.exampleWriting }));
    } catch (error) {
      console.error("Error fetching AI example writing:", error);
      setGameState((prev) => ({ ...prev, exampleWriting: null })); // Clear example on error
    } finally {
      setGameState((prev) => ({ ...prev, isGeneratingExample: false }));
    }
  }, [category, vocabulary, grammarPoints, gameState.topic]);

  function handleContentChange(value: string) {
    setGameState((prev) => ({
      ...prev,
      content: value,
    }));
  }

  function handleTopicSourceChange(value: "ai" | "user") {
    setGameState((prev) => ({
      ...prev,
      topicSource: value,
      topic: value === "user" ? prev.userCustomTopic : "Loading topic...", // Reset topic based on source
      aiSuggestions: null, // Clear suggestions when topic source changes
      exampleWriting: null, // Clear example when topic source changes
      isGeneratingSuggestions: false,
      isGeneratingExample: false,
    }));
  }

  function handleUserCustomTopicChange(value: string) {
    setGameState((prev) => ({
      ...prev,
      userCustomTopic: value,
      topic: value, // Update topic immediately if user source is selected
      aiSuggestions: null, // Clear suggestions when custom topic changes
      exampleWriting: null, // Clear example when custom topic changes
      isGeneratingSuggestions: false,
      isGeneratingExample: false,
    }));
  }

  const fetchAIEvaluation = useCallback(async () => {
    setGameState((prev) => ({
      ...prev,
      isEvaluating: true,
      evaluationError: null,
      evaluationResult: null,
    }));

    try {
      const vocabularyList = vocabulary.map((item) => ({
        word: item.word || item.character,
        meaning: item.meaning,
      }));

      const grammarPointsList = grammarPoints.map((point) => ({
        ruleName: point.ruleName,
        explanation: point.explanation,
      }));

      const prompt = `
        You are an AI assistant specialized in evaluating Chinese writing for language learners.
        The user has written a text based on the topic: "${gameState.topic}".
        Their submission is: "${gameState.content}".

        Here is the vocabulary available to the user for this category:
        ${vocabularyList.map((v) => `- ${v.word}: ${v.meaning}`).join("\n")}

        Here are relevant grammar points for this category:
        ${grammarPointsList.map((g) => `- ${g.ruleName}: ${g.explanation}`).join("\n")}

        Please provide a comprehensive evaluation of the user's writing.
        Focus on:
        1. Overall feedback on clarity, coherence, and how well it addresses the topic.
        2. Specific corrections for grammatical errors, typos, or unnatural phrasing. For each correction, provide the original text, the suggested correction, and a brief explanation.
        3. Suggestions for vocabulary improvement or alternative words that would fit better or expand their expression.
        4. Suggestions for grammar improvement, referencing the provided grammar points where applicable.

        Provide the output in a structured JSON format according to the AIEvaluationOutputSchema:
        {
          "overallFeedback": "...",
          "corrections": [
            {
              "originalText": "...",
              "suggestedCorrection": "...",
              "explanation": "..."
            }
          ],
          "vocabularySuggestions": [
            {
              "wordUsed": "...",
              "suggestion": "...",
              "relevantVocabulary": ["..."]
            }
          ],
          "grammarSuggestions": [
            {
              "area": "...",
              "suggestion": "...",
              "relevantGrammarRule": "..."
            }
          ]
        }
        Ensure all required fields are present in the JSON output.
      `;

      const evaluationResult = await llmService.generateStructuredOutput(
        prompt,
        {
          responseSchema: AIEvaluationOutputSchema,
          temperature: 0.7,
          maxTokens: 1000,
        }
      );

      setGameState((prev) => ({
        ...prev,
        evaluationResult,
        isEvaluating: false,
        isComplete: true,
      }));
    } catch (error) {
      console.error("Error fetching AI evaluation:", error);
      setGameState((prev) => ({
        ...prev,
        evaluationError:
          error instanceof Error ? error.message : "An unknown error occurred.",
        isEvaluating: false,
      }));
    }
  }, [category, vocabulary, grammarPoints, gameState.topic, gameState.content]);

  function handleComplete() {
    setGameState((prev) => ({
      ...prev,
      isComplete: true,
      isEvaluating: true,
      evaluationError: null,
      evaluationResult: null,
    }));
    fetchAIEvaluation();
  }

  function handleNewTopic() {
    setGameState((prev) => ({
      ...prev,
      content: "",
      isComplete: false,
      isEvaluating: false,
      evaluationError: null,
      evaluationResult: null,
      aiSuggestions: null, // Clear suggestions
      exampleWriting: null, // Clear example
      isGeneratingSuggestions: false,
      isGeneratingExample: false,
    }));
    if (gameState.topicSource === "ai") {
      generateNewTopic(category, vocabulary, grammarPoints); // Regenerate topic using LLM
    } else {
      // If user custom topic, just clear content and reset state
      setGameState((prev) => ({ ...prev, topic: prev.userCustomTopic }));
    }
  }

  if (topicLoading && gameState.topicSource === "ai") {
    return (
      <LoadingDisplay
        title="Generating Topic"
        message="Please wait while we fetch a new writing prompt."
      />
    );
  }

  if (gameState.isEvaluating) {
    return (
      <LoadingDisplay
        title="Evaluating Writing"
        message="Please wait while AI evaluates your writing..."
      />
    );
  }

  if (gameState.evaluationError) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Evaluation Error</CardTitle>
          <CardDescription>
            An error occurred during AI evaluation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-red-500">{gameState.evaluationError}</p>
          <Button className="w-full mt-4" onClick={handleNewTopic}>
            Try Again / Start New Topic
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (gameState.isComplete && gameState.evaluationResult) {
    return (
      <WritingFeedbackDisplay
        topic={gameState.topic}
        userContent={gameState.content}
        evaluationResult={gameState.evaluationResult}
        onStartNewTopic={handleNewTopic}
      />
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Writing Practice</CardTitle>
        <CardDescription>
          Topic:{" "}
          <VocabularyHighlighter
            text={gameState.topic}
            vocabularyItems={vocabulary}
          />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <RadioGroup
            defaultValue="ai"
            value={gameState.topicSource}
            onValueChange={handleTopicSourceChange}
            className="flex items-center space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="ai" id="topic-ai" />
              <Label htmlFor="topic-ai">AI Generated Topic</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="user" id="topic-user" />
              <Label htmlFor="topic-user">My Own Topic</Label>
            </div>
          </RadioGroup>
          {gameState.topicSource === "user" && (
            <Input
              placeholder="Enter your custom topic..."
              value={gameState.userCustomTopic}
              onChange={(e) => handleUserCustomTopicChange(e.target.value)}
              className="mt-2"
            />
          )}
          <Button
            onClick={() => generateNewTopic(category, vocabulary, grammarPoints)}
            disabled={gameState.topicSource === "user"}
            className="mt-2"
          >
            Generate New AI Topic
          </Button>
        </div>

        <Button
          onClick={fetchAISuggestions}
          disabled={gameState.isGeneratingSuggestions || !gameState.topic.trim()}
          className="w-full"
        >
          {gameState.isGeneratingSuggestions
            ? "Generating Suggestions..."
            : "Get Vocabulary & Grammar Suggestions"}
        </Button>

        {gameState.aiSuggestions && (
          <ScrollArea className="h-[150px] w-full rounded-md border p-4">
            <h4 className="mb-2 font-semibold">Suggested Vocabulary:</h4>
            <ul className="list-disc pl-5">
              {gameState.aiSuggestions.suggestedVocabulary.map((item, index) => (
                <li key={`vocab-sugg-${index}`}>
                  <TextWithTTS text={`${item.word} (${item.meaning})`} />
                </li>
              ))}
            </ul>
            <h4 className="mb-2 mt-4 font-semibold">Suggested Grammar:</h4>
            <ul className="list-disc pl-5">
              {gameState.aiSuggestions.suggestedGrammar.map((item, index) => (
                <li key={`grammar-sugg-${index}`}>
                  <TextWithTTS text={`${item.ruleName}: ${item.explanation}`} />
                </li>
              ))}
            </ul>
          </ScrollArea>
        )}

        <Button
          onClick={fetchAIExampleWriting}
          disabled={gameState.isGeneratingExample || !gameState.topic.trim()}
          className="w-full"
        >
          {gameState.isGeneratingExample
            ? "Generating Example..."
            : "Get Example Writing"}
        </Button>

        {gameState.exampleWriting && (
          <ScrollArea className="h-[150px] w-full rounded-md border p-4">
            <h4 className="mb-2 font-semibold">Example Writing:</h4>
            <TextWithTTS text={gameState.exampleWriting} />
          </ScrollArea>
        )}

        <Textarea
          placeholder="Start writing in Chinese..."
          value={gameState.content}
          onChange={(e) => handleContentChange(e.target.value)}
          className="min-h-[200px]"
          enableStt={true}
          sttLang="zh-CN"
        />
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={handleComplete}
          disabled={!gameState.content.trim()}
        >
          Complete Writing
        </Button>
      </CardFooter>
    </Card>
  );
}
