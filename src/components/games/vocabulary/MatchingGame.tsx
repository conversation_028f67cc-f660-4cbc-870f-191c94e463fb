"use client"
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  MatchingGameRoundState,
  initializeMatchingGameRound,
  handleMatchingAttempt,
  updateTimer,
  checkRoundCompletion,
  MatchingGameType // INITIAL_TIMER_SECONDS is no longer exported
} from '@/features/adaptiveVocabularyGame/gameTypes/matchingGameLogic';
import { VocabularyItem } from '@/types/content';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { GameSummaryDisplay, GameSummaryDisplayProps } from './ui/GameSummaryDisplay';
import { MessageDisplay } from './ui/MessageDisplay';
import { LoadingDisplay } from './ui/LoadingDisplay';
import { shuffleArray } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { courseData } from '@/lib/course-data';
import { useVocabularyProgress } from '@/hooks/useVocabularyProgress';
import { VocabularyProgress } from '@/types/progress';

interface MatchingGameProps {
  category?: string;
  tag?: string;
}

const TIMER_INTERVAL_MS = 1000;

export const MatchingGame: React.FC<MatchingGameProps> = ({ category, tag }) => {
  const [allVocabulary, setAllVocabulary] = useState<VocabularyItem[]>([]); // State to hold all vocabulary
  const [isVocabLoading, setIsVocabLoading] = useState<boolean>(true); // Loading state for vocabulary
  const { progress: vocabularyProgressMap } = useVocabularyProgress(allVocabulary); // Use vocabularyProgress hook

  const [gameState, setGameState] = useState<MatchingGameRoundState | null>(null);
  const [level, setLevel] = useState<number>(1);
  const [previousVocabulary, setPreviousVocabulary] = useState<VocabularyItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [gameStatus, setGameStatus] = useState<'playing' | 'won' | 'lost' | 'loading' | 'selectingType'>('selectingType');
  const [selectedMatchingType, setSelectedMatchingType] = useState<MatchingGameType | null>(null);
  const [shuffledOptions, setShuffledOptions] = useState<{ id: string; value: string; type: 'word' | 'pinyin' | 'definitions' }[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Effect to load vocabulary items based on category and tag
  useEffect(() => {
    const loadVocabulary = async () => {
      setIsVocabLoading(true);
      try {
        const vocab = await courseData.getVocabularyItemsByCategoryAndTag(category, tag);
        setAllVocabulary(vocab);
      } catch (error) {
        console.error("Failed to load vocabulary items:", error);
        // Handle error, maybe show a message to the user
      } finally {
        setIsVocabLoading(false);
      }
    };
    loadVocabulary();
  }, [category, tag]);

  const startNewRound = useCallback(async () => {
    if (!selectedMatchingType || isVocabLoading) {
      setGameStatus('selectingType');
      return;
    }

    setIsLoading(true);
    setGameStatus('loading');
    try {
      const newState = await initializeMatchingGameRound(
        level,
        selectedMatchingType,
        previousVocabulary,
        vocabularyProgressMap
      );
      setGameState(newState);
      setShuffledOptions(shuffleArray(newState.options));
      setGameStatus('playing');
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      timerRef.current = setInterval(() => {
        setGameState(prevState => {
          if (!prevState) return null;
          const updatedState = updateTimer(prevState);
          if (checkRoundCompletion(updatedState)) {
            if (timerRef.current) clearInterval(timerRef.current);
            if (updatedState.matchedPairs.length === updatedState.questions.length) {
              setGameStatus('won');
            } else {
              setGameStatus('lost');
            }
          }
          return updatedState;
        });
      }, TIMER_INTERVAL_MS);
    } catch (error) {
      console.error('Failed to initialize game round:', error);
      setGameStatus('lost');
    } finally {
      setIsLoading(false);
    }
  }, [level, previousVocabulary, selectedMatchingType, isVocabLoading, vocabularyProgressMap]);

  useEffect(() => {
    if (selectedMatchingType && gameStatus === 'loading' && !isVocabLoading) { // Only start round if type is selected, status is loading, and vocab is loaded
      startNewRound();
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startNewRound, selectedMatchingType, gameStatus, isVocabLoading]);

  const handleOptionClick = useCallback((option: { id: string; value: string; type: 'word' | 'pinyin' | 'definitions' }) => {
    if (!gameState || gameStatus !== 'playing') return;

    const updatedState = handleMatchingAttempt(gameState, { id: option.id, value: option.value, type: option.type });
    setGameState(updatedState);

    if (checkRoundCompletion(updatedState)) {
      if (timerRef.current) clearInterval(timerRef.current);
      if (updatedState.matchedPairs.length === updatedState.questions.length) {
        setGameStatus('won');
        setPreviousVocabulary(prev => [...prev, ...updatedState.questions]);
      } else {
        setGameStatus('lost');
      }
    }
  }, [gameState, gameStatus]);

  const handlePlayAgain = useCallback(() => {
    if (gameStatus === 'won') {
      setLevel(prev => prev + 1);
    } else {
      setLevel(1);
      setPreviousVocabulary([]);
    }
    setSelectedMatchingType(null);
    setGameStatus('selectingType');
  }, [gameStatus]);

  const getSummaryProps = (): GameSummaryDisplayProps => {
    if (!gameState) return { score: 0, totalQuestions: 0, onPlayAgain: handlePlayAgain, message: '' };
    const totalPossibleMatches = gameState.questions.length;
    const accuracy = totalPossibleMatches > 0 ? (gameState.correctMatches / totalPossibleMatches) * 100 : 0;
    // Use initialTimer for total time taken calculation
    const timeTaken = gameState.initialTimer - gameState.timer;

    return {
      score: gameState.correctMatches,
      totalQuestions: totalPossibleMatches,
      totalTimeTaken: timeTaken * 1000,
      message: gameStatus === 'won' ? 'Congratulations! You matched all characters!' : 'Time ran out or you made too many mistakes!',
      accuracy: accuracy,
      onPlayAgain: handlePlayAgain,
      playAgainButtonText: gameStatus === 'won' ? 'Next Level' : 'Try Again',
    };
  };

  // Debugging logs
  useEffect(() => {
    console.log('Current Game State:', gameState);
    console.log('Shuffled Options:', shuffledOptions);
  }, [gameState, shuffledOptions]);

  if (isVocabLoading) {
    return <LoadingDisplay title="Loading Vocabulary" message="Fetching all vocabulary items..." />;
  }

  if (gameStatus === 'selectingType') {
    return (
      <div className="p-4 max-w-md mx-auto text-center">
        <h1 className="text-2xl font-bold mb-6">Choose Matching Type</h1>
        <RadioGroup
          onValueChange={(value: MatchingGameType) => setSelectedMatchingType(value)}
          value={selectedMatchingType || ''}
          className="grid grid-cols-1 gap-4"
        >
          <Label htmlFor="word-pinyin" className="flex items-center space-x-2 p-4 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
            <RadioGroupItem value="word-pinyin" id="word-pinyin" />
            <span>Word to Pinyin</span>
          </Label>
          <Label htmlFor="word-definitions" className="flex items-center space-x-2 p-4 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
            <RadioGroupItem value="word-definitions" id="word-definitions" />
            <span>Word to Definitions</span>
          </Label>
          <Label htmlFor="pinyin-definitions" className="flex items-center space-x-2 p-4 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
            <RadioGroupItem value="pinyin-definitions" id="pinyin-definitions" />
            <span>Pinyin to Definitions</span>
          </Label>
        </RadioGroup>
        <Button
          onClick={() => startNewRound()}
          disabled={!selectedMatchingType}
          className="mt-6 w-full"
        >
          Start Game
        </Button>
      </div>
    );
  }

  if (isLoading || !gameState) {
    return <LoadingDisplay title="Loading Game" message="Preparing your round..." />;
  }

  if (gameStatus === 'won' || gameStatus === 'lost') {
    return (
      <GameSummaryDisplay
        {...getSummaryProps()}
        onPlayAgain={handlePlayAgain}
        playAgainButtonText={gameStatus === 'won' ? 'Next Level' : 'Try Again'}
      />
    );
  }

  const currentOptionsToDisplay = shuffledOptions.filter(opt => !gameState.matchedPairs.includes(opt.id));

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4 text-center">Vocabulary Matching Game - Level {level}</h1>
      <div className="flex justify-between items-center mb-4">
        <div className="text-lg">Matches: {gameState.correctMatches} / {gameState.questions.length}</div>
        <div className="text-lg">Time: {gameState.timer}s</div>
      </div>
      <Progress value={(gameState.timer / gameState.initialTimer) * 100} className="w-full mb-6" />

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {currentOptionsToDisplay.map((option) => {
          const isSelected = gameState.selected?.id === option.id && gameState.selected?.type === option.type;
          const isMatched = gameState.matchedPairs.includes(option.id);
          return (
          <Card
            key={`${option.id}-${option.type}`}
            className={`relative p-4 text-center cursor-pointer transition-all duration-200
              ${isSelected ? 'bg-blue-200 dark:bg-blue-700 ring-2 ring-blue-500' : ''}
              ${isMatched ? 'opacity-50 cursor-not-allowed bg-green-100 dark:bg-green-800' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
            `}
            onClick={() => handleOptionClick(option)}
          >
            <div className={`absolute top-2 right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white
              ${option.type === 'word' ? 'bg-red-500' :
                option.type === 'pinyin' ? 'bg-blue-500' :
                'bg-green-500'}`}
            >
              {option.type === 'word' ? '字' :
               option.type === 'pinyin' ? '拼' :
               '义'}
            </div>
            <span className="text-xl font-semibold">{option.value}</span>
          </Card>
          );
        })}
      </div>

      {gameState.selected && (
        <MessageDisplay title="Selected" message={`Selected: ${gameState.selected.value}`} />
      )}
      {/* Add feedback for incorrect attempts if desired */}
    </div>
  );
};
