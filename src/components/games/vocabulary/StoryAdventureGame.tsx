'use client';

import { useState, useEffect, useCallback } from 'react';
import { VocabularyItem } from '@/types/content';
import { Button } from '@/components/ui/button';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { VocabularyHighlighter } from '@/components/vocabulary/VocabularyHighlighter';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { generateStoryAdventureContent } from '@/lib/ai-service';

interface StoryAdventureGameProps {
  vocabulary: VocabularyItem[];
  category: string;
  tag?: string; // Add tag prop
  onExitGame: () => void; // Function to go back to GameSelector
}

type Difficulty = 'easy' | 'medium' | 'hard';

interface StoryData {
  storyText: string;
  theme: string;
  questions: Question[];
}

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false';
  questionText: string;
  options?: string[]; // For multiple-choice
  correctAnswer: string | boolean;
  userAnswer?: string | boolean;
  isCorrect?: boolean;
}

export function StoryAdventureGame({ vocabulary, category, tag, onExitGame }: StoryAdventureGameProps) {
  const [difficulty, setDifficulty] = useState<Difficulty>('medium');
  const [currentStoryData, setCurrentStoryData] = useState<StoryData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string | boolean | undefined>(undefined);

  const fetchNewStory = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setShowResults(false);
    setCurrentStoryData(null);
    setCurrentQuestionIndex(0);
    setScore(0);
    setStreak(0);
    setSelectedAnswer(undefined);

    try {
      const data = await generateStoryAdventureContent(vocabulary, category, difficulty);
      setCurrentStoryData(data);

    } catch (err) {
      console.error("Error fetching story:", err);
      setError('Failed to load story. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [vocabulary, category, difficulty]);

  useEffect(() => {
    fetchNewStory();
  }, [fetchNewStory]);

  const handleAnswerSubmit = () => {
    if (selectedAnswer === undefined || !currentStoryData) return;

    const currentQuestion = currentStoryData.questions[currentQuestionIndex];
    const isAnswerCorrect = selectedAnswer === currentQuestion.correctAnswer;

    const updatedQuestions = currentStoryData.questions.map((q, index) => 
      index === currentQuestionIndex ? { ...q, userAnswer: selectedAnswer, isCorrect: isAnswerCorrect } : q
    );
    setCurrentStoryData({ ...currentStoryData, questions: updatedQuestions });

    if (isAnswerCorrect) {
      setScore(prevScore => prevScore + 10); // 10 points per correct answer
      setStreak(prevStreak => prevStreak + 1);
      if ((streak + 1) % 3 === 0) { // Bonus for every 3 streaks
        setScore(prevScore => prevScore + 5); // 5 bonus points
      }
    } else {
      setStreak(0);
    }

    setSelectedAnswer(undefined); // Reset selected answer for next question

    if (currentQuestionIndex < currentStoryData.questions.length - 1) {
      setCurrentQuestionIndex(prevIndex => prevIndex + 1);
    } else {
      setShowResults(true);
    }
  };

  if (isLoading) {
    return <div className="text-center p-10">Loading story adventure...</div>;
  }

  if (error) {
    return (
      <div className="text-center p-10 text-red-500">
        <p>{error}</p>
        <Button onClick={fetchNewStory} className="mt-4">Try Again</Button>
        <Button variant="outline" onClick={onExitGame} className="mt-4 ml-2">Back to Games</Button>
      </div>
    );
  }

  if (!currentStoryData && !isLoading) {
     return (
      <div className="text-center p-10">
        <p>No story data available. Try starting a new game.</p>
        <Button onClick={fetchNewStory} className="mt-4">Start New Game</Button>
        <Button variant="outline" onClick={onExitGame} className="mt-4 ml-2">Back to Games</Button>
      </div>
    );
  }
  
  if (!currentStoryData) return null; // Should be covered by loading/error/no data states

  const currentQuestion = currentStoryData.questions[currentQuestionIndex];

  if (showResults) {
    return (
      <Card className="w-full max-w-2xl mx-auto mt-6">
        <CardHeader>
          <CardTitle>Story Results!</CardTitle>
          <CardDescription>You scored {score} points. Your longest streak was {streak > 0 ? streak : 'N/A'}.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Story Recap:</h3>
            <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md"><TextWithTTS text={currentStoryData.storyText} /></div>
          </div>
          <h3 className="font-semibold">Question Review:</h3>
          {currentStoryData.questions.map((q, index) => (
            <div key={q.id} className={`p-3 rounded-md ${q.isCorrect ? 'bg-green-100' : 'bg-red-100'}`}>
              <p className="font-medium">Q{index + 1}: {q.questionText}</p>
              <p className="text-sm">Your answer: {q.userAnswer?.toString() ?? 'Not answered'}</p>
              {!q.isCorrect && <p className="text-sm">Correct answer: {q.correctAnswer.toString()}</p>}
            </div>
          ))}
          <div className="flex justify-between mt-6">
            <Button onClick={fetchNewStory}>Play New Story (Same Difficulty)</Button>
            <Button variant="outline" onClick={onExitGame}>Back to Games</Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-6 p-4 md:p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Story Adventure: {category} - {difficulty}</h2>
        <Button variant="outline" onClick={onExitGame}>Exit Game</Button>
      </div>

      {!currentStoryData.storyText && !isLoading && (
         <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Choose Difficulty:</h3>
          <RadioGroup defaultValue={difficulty} onValueChange={(value: Difficulty) => setDifficulty(value)} className="flex space-x-4">
            {(['easy', 'medium', 'hard'] as Difficulty[]).map(level => (
              <div key={level} className="flex items-center space-x-2">
                <RadioGroupItem value={level} id={`difficulty-${level}`} />
                <Label htmlFor={`difficulty-${level}`}>{level.charAt(0).toUpperCase() + level.slice(1)}</Label>
              </div>
            ))}
          </RadioGroup>
          <Button onClick={fetchNewStory} className="mt-4">Start Story</Button>
        </div>
      )}

      {currentStoryData.storyText && (
        <Card>
          <CardHeader>
            <CardTitle>Story: <TextWithTTS text={currentStoryData.theme} /></CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap">
              <VocabularyHighlighter
                text={currentStoryData.storyText}
                vocabularyItems={vocabulary}
              />
            </p>
          </CardContent>
        </Card>
      )}

      {currentStoryData.storyText && currentQuestion && !showResults && (
        <Card>
          <CardHeader>
            <CardTitle>Question {currentQuestionIndex + 1} of {currentStoryData.questions.length}</CardTitle>
            <CardDescription><TextWithTTS text={currentQuestion.questionText} /></CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
              <RadioGroup onValueChange={(value) => setSelectedAnswer(value)} value={selectedAnswer as string | undefined}>
                {currentQuestion.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroupItem value={option} id={`q${currentQuestion.id}-option${index}`} />
                    <Label htmlFor={`q${currentQuestion.id}-option${index}`}><TextWithTTS text={option} /></Label>
                  </div>
                ))}
              </RadioGroup>
            )}
            {currentQuestion.type === 'true-false' && (
               <RadioGroup onValueChange={(value) => setSelectedAnswer(value === 'true')} value={selectedAnswer === undefined ? undefined : String(selectedAnswer)}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id={`q${currentQuestion.id}-true`} />
                  <Label htmlFor={`q${currentQuestion.id}-true`}>True</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id={`q${currentQuestion.id}-false`} />
                  <Label htmlFor={`q${currentQuestion.id}-false`}>False</Label>
                </div>
              </RadioGroup>
            )}
            <Button onClick={handleAnswerSubmit} disabled={selectedAnswer === undefined}>
              Submit Answer
            </Button>
            <div className="text-sm">Score: {score} | Streak: {streak}</div>
          </CardContent>
        </Card>
      )}
       <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Change Difficulty:</h3>
          <RadioGroup defaultValue={difficulty} onValueChange={(value: Difficulty) => {
            setDifficulty(value);
            // Optionally, you could trigger fetchNewStory directly here if desired
            // or wait for user to click a "Start with new difficulty" button
          }} className="flex space-x-4 mb-2">
            {(['easy', 'medium', 'hard'] as Difficulty[]).map(level => (
              <div key={level} className="flex items-center space-x-2">
                <RadioGroupItem value={level} id={`change-difficulty-${level}`} />
                <Label htmlFor={`change-difficulty-${level}`}>{level.charAt(0).toUpperCase() + level.slice(1)}</Label>
              </div>
            ))}
          </RadioGroup>
          <Button onClick={fetchNewStory} variant="outline" size="sm">Start New Story with Selected Difficulty</Button>
        </div>
    </div>
  );
}
