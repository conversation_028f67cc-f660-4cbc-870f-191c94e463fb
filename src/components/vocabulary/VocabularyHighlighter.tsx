"use client";

import React, { useState, useCallback } from "react";
import { VocabularyItem } from "@/types/content";
import { cn } from "@/lib/utils"; // Assuming cn utility is available
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"; // Import Dialog components
import TextWithTTS from "@/components/ui/TextWithTTS";

interface VocabularyHighlighterProps {
  text: string;
  vocabularyItems: VocabularyItem[];
}

// Define a mapping for partOfSpeech to Tailwind CSS classes
const partOfSpeechColors: { [key: string]: string } = {
  noun: "text-green-600 dark:text-green-400",
  n: "text-green-600 dark:text-green-400",
  "名": "text-green-600 dark:text-green-400",
  verb: "text-red-600 dark:text-red-400",
  "动": "text-red-600 dark:text-red-400",
  v: "text-red-600 dark:text-red-400",
  adj: "text-purple-600 dark:text-purple-400",
  adjective: "text-purple-600 dark:text-purple-400",
  "形": "text-purple-600 dark:text-purple-400",
  adv: "text-yellow-600 dark:text-yellow-400",
  adverb: "text-yellow-600 dark:text-yellow-400",
  pron: "text-indigo-600 dark:text-indigo-400",
  pronoun: "text-indigo-600 dark:text-indigo-400",
  prep: "text-pink-600 dark:text-pink-400",
  preposition: "text-pink-600 dark:text-pink-400",
  conj: "text-orange-600 dark:text-orange-400",
  conjunction: "text-orange-600 dark:text-orange-400",
  // Add more as needed, or a default
  other: "text-blue-600 dark:text-blue-400", // Default color
};

export function VocabularyHighlighter({
  text,
  vocabularyItems,
}: VocabularyHighlighterProps) {
  const [selectedWord, setSelectedWord] = useState<VocabularyItem | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleWordClick = useCallback((word: VocabularyItem) => {
    setSelectedWord(word);
    setIsModalOpen(true);
  }, []);

  const renderTextWithHighlights = () => {
    if (!text || vocabularyItems.length === 0) {
      return <>{text}</>; // Render plain text if no vocabulary or text
    }

    const parts: React.ReactNode[] = [];
    let lastIndex = 0;

    // Map vocabulary items to ensure 'word' is always a string (using 'character' as fallback)
    const processedVocabulary = vocabularyItems.map(item => ({
      ...item,
      word: item.word || item.character // Ensure 'word' is defined for sorting and regex
    }));

    // Sort vocabulary items by length in descending order to prioritize longer matches
    const sortedVocabulary = [...processedVocabulary].sort(
      (a, b) => (b.word?.length || 0) - (a.word?.length || 0)
    );

    // Create a regex pattern to match all vocabulary words
    // Escape special characters in words for regex safety
    const escapedWords = sortedVocabulary.map((item) =>
      (item.word || item.character).replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
    );
    const regex = new RegExp(`(${escapedWords.join("|")})`, "g");

    text.replace(regex, (match, word, offset) => {
      // Add the text before the current match
      if (offset > lastIndex) {
        parts.push(
          <React.Fragment key={`text-${lastIndex}`}>
            {text.substring(lastIndex, offset)}
          </React.Fragment>
        );
      }

      // Find the vocabulary item that matches the current 'word'
      const vocabItem = sortedVocabulary.find((item) => (item.word || item.character) === word);

      if (vocabItem) {
        const colorClass = vocabItem.partOfSpeech
          ? partOfSpeechColors[vocabItem.partOfSpeech.toLowerCase()] || partOfSpeechColors.other
          : partOfSpeechColors.other;

        parts.push(
          <span
            key={`highlight-${offset}`}
            className={cn(
              "cursor-pointer font-semibold underline decoration-dotted",
              colorClass // Apply dynamic color based on partOfSpeech
            )}
            onClick={() => handleWordClick(vocabItem)}
          >
            {word}
          </span>
        );
      } else {
        // Fallback if for some reason vocabItem is not found (shouldn't happen with the regex)
        parts.push(<React.Fragment key={`text-${offset}`}>{word}</React.Fragment>);
      }

      lastIndex = offset + match.length;
      return match; // Return match to keep replace function working
    });

    // Add any remaining text after the last match
    if (lastIndex < text.length) {
      parts.push(
        <React.Fragment key={`text-${lastIndex}`}>
          {text.substring(lastIndex)}
        </React.Fragment>
      );
    }

    return <>{parts}</>;
  };

  return (
    <>
      {renderTextWithHighlights()}

      {selectedWord && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                <TextWithTTS text={selectedWord.word} />
                {selectedWord.pinyin && (
                  <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                    ({selectedWord.pinyin})
                  </span>
                )}
              </DialogTitle>
              <DialogDescription>
                <TextWithTTS text={selectedWord.meaning} />
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {selectedWord.examples && selectedWord.examples.length > 0 && (
                <div>
                  <h5 className="font-semibold">Examples:</h5>
                  <ul className="list-disc pl-5 text-sm">
                    {selectedWord.examples.map((example, index) => (
                      <li key={index}>
                        <TextWithTTS text={example.chinese} /> -{" "}
                        <TextWithTTS text={example.english} />
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {/* Add more details as needed from VocabularyItem */}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
