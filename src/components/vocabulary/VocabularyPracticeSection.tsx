'use client';

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { VocabularyItem as OriginalVocabularyItem } from '@/types/content';
import InteractiveVocabularyChat from '@/components/practice/InteractiveVocabularyChat';

// Define a serializable version of VocabularyItem for client components
interface SerializableVocabularyItem extends Omit<OriginalVocabularyItem, 'createdAt'> {
  createdAt: string | null;
  definitions: string[];
  examples: { chinese: string; pinyin: string; english: string; }[];
}

interface VocabularyPracticeSectionProps {
  vocabularyItem: SerializableVocabularyItem;
}

const VocabularyPracticeSection: React.FC<VocabularyPracticeSectionProps> = ({ vocabularyItem }) => {
  const [showPracticeChat, setShowPracticeChat] = useState(false);

  return (
    <div className="mt-8">
      {!showPracticeChat ? (
        <Button onClick={() => setShowPracticeChat(true)}>
          Practice this Vocabulary Item
        </Button>
      ) : (
        <div className="mt-8 h-[600px]"> {/* Set a fixed height for the chat */}
          <InteractiveVocabularyChat
            vocabularyItem={vocabularyItem}
            onEndChat={() => setShowPracticeChat(false)}
          />
        </div>
      )}
    </div>
  );
};

export default VocabularyPracticeSection;
