import React, { ComponentType, ReactNode } from 'react';
import { containsChinese } from '@/lib/utils';
import useTextToSpeech, { UseTextToSpeechOptions } from '@/hooks/useTextToSpeech';

// Simple inline SVG Speaker Icon
const SpeakerIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    aria-hidden="true"
  >
    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
    <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
  </svg>
);

interface WithTTSProps {
  contentText?: string;
  children?: ReactNode;
  ttsOptions?: UseTextToSpeechOptions;
}

const extractTextFromChildren = (children: ReactNode): string | null => {
  if (typeof children === 'string') {
    return children;
  }
  if (Array.isArray(children)) {
    return children
      .map(child => extractTextFromChildren(child))
      .filter(text => text !== null)
      .join('');
  }
  // Add more sophisticated extraction logic if needed for complex children
  return null;
};

export function withTTS<P extends object>(WrappedComponent: ComponentType<P>) {
  const ComponentWithTTS = (props: P & WithTTSProps) => {
    const { contentText, children, ttsOptions, ...restProps } = props;
    const { speak, isPlaying, browserSupportsSpeechSynthesis } = useTextToSpeech(ttsOptions);

    let speakableText: string | null = null;

    if (contentText) {
      speakableText = contentText;
    } else if (children) {
      speakableText = extractTextFromChildren(children);
    }

    const hasChineseText = speakableText && containsChinese(speakableText);

    if (hasChineseText && browserSupportsSpeechSynthesis) {
      return (
        <>
          <WrappedComponent {...(restProps as P)} />
          <button
            onClick={() => speakableText && speak(speakableText, ttsOptions?.defaultLang, ttsOptions?.defaultRate, ttsOptions?.defaultPitch, ttsOptions?.defaultVoiceName)}
            disabled={isPlaying}
            aria-label="Speak text"
            style={{ marginLeft: '8px', cursor: 'pointer', border: 'none', background: 'transparent', display: 'inline-flex', alignItems: 'center', justifyContent: 'center' }}
          >
            <SpeakerIcon />
          </button>
        </>
      );
    }

    return <WrappedComponent {...(restProps as P)} />;
  };

  ComponentWithTTS.displayName = `WithTTS(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return ComponentWithTTS;
}

/*
Example Usage:

// 1. Define your component
interface MyDisplayProps {
  chineseText: string;
  id: number;
  // other props...
}
const MyContentDisplayComponent: React.FC<MyDisplayProps> = (props) => {
  return <div>ID: {props.id} - {props.chineseText}</div>;
};

// 2. Wrap it with the HOC
const MyContentDisplayWithTTS = withTTS(MyContentDisplayComponent);

// 3. Use the enhanced component
// Example 1: Using a specific prop 'contentText' (if your component doesn't naturally have a text prop)
// const App = () => {
//   return <MyContentDisplayWithTTS id={1} chineseText="你好世界" contentText="你好世界" ttsOptions={{ defaultLang: 'zh-CN', defaultRate: 0.8 }} />;
// };

// Example 2: Extracting from 'children'
// interface MySimpleDivProps {
//   style?: React.CSSProperties;
//   children: React.ReactNode;
// }
// const MySimpleDiv: React.FC<MySimpleDivProps> = ({ children, style }) => {
//   return <div style={style}>{children}</div>;
// };
// const MySimpleDivWithTTS = withTTS(MySimpleDiv);
//
// const App = () => {
//   return (
//     <MySimpleDivWithTTS style={{ padding: '10px', border: '1px solid #ccc' }} ttsOptions={{ defaultLang: 'zh-TW' }}>
//       你好，這是從子節點提取的文本。
//     </MySimpleDivWithTTS>
//   );
// };

// Example 3: Component already has a prop that can be used by withTTS (e.g. 'chineseText' if we modify HOC to look for it or pass it as contentText)
// This example assumes 'chineseText' is passed as 'contentText' to the HOC or the HOC is modified to check for 'chineseText'
// const App = () => {
//   return <MyContentDisplayWithTTS id={2} chineseText="再见朋友" contentText="再见朋友" />;
// };

// If MyContentDisplayComponent's main text was in its children:
// const MyContentDisplayAsChildren: React.FC<{ id: number, children: React.ReactNode }> = ({ id, children }) => {
//   return <div>ID: {id} - {children}</div>;
// };
// const MyContentDisplayAsChildrenWithTTS = withTTS(MyContentDisplayAsChildren);
//
// const App = () => {
//   return <MyContentDisplayAsChildrenWithTTS id={3}>你好儿童</MyContentDisplayAsChildrenWithTTS>;
// };

*/