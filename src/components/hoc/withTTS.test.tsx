import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { withTTS } from './withTTS';
import useTextToSpeech, { UseTextToSpeechOptions } from '@/hooks/useTextToSpeech';
import { containsChinese } from '@/lib/utils';

// Mock dependencies
jest.mock('@/hooks/useTextToSpeech');
jest.mock('@/lib/utils');

const mockUseTextToSpeech = useTextToSpeech as jest.MockedFunction<typeof useTextToSpeech>;
const mockContainsChinese = containsChinese as jest.MockedFunction<typeof containsChinese>;

// A simple component to wrap
interface TestComponentProps {
  id: number;
  text?: string;
  children?: React.ReactNode;
}
const TestComponent: React.FC<TestComponentProps> = ({ id, text, children }) => (
  <div data-testid="wrapped-component">
    <span data-testid="prop-id">{id}</span>
    {text && <span data-testid="prop-text">{text}</span>}
    {children && <div data-testid="prop-children">{children}</div>}
  </div>
);

describe('withTTS HOC', () => {
  let mockSpeak: jest.Mock;
  let mockIsPlaying: boolean;
  let mockBrowserSupportsSpeechSynthesis: boolean;

  beforeEach(() => {
    mockSpeak = jest.fn();
    mockIsPlaying = false;
    mockBrowserSupportsSpeechSynthesis = true;

    mockUseTextToSpeech.mockReturnValue({
      speak: mockSpeak,
      isPlaying: mockIsPlaying,
      browserSupportsSpeechSynthesis: mockBrowserSupportsSpeechSynthesis,
      error: null,
      cancel: jest.fn(),
      clearError: jest.fn(),
      getAvailableVoices: jest.fn().mockReturnValue([]),
    });
    mockContainsChinese.mockReturnValue(false); // Default to no Chinese text
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the WrappedComponent with its props', () => {
    const WrappedWithTTS = withTTS(TestComponent);
    render(<WrappedWithTTS id={123} text="Hello" />);

    expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
    expect(screen.getByTestId('prop-id')).toHaveTextContent('123');
    expect(screen.getByTestId('prop-text')).toHaveTextContent('Hello');
  });

  it('should not render speaker icon if browserSupportsSpeechSynthesis is false', () => {
    mockUseTextToSpeech.mockReturnValueOnce({
      ...mockUseTextToSpeech(),
      browserSupportsSpeechSynthesis: false,
    });
    mockContainsChinese.mockReturnValueOnce(true); // Has Chinese text
    const WrappedWithTTS = withTTS(TestComponent);
    render(<WrappedWithTTS id={1} contentText="你好" />);
    expect(screen.queryByRole('button', { name: /speak text/i })).not.toBeInTheDocument();
  });

  describe('Text Extraction and Speaker Icon Rendering', () => {
    it('should extract text from contentText prop and render speaker icon if Chinese', () => {
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={1} contentText="你好世界" />);

      expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /speak text/i })).toBeInTheDocument();
    });

    it('should extract text from children prop (string) and render speaker icon if Chinese', () => {
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={2}>你好，孩子们</WrappedWithTTS>);

      expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
      expect(screen.getByTestId('prop-children')).toHaveTextContent('你好，孩子们');
      expect(screen.getByRole('button', { name: /speak text/i })).toBeInTheDocument();
    });

    it('should extract text from children prop (array of strings/nodes) and render speaker icon if Chinese', () => {
        mockContainsChinese.mockReturnValueOnce(true);
        const WrappedWithTTS = withTTS(TestComponent);
        render(<WrappedWithTTS id={3}>你好<span> </span>世界</WrappedWithTTS>);
  
        expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
        expect(screen.getByTestId('prop-children')).toHaveTextContent('你好 世界');
        expect(screen.getByRole('button', { name: /speak text/i })).toBeInTheDocument();
      });

    it('should not render speaker icon if text is present but not Chinese', () => {
      mockContainsChinese.mockReturnValueOnce(false);
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={3} contentText="Hello World" />);

      expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /speak text/i })).not.toBeInTheDocument();
    });

    it('should not render speaker icon if no speakable text is found', () => {
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={4} />); // No contentText or children

      expect(screen.getByTestId('wrapped-component')).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /speak text/i })).not.toBeInTheDocument();
    });
  });

  describe('Speaker Icon Interaction', () => {
    it('should call speak function with extracted text when icon is clicked (from contentText)', () => {
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      const testText = "你好 contentText";
      render(<WrappedWithTTS id={5} contentText={testText} />);

      const speakerButton = screen.getByRole('button', { name: /speak text/i });
      fireEvent.click(speakerButton);

      expect(mockSpeak).toHaveBeenCalledTimes(1);
      expect(mockSpeak).toHaveBeenCalledWith(testText, undefined, undefined, undefined, undefined);
    });

    it('should call speak function with extracted text when icon is clicked (from children)', () => {
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      const testText = "你好 children";
      render(<WrappedWithTTS id={6}>{testText}</WrappedWithTTS>);

      const speakerButton = screen.getByRole('button', { name: /speak text/i });
      fireEvent.click(speakerButton);

      expect(mockSpeak).toHaveBeenCalledTimes(1);
      expect(mockSpeak).toHaveBeenCalledWith(testText, undefined, undefined, undefined, undefined);
    });

    it('should disable speaker icon when isPlaying is true', () => {
      mockContainsChinese.mockReturnValueOnce(true);
      mockUseTextToSpeech.mockReturnValueOnce({
        ...mockUseTextToSpeech(),
        speak: mockSpeak,
        isPlaying: true, // Simulate playing state
        browserSupportsSpeechSynthesis: true,
      });
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={7} contentText="你好" />);

      const speakerButton = screen.getByRole('button', { name: /speak text/i });
      expect(speakerButton).toBeDisabled();
    });
  });

  describe('ttsOptions Prop', () => {
    it('should pass ttsOptions to useTextToSpeech hook', () => {
      const ttsOptions: UseTextToSpeechOptions = {
        defaultLang: 'zh-HK',
        defaultRate: 0.7,
        defaultPitch: 1.2,
        defaultVoiceName: 'TestVoice'
      };
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      render(<WrappedWithTTS id={8} contentText="你好" ttsOptions={ttsOptions} />);

      expect(mockUseTextToSpeech).toHaveBeenCalledWith(ttsOptions);
    });

    it('should call speak function with ttsOptions when icon is clicked', () => {
      const ttsOptions: UseTextToSpeechOptions = {
        defaultLang: 'zh-CN',
        defaultRate: 0.9,
        defaultPitch: 1.1,
        defaultVoiceName: 'AnotherVoice'
      };
      mockContainsChinese.mockReturnValueOnce(true);
      const WrappedWithTTS = withTTS(TestComponent);
      const testText = "你好 options";
      render(<WrappedWithTTS id={9} contentText={testText} ttsOptions={ttsOptions} />);

      const speakerButton = screen.getByRole('button', { name: /speak text/i });
      fireEvent.click(speakerButton);

      expect(mockSpeak).toHaveBeenCalledTimes(1);
      expect(mockSpeak).toHaveBeenCalledWith(
        testText,
        ttsOptions.defaultLang,
        ttsOptions.defaultRate,
        ttsOptions.defaultPitch,
        ttsOptions.defaultVoiceName
      );
    });
  });

  it('should set a displayName for the HOC', () => {
    const WrappedWithTTS = withTTS(TestComponent);
    expect(WrappedWithTTS.displayName).toBe('WithTTS(TestComponent)');

    const NamelessComponent = () => <div>Nameless</div>;
    const WrappedNameless = withTTS(NamelessComponent);
    expect(WrappedNameless.displayName).toBe('WithTTS(Component)');
  });
});