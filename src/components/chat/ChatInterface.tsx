'use client';

import React, { useState } from 'react';
import { ChatDialogueRequest, ChatProgressSummary } from '@/types/chat';
import TopicSelector from './TopicSelector';
import ChatSessionManager from './ChatSessionManager';
import ProgressVisualization from './ProgressVisualization';
import { usePerformanceTracking } from '@/hooks/usePerformanceTracking';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  ArrowLeft,
  RotateCcw,
  TrendingUp,
  Target,
  BarChart3
} from 'lucide-react';

interface ChatInterfaceProps {
  userId: string;
  onExit: () => void;
}

type ChatState = 'topic-selection' | 'in-session' | 'session-complete';

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  userId,
  onExit
}) => {
  const [chatState, setChatState] = useState<ChatState>('topic-selection');
  const [currentDialogueRequest, setCurrentDialogueRequest] = useState<ChatDialogueRequest | null>(null);
  const [sessionSummary, setSessionSummary] = useState<ChatProgressSummary | null>(null);

  const {
    adaptiveProfile,
    loadAdaptiveProfile
  } = usePerformanceTracking({ userId });

  const handleTopicSelect = (request: ChatDialogueRequest) => {
    setCurrentDialogueRequest(request);
    setChatState('in-session');
  };

  const handleSessionComplete = async (summary: ChatProgressSummary) => {
    setSessionSummary(summary);
    setChatState('session-complete');
    
    // Reload adaptive profile to reflect new data
    await loadAdaptiveProfile();
  };

  const handleSessionExit = () => {
    setChatState('topic-selection');
    setCurrentDialogueRequest(null);
    setSessionSummary(null);
  };

  const handleStartNewSession = () => {
    setChatState('topic-selection');
    setCurrentDialogueRequest(null);
    setSessionSummary(null);
  };

  const renderHeader = () => {
    return (
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <MessageSquare className="h-6 w-6 text-blue-600" />
              <div>
                <CardTitle>Chinese Conversation Practice</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  {chatState === 'topic-selection' && 'Choose a topic to start practicing'}
                  {chatState === 'in-session' && `Practicing: ${currentDialogueRequest?.topic}`}
                  {chatState === 'session-complete' && 'Session completed!'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {adaptiveProfile && (
                <div className="flex items-center gap-2 text-sm">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    {adaptiveProfile.difficultyLevel}
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {Math.round(adaptiveProfile.averageScore)}%
                  </Badge>
                </div>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('/chat/dashboard', '_blank')}
                className="flex items-center gap-1"
              >
                <BarChart3 className="h-4 w-4" />
                Dashboard
              </Button>

              {chatState !== 'topic-selection' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSessionExit}
                  className="flex items-center gap-1"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={onExit}
              >
                Exit
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {renderHeader()}
        
        {chatState === 'topic-selection' && (
          <div className="space-y-6">
            <TopicSelector
              userId={userId}
              onTopicSelect={handleTopicSelect}
              onCancel={onExit}
            />
            
            {/* Show user's learning profile */}
            {adaptiveProfile && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Your Learning Progress</h3>
                <ProgressVisualization 
                  adaptiveProfile={adaptiveProfile}
                  showDetailed={false}
                />
              </div>
            )}
          </div>
        )}

        {chatState === 'in-session' && currentDialogueRequest && (
          <ChatSessionManager
            dialogueRequest={currentDialogueRequest}
            onSessionComplete={handleSessionComplete}
            onExit={handleSessionExit}
          />
        )}

        {chatState === 'session-complete' && sessionSummary && (
          <div className="space-y-6">
            <ProgressVisualization 
              sessionSummary={sessionSummary}
              adaptiveProfile={adaptiveProfile}
              showDetailed={true}
            />
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <h2 className="text-2xl font-bold text-green-600">
                    Congratulations! 🎉
                  </h2>
                  <p className="text-gray-600">
                    You&apos;ve completed your conversation practice session. 
                    Your progress has been saved and will help personalize future sessions.
                  </p>
                  
                  <div className="flex gap-3 justify-center">
                    <Button 
                      onClick={handleStartNewSession}
                      className="flex items-center gap-2"
                    >
                      <RotateCcw className="h-4 w-4" />
                      Start New Session
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={onExit}
                    >
                      Return to Main Menu
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
