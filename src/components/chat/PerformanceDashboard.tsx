'use client';

import React, { useState, useEffect } from 'react';
import { ChatProgressSummary, AdaptiveLearningProfile } from '@/types/chat';
import { usePerformanceTracking } from '@/hooks/usePerformanceTracking';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Clock, 
  Award, 
  Brain,
  Calendar,
  Zap,
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

interface PerformanceDashboardProps {
  userId: string;
  onClose?: () => void;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  userId,
  onClose
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'all'>('month');
  
  const {
    adaptiveProfile,
    sessionSummaries,
    isLoading,
    error,
    loadAdaptiveProfile,
    loadSessionSummaries
  } = usePerformanceTracking({ userId });

  useEffect(() => {
    loadAdaptiveProfile();
    loadSessionSummaries();
  }, [loadAdaptiveProfile, loadSessionSummaries]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-blue-500';
    if (score >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getRecentSessions = () => {
    const now = Date.now();
    const timeframes = {
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
      all: Infinity
    };
    
    return sessionSummaries.filter(session => {
      // For demo purposes, we'll show all sessions since we don't have real timestamps
      return true;
    }).slice(0, 10);
  };

  const calculateStats = () => {
    const recentSessions = getRecentSessions();
    
    if (recentSessions.length === 0) {
      return {
        totalSessions: 0,
        averageScore: 0,
        totalTime: 0,
        improvementTrend: 0,
        completionRate: 0
      };
    }

    const totalSessions = recentSessions.length;
    const averageScore = recentSessions.reduce((sum, s) => sum + s.overallScore, 0) / totalSessions;
    const totalTime = recentSessions.reduce((sum, s) => sum + s.timeSpent, 0);
    const completionRate = recentSessions.reduce((sum, s) => 
      sum + (s.completedSegments / s.totalSegments), 0) / totalSessions * 100;

    // Calculate improvement trend (compare first half vs second half)
    const midpoint = Math.floor(totalSessions / 2);
    const firstHalf = recentSessions.slice(0, midpoint);
    const secondHalf = recentSessions.slice(midpoint);
    
    const firstHalfAvg = firstHalf.length > 0 
      ? firstHalf.reduce((sum, s) => sum + s.overallScore, 0) / firstHalf.length 
      : 0;
    const secondHalfAvg = secondHalf.length > 0 
      ? secondHalf.reduce((sum, s) => sum + s.overallScore, 0) / secondHalf.length 
      : 0;
    
    const improvementTrend = secondHalfAvg - firstHalfAvg;

    return {
      totalSessions,
      averageScore: Math.round(averageScore),
      totalTime,
      improvementTrend: Math.round(improvementTrend),
      completionRate: Math.round(completionRate)
    };
  };

  const stats = calculateStats();

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading your performance data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Data</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => {
              loadAdaptiveProfile();
              loadSessionSummaries();
            }}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Dashboard</h1>
          <p className="text-gray-600 mt-1">Track your Chinese conversation learning progress</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-4 w-4" />
            <select 
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as any)}
              className="border rounded px-2 py-1"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalSessions}</div>
            <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
              <BarChart3 className="h-3 w-3" />
              Sessions
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(stats.averageScore)}`}>
              {stats.averageScore}%
            </div>
            <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
              <Target className="h-3 w-3" />
              Avg Score
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatTime(stats.totalTime)}
            </div>
            <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
              <Clock className="h-3 w-3" />
              Total Time
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className={`text-2xl font-bold ${
              stats.improvementTrend >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.improvementTrend >= 0 ? '+' : ''}{stats.improvementTrend}%
            </div>
            <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
              <TrendingUp className="h-3 w-3" />
              Trend
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.completionRate}%</div>
            <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Completion
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="characters">Characters</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {adaptiveProfile && (
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    Learning Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Difficulty Level</span>
                    <Badge>{adaptiveProfile.difficultyLevel}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Learning Velocity</span>
                    <span className="font-medium">{adaptiveProfile.learningVelocity.toFixed(1)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Sessions</span>
                    <span className="font-medium">{adaptiveProfile.totalSessions}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-yellow-600" />
                    Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {stats.totalSessions >= 10 && (
                    <Badge variant="secondary" className="mr-2 mb-2">
                      🎯 Consistent Learner
                    </Badge>
                  )}
                  {stats.averageScore >= 80 && (
                    <Badge variant="secondary" className="mr-2 mb-2">
                      ⭐ High Performer
                    </Badge>
                  )}
                  {stats.improvementTrend > 10 && (
                    <Badge variant="secondary" className="mr-2 mb-2">
                      📈 Rapid Improver
                    </Badge>
                  )}
                  {adaptiveProfile && adaptiveProfile.problematicCharacters.filter(c => c.masteryScore >= 80).length >= 5 && (
                    <Badge variant="secondary" className="mr-2 mb-2">
                      🔤 Character Master
                    </Badge>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              {getRecentSessions().length === 0 ? (
                <p className="text-gray-500 text-center py-8">No sessions found for the selected timeframe.</p>
              ) : (
                <div className="space-y-3">
                  {getRecentSessions().map((session, index) => (
                    <div key={session.sessionId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{session.topic}</h4>
                        <p className="text-sm text-gray-600">
                          {session.completedSegments}/{session.totalSegments} segments • {formatTime(session.timeSpent)}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${getScoreColor(session.overallScore)}`}>
                          {session.overallScore}%
                        </div>
                        <Progress 
                          value={session.overallScore} 
                          className="w-20 h-2 mt-1"
                          indicatorClassName={getProgressColor(session.overallScore)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="characters" className="space-y-4">
          {adaptiveProfile && adaptiveProfile.problematicCharacters.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Character Mastery Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {adaptiveProfile.problematicCharacters
                    .sort((a, b) => a.masteryScore - b.masteryScore)
                    .slice(0, 10)
                    .map((char, index) => (
                      <div key={index} className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <span className="text-xl font-bold">{char.character}</span>
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Mastery: {char.masteryScore}%</span>
                            <span>{char.mistakeCount} mistakes</span>
                          </div>
                          <Progress 
                            value={char.masteryScore} 
                            className="h-2"
                            indicatorClassName={getProgressColor(char.masteryScore)}
                          />
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`${
                            char.improvementRate > 0.7 ? 'text-green-600' : 
                            char.improvementRate > 0.3 ? 'text-yellow-600' : 'text-red-600'
                          }`}
                        >
                          {char.improvementRate > 0.7 ? '↗ Improving' : 
                           char.improvementRate > 0.3 ? '→ Stable' : '↘ Struggling'}
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Character Data Yet</h3>
                <p className="text-gray-500">Complete more conversation sessions to see character analysis.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-green-600">Strengths</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {stats.averageScore >= 80 && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Consistent high performance</span>
                    </li>
                  )}
                  {stats.completionRate >= 90 && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Excellent session completion rate</span>
                    </li>
                  )}
                  {stats.improvementTrend > 5 && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Strong improvement trend</span>
                    </li>
                  )}
                  {stats.totalSessions >= 10 && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Regular practice habit</span>
                    </li>
                  )}
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-blue-600">Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {stats.averageScore < 70 && (
                    <li className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span>Focus on easier topics to build confidence</span>
                    </li>
                  )}
                  {stats.totalSessions < 5 && (
                    <li className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span>Practice more regularly for better results</span>
                    </li>
                  )}
                  {adaptiveProfile && adaptiveProfile.problematicCharacters.length >= 5 && (
                    <li className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span>Review problematic characters with flashcards</span>
                    </li>
                  )}
                  {stats.improvementTrend < 0 && (
                    <li className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span>Try shorter sessions to maintain focus</span>
                    </li>
                  )}
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceDashboard;
