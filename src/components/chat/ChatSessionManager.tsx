'use client';

import React, { useState } from 'react';
import { useChatSession } from '@/hooks/useChatSession';
import { ChatDialogueRequest, ChatProgressSummary } from '@/types/chat';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Clock, Target, MessageSquare } from 'lucide-react';
import TranslationFeedback from './TranslationFeedback';

interface ChatSessionManagerProps {
  dialogueRequest: ChatDialogueRequest;
  onSessionComplete: (summary: ChatProgressSummary) => void;
  onExit: () => void;
}

const ChatSessionManager: React.FC<ChatSessionManagerProps> = ({
  dialogueRequest,
  onSessionComplete,
  onExit
}) => {
  const [userInput, setUserInput] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);
  const [currentEvaluation, setCurrentEvaluation] = useState(null);

  const {
    sessionState,
    isLoading,
    error,
    startSession,
    submitTranslation,
    nextSegment,
    currentSegment,
    progress,
    sessionStats,
    sessionComplete,
    hasActiveSession
  } = useChatSession({
    onSessionComplete: (sessionId, performance) => {
      // Create progress summary
      const summary: ChatProgressSummary = {
        sessionId,
        topic: sessionState?.dialogue.topic || '',
        totalSegments: sessionState?.dialogue.segments.length || 0,
        completedSegments: performance.length,
        overallScore: performance.length > 0 
          ? Math.round(performance.reduce((sum, p) => sum + p.evaluation.score, 0) / performance.length)
          : 0,
        timeSpent: sessionState?.totalTimeSpent || 0,
        charactersLearned: [], // TODO: Extract from performance
        charactersMistaken: [], // TODO: Extract from performance
        improvementAreas: [], // TODO: Extract from evaluations
        strengths: [] // TODO: Extract from evaluations
      };
      onSessionComplete(summary);
    },
    onError: (error) => {
      console.error('Chat session error:', error);
    }
  });

  // Initialize session on mount
  React.useEffect(() => {
    if (!hasActiveSession) {
      startSession(dialogueRequest);
    }
  }, [dialogueRequest, hasActiveSession, startSession]);

  const handleSubmitTranslation = async () => {
    if (!userInput.trim() || !currentSegment) return;

    try {
      const evaluation = await submitTranslation(userInput);
      setCurrentEvaluation(evaluation);
      setShowFeedback(true);
    } catch (error) {
      console.error('Failed to submit translation:', error);
    }
  };

  const handleContinue = () => {
    const hasMore = nextSegment();
    setUserInput('');
    setShowFeedback(false);
    setCurrentEvaluation(null);
    
    if (!hasMore) {
      // Session is complete, handled by useChatSession hook
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!showFeedback) {
        handleSubmitTranslation();
      } else {
        handleContinue();
      }
    }
  };

  if (error) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-red-600">Session Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 mb-4">{error}</p>
          <div className="flex gap-2">
            <Button onClick={() => startSession(dialogueRequest)}>
              Retry
            </Button>
            <Button variant="outline" onClick={onExit}>
              Exit
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading && !hasActiveSession) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Generating your conversation...</p>
        </CardContent>
      </Card>
    );
  }

  if (sessionComplete) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Session Complete! 🎉</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 mb-4">
            Great job completing the conversation practice!
          </p>
          {sessionStats && (
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{sessionStats.averageScore}</div>
                <div className="text-sm text-gray-600">Average Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{sessionStats.accuracy}%</div>
                <div className="text-sm text-gray-600">Accuracy</div>
              </div>
            </div>
          )}
          <Button onClick={onExit} className="w-full">
            View Detailed Results
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!currentSegment) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <p className="text-gray-600">No dialogue segment available.</p>
          <Button onClick={onExit} className="mt-4">
            Exit
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      {/* Progress Header */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <span className="font-medium">{sessionState?.dialogue.topic}</span>
              <Badge variant="outline">{sessionState?.dialogue.difficulty}</Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              {sessionStats && (
                <>
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4" />
                    <span>{sessionStats.averageScore}/100</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{Math.floor(sessionStats.totalTime / 60)}:{(sessionStats.totalTime % 60).toString().padStart(2, '0')}</span>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Progress value={progress.percentage} className="flex-1" />
            <span className="text-sm text-gray-600">
              {progress.current}/{progress.total}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Current Dialogue Segment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="text-blue-600">{currentSegment.speaker}:</span>
            <Badge variant="outline">Segment {progress.current + 1}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-lg text-gray-800">{currentSegment.englishText}</p>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Translate to Chinese:
              </label>
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type your Chinese translation here..."
                className="w-full p-3 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                disabled={showFeedback || isLoading}
              />
            </div>

            {!showFeedback && (
              <Button 
                onClick={handleSubmitTranslation}
                disabled={!userInput.trim() || isLoading}
                className="w-full"
              >
                {isLoading ? 'Evaluating...' : 'Submit Translation'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Feedback */}
      {showFeedback && currentEvaluation && (
        <div className="space-y-4">
          <TranslationFeedback
            evaluation={currentEvaluation}
            userTranslation={userInput}
            englishText={currentSegment.englishText}
          />
          <Button onClick={handleContinue} className="w-full">
            Continue to Next Segment
          </Button>
        </div>
      )}

      {/* Exit Button */}
      <div className="flex justify-center">
        <Button variant="outline" onClick={onExit}>
          Exit Session
        </Button>
      </div>
    </div>
  );
};

export default ChatSessionManager;
