'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useChatSession } from '@/hooks/useChatSession';
import { ChatDialogueRequest, ChatProgressSummary } from '@/types/chat';
import { Button } from '@/components/ui/button';
import { Send, Loader2 } from 'lucide-react';
import ChatBubble from './ChatBubble';
import EvaluationPanel from './EvaluationPanel';

interface ChatSessionManagerProps {
  dialogueRequest: ChatDialogueRequest;
  onSessionComplete: (summary: ChatProgressSummary) => void;
  onExit: () => void;
}

const ChatSessionManager: React.FC<ChatSessionManagerProps> = ({
  dialogueRequest,
  onSessionComplete,
  onExit
}) => {
  const [userInput, setUserInput] = useState('');
  const [revealedSegments, setRevealedSegments] = useState(1); // Start with first segment revealed
  const [currentEvaluation, setCurrentEvaluation] = useState(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const {
    sessionState,
    isLoading,
    error,
    startSession,
    submitTranslation,
    nextSegment,
    currentSegment,
    progress,
    sessionStats,
    sessionComplete,
    hasActiveSession
  } = useChatSession({
    onSessionComplete: (sessionId, performance) => {
      // Create progress summary
      const summary: ChatProgressSummary = {
        sessionId,
        topic: sessionState?.dialogue.topic || '',
        totalSegments: sessionState?.dialogue.segments.length || 0,
        completedSegments: performance.length,
        overallScore: performance.length > 0
          ? Math.round(performance.reduce((sum, p) => sum + p.evaluation.score, 0) / performance.length)
          : 0,
        timeSpent: sessionState?.totalTimeSpent || 0,
        charactersLearned: [], // TODO: Extract from performance
        charactersMistaken: [], // TODO: Extract from performance
        improvementAreas: [], // TODO: Extract from evaluations
        strengths: [] // TODO: Extract from evaluations
      };
      onSessionComplete(summary);
    },
    onError: (error) => {
      console.error('Chat session error:', error);
    }
  });

  // Initialize session on mount
  React.useEffect(() => {
    if (!hasActiveSession) {
      startSession(dialogueRequest);
    }
  }, [dialogueRequest, hasActiveSession, startSession]);

  // Auto-scroll to bottom when new segments are revealed
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [revealedSegments, currentEvaluation]);

  const handleSubmitTranslation = async () => {
    if (!userInput.trim() || !currentSegment) return;

    try {
      const evaluation = await submitTranslation(userInput);
      setCurrentEvaluation(evaluation);

      // Reveal next segment after a short delay
      setTimeout(() => {
        setRevealedSegments(prev => prev + 1);
        setUserInput('');
        setCurrentEvaluation(null);
        nextSegment();
      }, 3000); // Show evaluation for 3 seconds

    } catch (error) {
      console.error('Failed to submit translation:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!currentEvaluation) {
        handleSubmitTranslation();
      }
    }
  };

  // Error state
  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
          <div className="text-red-600 text-xl font-semibold mb-4">Session Error</div>
          <p className="text-gray-700 mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={() => startSession(dialogueRequest)}>
              Retry
            </Button>
            <Button variant="outline" onClick={onExit}>
              Exit
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading && !hasActiveSession) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Generating your conversation...</p>
        </div>
      </div>
    );
  }

  // Session complete state
  if (sessionComplete) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
          <div className="text-2xl font-bold text-green-600 mb-4">Session Complete! 🎉</div>
          <p className="text-gray-700 mb-4">
            Great job completing the conversation practice!
          </p>
          {sessionStats && (
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{sessionStats.averageScore}</div>
                <div className="text-sm text-gray-600">Average Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{sessionStats.accuracy}%</div>
                <div className="text-sm text-gray-600">Accuracy</div>
              </div>
            </div>
          )}
          <Button onClick={onExit} className="w-full">
            View Detailed Results
          </Button>
        </div>
      </div>
    );
  }

  // Main chat interface with split-screen design
  return (
    <div className="h-screen flex bg-gray-50">
      {/* Left Panel - Chat Interface (70%) */}
      <div className="flex-1 flex flex-col" style={{ width: '70%' }}>
        {/* Chat Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                {sessionState?.dialogue.topic?.charAt(0) || 'C'}
              </div>
              <div>
                <h2 className="font-semibold text-gray-900">{sessionState?.dialogue.topic}</h2>
                <p className="text-sm text-gray-500">
                  {sessionState?.dialogue.speakers?.join(', ')} • {sessionState?.dialogue.difficulty}
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={onExit}>
              Exit
            </Button>
          </div>
        </div>

        {/* Chat Messages Area */}
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-blue-50 to-white"
        >
          {sessionState?.dialogue.segments.slice(0, revealedSegments).map((segment, index) => {
            const speakerIndex = sessionState.dialogue.speakers.indexOf(segment.speaker);
            return (
              <ChatBubble
                key={index}
                segment={segment}
                speakerIndex={speakerIndex}
                segmentNumber={index + 1}
                isCurrentSegment={index === progress.current}
                isRevealed={index < revealedSegments}
                showTranslation={currentEvaluation && index === progress.current}
              />
            );
          })}

          {/* Show next segment placeholder if not revealed yet */}
          {revealedSegments < (sessionState?.dialogue.segments.length || 0) && (
            <div className="flex justify-center my-8">
              <div className="bg-gray-100 rounded-lg px-6 py-3 text-gray-500 text-sm border-2 border-dashed border-gray-300">
                Complete current translation to continue...
              </div>
            </div>
          )}
        </div>

        {/* Translation Input Area */}
        <div className="bg-white border-t border-gray-200 p-4">
          {currentSegment && !currentEvaluation && (
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                Translate: <span className="font-medium">"{currentSegment.englishText}"</span>
              </div>
              <div className="flex gap-2">
                <textarea
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder="Type your Chinese translation here..."
                  className="flex-1 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={2}
                  disabled={isLoading}
                />
                <Button
                  onClick={handleSubmitTranslation}
                  disabled={!userInput.trim() || isLoading}
                  size="lg"
                  className="px-6"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}

          {currentEvaluation && (
            <div className="text-center py-4">
              <div className="text-sm text-gray-600 mb-2">
                Translation submitted! Next segment will appear shortly...
              </div>
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Evaluation Panel (30%) */}
      <div style={{ width: '30%' }}>
        <EvaluationPanel
          currentScore={sessionStats?.averageScore || 0}
          totalSegments={sessionState?.dialogue.segments.length || 0}
          completedSegments={progress.current}
          timeSpent={sessionState?.totalTimeSpent || 0}
          accuracy={sessionStats?.accuracy || 0}
          recentEvaluation={currentEvaluation}
          focusCharacters={dialogueRequest.targetCharacters}
          sessionStats={sessionStats}
        />
      </div>
    </div>
  );
};

export default ChatSessionManager;
