'use client';

import React, { useState } from 'react';
import { TranslationEvaluation, CharacterMistakeAnalysis } from '@/types/chat';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Lightbulb, BookOpen } from 'lucide-react';
import TextWithTTS from '@/components/ui/TextWithTTS';

interface TranslationEvaluatorProps {
  evaluation: TranslationEvaluation;
  onContinue: () => void;
  showCorrectAnswer: boolean;
  userTranslation?: string;
  englishText?: string;
}

const TranslationEvaluator: React.FC<TranslationEvaluatorProps> = ({
  evaluation,
  onContinue,
  showCorrectAnswer,
  userTranslation,
  englishText
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 70) return <CheckCircle className="h-5 w-5 text-blue-600" />;
    if (score >= 50) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <XCircle className="h-5 w-5 text-red-600" />;
  };

  const getMistakeTypeColor = (type: string) => {
    switch (type) {
      case 'substitution': return 'bg-red-100 text-red-800';
      case 'omission': return 'bg-orange-100 text-orange-800';
      case 'addition': return 'bg-yellow-100 text-yellow-800';
      case 'tone': return 'bg-purple-100 text-purple-800';
      case 'context': return 'bg-blue-100 text-blue-800';
      case 'grammar': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'major': return 'border-red-500 bg-red-50';
      case 'moderate': return 'border-yellow-500 bg-yellow-50';
      case 'minor': return 'border-blue-500 bg-blue-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="space-y-4">
      {/* Score and Overall Feedback */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getScoreIcon(evaluation.score)}
            <span className={`text-2xl font-bold ${getScoreColor(evaluation.score)}`}>
              {evaluation.score}/100
            </span>
            <Badge variant={evaluation.isCorrect ? "default" : "destructive"}>
              {evaluation.isCorrect ? "Correct" : "Needs Improvement"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 mb-4">{evaluation.feedback}</p>
          
          {/* Show translations */}
          {userTranslation && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-600 mb-1">Your Translation:</p>
              <div className="p-2 bg-blue-50 rounded border">
                <TextWithTTS text={userTranslation} lang="zh-CN" />
              </div>
            </div>
          )}
          
          {showCorrectAnswer && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-600 mb-1">Correct Translation:</p>
              <div className="p-2 bg-green-50 rounded border">
                <TextWithTTS text={evaluation.correctTranslation} lang="zh-CN" />
              </div>
            </div>
          )}

          {englishText && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-600 mb-1">Original English:</p>
              <div className="p-2 bg-gray-50 rounded border">
                <p className="text-gray-800">{englishText}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Character Mistakes Analysis */}
      {evaluation.mistakeAnalysis && evaluation.mistakeAnalysis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Character Analysis ({evaluation.mistakeAnalysis.length} issues)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {evaluation.mistakeAnalysis.map((mistake, index) => (
                <div 
                  key={index} 
                  className={`p-3 rounded-lg border-2 ${getSeverityColor(mistake.severity)}`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getMistakeTypeColor(mistake.mistakeType)}>
                      {mistake.mistakeType}
                    </Badge>
                    <Badge variant="outline">
                      {mistake.severity} severity
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-2 text-sm">
                    <div>
                      <span className="font-medium text-red-600">You wrote: </span>
                      <span className="font-mono bg-red-100 px-1 rounded">
                        {mistake.character}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-green-600">Should be: </span>
                      <span className="font-mono bg-green-100 px-1 rounded">
                        {mistake.expectedCharacter}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700 mb-2">{mistake.explanation}</p>
                  
                  {mistake.similarCharacters && mistake.similarCharacters.length > 0 && (
                    <div className="text-sm">
                      <span className="font-medium">Similar characters: </span>
                      {mistake.similarCharacters.map((char, i) => (
                        <span key={i} className="font-mono bg-gray-100 px-1 rounded mr-1">
                          {char}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {mistake.contextualUsage && (
                    <div className="text-sm mt-2">
                      <span className="font-medium">Context tip: </span>
                      <span className="text-gray-600">{mistake.contextualUsage}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggestions */}
      {evaluation.suggestions && evaluation.suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Suggestions for Improvement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {evaluation.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-gray-700">{suggestion}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Context Tips */}
      {evaluation.contextTips && evaluation.contextTips.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Context & Usage Tips
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {evaluation.contextTips.map((tip, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span className="text-gray-700">{tip}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2 pt-4">
        <Button onClick={onContinue} className="flex-1">
          Continue to Next Segment
        </Button>
        <Button 
          variant="outline" 
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? 'Hide' : 'Show'} Details
        </Button>
      </div>
    </div>
  );
};

export default TranslationEvaluator;
