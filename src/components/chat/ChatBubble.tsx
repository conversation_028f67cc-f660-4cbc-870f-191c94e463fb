'use client';

import React from 'react';
import { DialogueSegmentData } from '@/types/chat';
import { Badge } from '@/components/ui/badge';
import TextWithTTS from '@/components/ui/TextWithTTS';
import { Clock, Target } from 'lucide-react';

interface ChatBubbleProps {
  segment: DialogueSegmentData;
  speakerIndex: number;
  segmentNumber: number;
  isCurrentSegment?: boolean;
  isRevealed?: boolean;
  showTranslation?: boolean;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  segment,
  speakerIndex,
  segmentNumber,
  isCurrentSegment = false,
  isRevealed = true,
  showTranslation = false
}) => {
  // Define speaker colors
  const speakerColors = [
    'bg-blue-500 text-white', // Speaker 1 - Blue (like iMessage)
    'bg-gray-300 text-gray-800', // Speaker 2 - Gray
    'bg-green-500 text-white', // Speaker 3 - Green (like WhatsApp)
    'bg-purple-500 text-white', // Speaker 4 - Purple
  ];

  const bubbleColor = speakerColors[speakerIndex % speakerColors.length];
  const isRightAligned = speakerIndex % 2 === 0; // Alternate alignment

  if (!isRevealed) {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 rounded-lg px-4 py-2 text-gray-500 text-sm">
          Continue to reveal next message...
        </div>
      </div>
    );
  }

  return (
    <div className={`flex mb-4 ${isRightAligned ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md ${isRightAligned ? 'order-2' : 'order-1'}`}>
        {/* Speaker name and segment info */}
        <div className={`flex items-center gap-2 mb-1 ${isRightAligned ? 'justify-end' : 'justify-start'}`}>
          <span className="text-sm font-medium text-gray-600">{segment.speaker}</span>
          <Badge variant="outline" className="text-xs">
            #{segmentNumber}
          </Badge>
          {segment.difficulty && (
            <Badge variant="outline" className="text-xs">
              Level {segment.difficulty}
            </Badge>
          )}
        </div>

        {/* Chat bubble */}
        <div
          className={`
            relative px-4 py-3 rounded-2xl shadow-sm
            ${bubbleColor}
            ${isCurrentSegment ? 'ring-2 ring-blue-400 ring-opacity-50 animate-pulse' : ''}
            ${isRightAligned ? 'rounded-br-md' : 'rounded-bl-md'}
          `}
        >
          {/* Chinese text */}
          <div className="mb-2">
            <TextWithTTS 
              text={segment.chineseText} 
              lang="zh-CN" 
              className={`text-base font-medium ${
                speakerIndex % 2 === 1 ? 'text-gray-800' : 'text-white'
              }`}
            />
          </div>

          {/* Pinyin (if available) */}
          {segment.pinyin && (
            <div className={`text-sm opacity-75 mb-1 ${
              speakerIndex % 2 === 1 ? 'text-gray-600' : 'text-white'
            }`}>
              {segment.pinyin}
            </div>
          )}

          {/* English translation (conditionally shown) */}
          {showTranslation && (
            <div className={`text-sm opacity-90 border-t pt-2 mt-2 ${
              speakerIndex % 2 === 1 
                ? 'text-gray-700 border-gray-400' 
                : 'text-white border-white border-opacity-30'
            }`}>
              {segment.englishText}
            </div>
          )}

          {/* Target vocabulary and grammar indicators */}
          {(segment.targetVocabulary?.length || segment.targetGrammar?.length) && (
            <div className="flex flex-wrap gap-1 mt-2">
              {segment.targetVocabulary?.slice(0, 3).map((vocab, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="text-xs bg-white bg-opacity-20 text-white border-white border-opacity-30"
                >
                  {vocab}
                </Badge>
              ))}
              {segment.targetGrammar?.slice(0, 2).map((grammar, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="text-xs bg-white bg-opacity-10 text-white border-white border-opacity-30"
                >
                  <Target className="h-2 w-2 mr-1" />
                  {grammar}
                </Badge>
              ))}
            </div>
          )}

          {/* Current segment indicator */}
          {isCurrentSegment && (
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
              <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg">
                Translate this
              </div>
            </div>
          )}
        </div>

        {/* Timestamp */}
        <div className={`flex items-center gap-1 mt-1 text-xs text-gray-500 ${
          isRightAligned ? 'justify-end' : 'justify-start'
        }`}>
          <Clock className="h-3 w-3" />
          <span>Just now</span>
        </div>
      </div>
    </div>
  );
};

export default ChatBubble;
