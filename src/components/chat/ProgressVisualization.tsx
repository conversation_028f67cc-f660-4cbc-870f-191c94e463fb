'use client';

import React from 'react';
import { ChatProgressSummary, AdaptiveLearningProfile } from '@/types/chat';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Target, 
  Clock, 
  Award, 
  Brain, 
  Zap,
  CheckCircle,
  AlertTriangle,
  BarChart3
} from 'lucide-react';

interface ProgressVisualizationProps {
  sessionSummary?: ChatProgressSummary;
  adaptiveProfile?: AdaptiveLearningProfile | null;
  showDetailed?: boolean;
}

const ProgressVisualization: React.FC<ProgressVisualizationProps> = ({
  sessionSummary,
  adaptiveProfile,
  showDetailed = false
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-blue-500';
    if (score >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!sessionSummary && !adaptiveProfile) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">No progress data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Session Summary */}
      {sessionSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5 text-blue-600" />
              Session Results: {sessionSummary.topic}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(sessionSummary.overallScore)}`}>
                  {sessionSummary.overallScore}
                </div>
                <div className="text-sm text-gray-600">Overall Score</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {sessionSummary.completedSegments}/{sessionSummary.totalSegments}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {formatTime(sessionSummary.timeSpent)}
                </div>
                <div className="text-sm text-gray-600">Time Spent</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {sessionSummary.charactersLearned.length}
                </div>
                <div className="text-sm text-gray-600">Characters Learned</div>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Session Progress</span>
                  <span>{Math.round((sessionSummary.completedSegments / sessionSummary.totalSegments) * 100)}%</span>
                </div>
                <Progress 
                  value={(sessionSummary.completedSegments / sessionSummary.totalSegments) * 100} 
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Performance Score</span>
                  <span>{sessionSummary.overallScore}/100</span>
                </div>
                <Progress 
                  value={sessionSummary.overallScore} 
                  className="h-2"
                  indicatorClassName={getProgressColor(sessionSummary.overallScore)}
                />
              </div>
            </div>

            {/* Character Mistakes */}
            {sessionSummary.charactersMistaken.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2 flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  Characters to Review
                </h4>
                <div className="flex flex-wrap gap-1">
                  {sessionSummary.charactersMistaken.slice(0, 8).map((mistake, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {mistake.character} ({mistake.frequency}x)
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Strengths and Areas for Improvement */}
            {showDetailed && (
              <div className="grid md:grid-cols-2 gap-4 mt-4">
                {sessionSummary.strengths.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-1 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      Strengths
                    </h4>
                    <ul className="text-sm space-y-1">
                      {sessionSummary.strengths.map((strength, index) => (
                        <li key={index} className="text-gray-700">• {strength}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {sessionSummary.improvementAreas.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-1 text-blue-600">
                      <Target className="h-4 w-4" />
                      Areas to Improve
                    </h4>
                    <ul className="text-sm space-y-1">
                      {sessionSummary.improvementAreas.map((area, index) => (
                        <li key={index} className="text-gray-700">• {area}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Adaptive Learning Profile */}
      {adaptiveProfile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-600" />
              Learning Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {adaptiveProfile.totalSessions}
                </div>
                <div className="text-sm text-gray-600">Total Sessions</div>
              </div>
              
              <div className="text-center">
                <div className={`text-2xl font-bold ${getScoreColor(adaptiveProfile.averageScore)}`}>
                  {Math.round(adaptiveProfile.averageScore)}
                </div>
                <div className="text-sm text-gray-600">Average Score</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {adaptiveProfile.learningVelocity.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Learning Velocity</div>
              </div>
              
              <div className="text-center">
                <Badge className="text-sm px-3 py-1">
                  {adaptiveProfile.difficultyLevel}
                </Badge>
                <div className="text-sm text-gray-600 mt-1">Current Level</div>
              </div>
            </div>

            {/* Problematic Characters */}
            {adaptiveProfile.problematicCharacters.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm flex items-center gap-1">
                  <BarChart3 className="h-4 w-4" />
                  Character Mastery Progress
                </h4>
                
                <div className="space-y-2">
                  {adaptiveProfile.problematicCharacters
                    .sort((a, b) => a.masteryScore - b.masteryScore)
                    .slice(0, 6)
                    .map((char, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-8 text-center font-mono font-bold">
                          {char.character}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between text-xs mb-1">
                            <span>Mastery: {char.masteryScore}%</span>
                            <span>{char.mistakeCount} mistakes</span>
                          </div>
                          <Progress 
                            value={char.masteryScore} 
                            className="h-1"
                            indicatorClassName={getProgressColor(char.masteryScore)}
                          />
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            char.improvementRate > 0.7 ? 'text-green-600' : 
                            char.improvementRate > 0.3 ? 'text-yellow-600' : 'text-red-600'
                          }`}
                        >
                          {char.improvementRate > 0.7 ? '↗' : char.improvementRate > 0.3 ? '→' : '↘'}
                        </Badge>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Preferred Topics */}
            {adaptiveProfile.preferredTopics.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2">Preferred Topics</h4>
                <div className="flex flex-wrap gap-1">
                  {adaptiveProfile.preferredTopics.map((topic, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProgressVisualization;
