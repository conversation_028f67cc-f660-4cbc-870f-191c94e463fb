'use client';

import React from 'react';
import { TranslationEvaluation } from '@/types/chat';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import TextWithTTS from '@/components/ui/TextWithTTS';

interface TranslationFeedbackProps {
  evaluation: TranslationEvaluation;
  userTranslation: string;
  englishText: string;
  compact?: boolean;
}

const TranslationFeedback: React.FC<TranslationFeedbackProps> = ({
  evaluation,
  userTranslation,
  englishText,
  compact = false
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (score >= 70) return <CheckCircle className="h-4 w-4 text-blue-600" />;
    if (score >= 50) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <XCircle className="h-4 w-4 text-red-600" />;
  };

  if (compact) {
    return (
      <Card className="border-l-4 border-l-blue-500">
        <CardContent className="p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {getScoreIcon(evaluation.score)}
              <span className={`font-bold ${getScoreColor(evaluation.score)}`}>
                {evaluation.score}/100
              </span>
              <Badge variant={evaluation.isCorrect ? "default" : "destructive"} className="text-xs">
                {evaluation.isCorrect ? "✓" : "✗"}
              </Badge>
            </div>
          </div>
          <p className="text-sm text-gray-700">{evaluation.feedback}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        {/* Score Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {getScoreIcon(evaluation.score)}
            <span className={`text-xl font-bold ${getScoreColor(evaluation.score)}`}>
              {evaluation.score}/100
            </span>
            <Badge variant={evaluation.isCorrect ? "default" : "destructive"}>
              {evaluation.isCorrect ? "Correct" : "Needs Work"}
            </Badge>
          </div>
        </div>

        {/* Translations Comparison */}
        <div className="space-y-3 mb-3">
          <div>
            <p className="text-xs font-medium text-gray-500 mb-1">ENGLISH ORIGINAL</p>
            <p className="text-sm text-gray-800 bg-gray-50 p-2 rounded">{englishText}</p>
          </div>
          
          <div>
            <p className="text-xs font-medium text-gray-500 mb-1">YOUR TRANSLATION</p>
            <div className="bg-blue-50 p-2 rounded">
              <TextWithTTS text={userTranslation} lang="zh-CN" className="text-sm" />
            </div>
          </div>
          
          <div>
            <p className="text-xs font-medium text-gray-500 mb-1">CORRECT TRANSLATION</p>
            <div className="bg-green-50 p-2 rounded">
              <TextWithTTS text={evaluation.correctTranslation} lang="zh-CN" className="text-sm" />
            </div>
          </div>
        </div>

        {/* Feedback */}
        <div className="border-t pt-3">
          <p className="text-sm text-gray-700 leading-relaxed">{evaluation.feedback}</p>
        </div>

        {/* Quick Mistake Summary */}
        {evaluation.mistakeAnalysis && evaluation.mistakeAnalysis.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs font-medium text-gray-500 mb-2">
              COMMON ISSUES ({evaluation.mistakeAnalysis.length})
            </p>
            <div className="flex flex-wrap gap-1">
              {evaluation.mistakeAnalysis.slice(0, 3).map((mistake, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {mistake.character} → {mistake.expectedCharacter}
                </Badge>
              ))}
              {evaluation.mistakeAnalysis.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{evaluation.mistakeAnalysis.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Quick Suggestions */}
        {evaluation.suggestions && evaluation.suggestions.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs font-medium text-gray-500 mb-2">QUICK TIPS</p>
            <ul className="text-xs text-gray-600 space-y-1">
              {evaluation.suggestions.slice(0, 2).map((suggestion, index) => (
                <li key={index} className="flex items-start gap-1">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TranslationFeedback;
