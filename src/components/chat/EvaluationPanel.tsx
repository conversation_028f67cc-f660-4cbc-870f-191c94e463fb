'use client';

import React from 'react';
import { TranslationEvaluation } from '@/types/chat';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Trophy, 
  Target, 
  Clock, 
  Zap, 
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Star
} from 'lucide-react';

interface EvaluationPanelProps {
  currentScore: number;
  totalSegments: number;
  completedSegments: number;
  timeSpent: number;
  accuracy: number;
  recentEvaluation?: TranslationEvaluation | null;
  focusCharacters?: string[];
  sessionStats?: {
    averageScore: number;
    totalTime: number;
    correctTranslations: number;
  };
}

const EvaluationPanel: React.FC<EvaluationPanelProps> = ({
  currentScore,
  totalSegments,
  completedSegments,
  timeSpent,
  accuracy,
  recentEvaluation,
  focusCharacters = [],
  sessionStats
}) => {
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 70) return <CheckCircle className="h-5 w-5 text-blue-600" />;
    if (score >= 50) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <XCircle className="h-5 w-5 text-red-600" />;
  };

  const progressPercentage = totalSegments > 0 ? (completedSegments / totalSegments) * 100 : 0;

  return (
    <div className="h-full bg-gray-50 border-l border-gray-200 overflow-y-auto">
      <div className="p-4 space-y-4">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-lg font-bold text-gray-800 flex items-center justify-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Game Stats
          </h2>
        </div>

        {/* Current Score */}
        <Card className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold mb-1">{currentScore}</div>
            <div className="text-sm opacity-90">Current Score</div>
            <div className="flex items-center justify-center gap-1 mt-2">
              <Star className="h-4 w-4 fill-current" />
              <Star className="h-4 w-4 fill-current" />
              <Star className="h-4 w-4 fill-current" />
              {currentScore >= 80 && <Star className="h-4 w-4 fill-current" />}
              {currentScore >= 95 && <Star className="h-4 w-4 fill-current" />}
            </div>
          </CardContent>
        </Card>

        {/* Progress */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Target className="h-4 w-4" />
              Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Segments</span>
                  <span>{completedSegments}/{totalSegments}</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-center">
                <div className="bg-gray-100 rounded-lg p-2">
                  <div className="text-lg font-bold text-blue-600">{accuracy}%</div>
                  <div className="text-xs text-gray-600">Accuracy</div>
                </div>
                <div className="bg-gray-100 rounded-lg p-2">
                  <div className="text-lg font-bold text-green-600">{formatTime(timeSpent)}</div>
                  <div className="text-xs text-gray-600">Time</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Evaluation */}
        {recentEvaluation && (
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                {getScoreIcon(recentEvaluation.score)}
                Last Translation
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Score</span>
                  <span className={`font-bold ${getScoreColor(recentEvaluation.score)}`}>
                    {recentEvaluation.score}/100
                  </span>
                </div>
                
                <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                  {recentEvaluation.feedback.length > 100 
                    ? recentEvaluation.feedback.substring(0, 100) + '...'
                    : recentEvaluation.feedback
                  }
                </div>

                {recentEvaluation.mistakeAnalysis.length > 0 && (
                  <div>
                    <div className="text-xs font-medium text-gray-700 mb-1">Issues Found:</div>
                    <div className="flex flex-wrap gap-1">
                      {recentEvaluation.mistakeAnalysis.slice(0, 3).map((mistake, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {mistake.character} → {mistake.expectedCharacter}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Focus Characters */}
        {focusCharacters.length > 0 && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                Focus Characters
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-xs text-gray-600 mb-2">
                Characters you're practicing:
              </div>
              <div className="flex flex-wrap gap-1">
                {focusCharacters.slice(0, 8).map((char, index) => (
                  <Badge key={index} variant="secondary" className="text-sm font-mono">
                    {char}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Session Stats */}
        {sessionStats && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                Session Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Average Score</span>
                  <span className={`font-medium ${getScoreColor(sessionStats.averageScore)}`}>
                    {sessionStats.averageScore}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Correct Translations</span>
                  <span className="font-medium text-green-600">
                    {sessionStats.correctTranslations}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Total Time</span>
                  <span className="font-medium text-blue-600">
                    {formatTime(sessionStats.totalTime)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Motivational Messages */}
        <Card className="bg-gradient-to-r from-green-400 to-blue-500 text-white">
          <CardContent className="p-3 text-center">
            <div className="text-sm font-medium">
              {accuracy >= 90 && "🎉 Excellent work!"}
              {accuracy >= 70 && accuracy < 90 && "👍 Great progress!"}
              {accuracy >= 50 && accuracy < 70 && "💪 Keep going!"}
              {accuracy < 50 && "🌟 Every mistake is learning!"}
            </div>
            <div className="text-xs opacity-90 mt-1">
              {completedSegments === 0 && "Ready to start your conversation!"}
              {completedSegments > 0 && completedSegments < totalSegments && "You're doing great!"}
              {completedSegments === totalSegments && "Session complete!"}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EvaluationPanel;
