'use client';

import React, { useState, useEffect } from 'react';
import { ChatTopic, ChatDialogueRequest } from '@/types/chat';
import { useAdaptiveLearning } from '@/hooks/useAdaptiveLearning';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare, 
  Clock, 
  Users, 
  Target, 
  Sparkles, 
  Search,
  TrendingUp,
  BookOpen
} from 'lucide-react';

interface TopicSelectorProps {
  userId: string;
  onTopicSelect: (request: ChatDialogueRequest) => void;
  onCancel: () => void;
}

// Sample topics - in a real app, these would come from a database
const SAMPLE_TOPICS: ChatTopic[] = [
  {
    id: 'daily-routine',
    name: 'Daily Routine',
    description: 'Discuss daily activities, schedules, and habits',
    difficulty: 'beginner',
    category: 'Lifestyle',
    estimatedDuration: 15,
    keyVocabulary: ['起床', '吃饭', '工作', '睡觉', '时间'],
    culturalContext: 'Learn about typical Chinese daily schedules',
    popularity: 95
  },
  {
    id: 'food-ordering',
    name: 'Ordering Food',
    description: 'Practice ordering food at restaurants and cafes',
    difficulty: 'beginner',
    category: 'Food & Dining',
    estimatedDuration: 12,
    keyVocabulary: ['菜单', '点菜', '服务员', '买单', '好吃'],
    culturalContext: 'Chinese dining etiquette and common dishes',
    popularity: 88
  },
  {
    id: 'travel-planning',
    name: 'Travel Planning',
    description: 'Plan trips, discuss destinations, and travel experiences',
    difficulty: 'intermediate',
    category: 'Travel',
    estimatedDuration: 20,
    keyVocabulary: ['旅游', '机票', '酒店', '景点', '行程'],
    culturalContext: 'Popular Chinese travel destinations',
    popularity: 76
  },
  {
    id: 'business-meeting',
    name: 'Business Meeting',
    description: 'Professional conversations and business discussions',
    difficulty: 'advanced',
    category: 'Business',
    estimatedDuration: 25,
    keyVocabulary: ['会议', '项目', '合作', '决定', '报告'],
    culturalContext: 'Chinese business culture and etiquette',
    popularity: 62
  },
  {
    id: 'shopping',
    name: 'Shopping',
    description: 'Browse stores, ask about prices, and make purchases',
    difficulty: 'beginner',
    category: 'Shopping',
    estimatedDuration: 18,
    keyVocabulary: ['商店', '价格', '便宜', '贵', '买'],
    culturalContext: 'Chinese shopping customs and bargaining',
    popularity: 82
  },
  {
    id: 'weather-chat',
    name: 'Weather & Seasons',
    description: 'Talk about weather, seasons, and climate',
    difficulty: 'beginner',
    category: 'Small Talk',
    estimatedDuration: 10,
    keyVocabulary: ['天气', '下雨', '晴天', '冷', '热'],
    culturalContext: 'Chinese weather patterns and seasonal activities',
    popularity: 71
  }
];

const TopicSelector: React.FC<TopicSelectorProps> = ({
  userId,
  onTopicSelect,
  onCancel
}) => {
  const [selectedTopic, setSelectedTopic] = useState<ChatTopic | null>(null);
  const [customTopic, setCustomTopic] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [difficulty, setDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner');
  const [conversationLength, setConversationLength] = useState([8]);
  const [speakers, setSpeakers] = useState('2');
  const [filteredTopics, setFilteredTopics] = useState<ChatTopic[]>(SAMPLE_TOPICS);

  const {
    recommendedTopics,
    learningInsights,
    recommendedDifficulty,
    recommendedSessionLength,
    focusCharacters,
    encouragementMessage,
    generateAdaptiveRequest,
    loadTopicRecommendations
  } = useAdaptiveLearning({ userId });

  // Load recommendations on mount
  useEffect(() => {
    loadTopicRecommendations(SAMPLE_TOPICS);
    setDifficulty(recommendedDifficulty);
    setConversationLength([recommendedSessionLength]);
  }, [loadTopicRecommendations, recommendedDifficulty, recommendedSessionLength]);

  // Filter topics based on search
  useEffect(() => {
    const filtered = SAMPLE_TOPICS.filter(topic =>
      topic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      topic.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      topic.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredTopics(filtered);
  }, [searchQuery]);

  const handleTopicSelect = (topic: ChatTopic) => {
    setSelectedTopic(topic);
    setDifficulty(topic.difficulty);
  };

  const handleStartConversation = async () => {
    const topicName = selectedTopic?.name || customTopic;
    if (!topicName) return;

    try {
      const request = await generateAdaptiveRequest(topicName, {
        difficulty,
        conversationLength: conversationLength[0],
        speakers: parseInt(speakers),
        targetCharacters: focusCharacters
      });

      onTopicSelect(request);
    } catch (error) {
      console.error('Failed to generate conversation request:', error);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-6 w-6 text-blue-600" />
            Choose Your Conversation Topic
          </CardTitle>
          {encouragementMessage && (
            <p className="text-gray-600">{encouragementMessage}</p>
          )}
        </CardHeader>
      </Card>

      <Tabs defaultValue="recommended" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recommended" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Recommended
          </TabsTrigger>
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Browse All
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Custom Topic
          </TabsTrigger>
        </TabsList>

        {/* Recommended Topics */}
        <TabsContent value="recommended" className="space-y-4">
          {learningInsights && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span>Progress: {learningInsights.overallProgress}%</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span>Level: {recommendedDifficulty}</span>
                  </div>
                  {focusCharacters.length > 0 && (
                    <div className="flex items-center gap-1">
                      <span>Focus: {focusCharacters.slice(0, 3).join(', ')}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid md:grid-cols-2 gap-4">
            {recommendedTopics.map((topic) => (
              <Card 
                key={topic.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedTopic?.id === topic.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => handleTopicSelect(topic)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold">{topic.name}</h3>
                    <Badge className={getDifficultyColor(topic.difficulty)}>
                      {topic.difficulty}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{topic.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{topic.estimatedDuration}min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>{topic.category}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Browse All Topics */}
        <TabsContent value="browse" className="space-y-4">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTopics.map((topic) => (
              <Card 
                key={topic.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedTopic?.id === topic.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => handleTopicSelect(topic)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-sm">{topic.name}</h3>
                    <Badge className={getDifficultyColor(topic.difficulty)} variant="outline">
                      {topic.difficulty}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{topic.description}</p>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>{topic.estimatedDuration}min</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Custom Topic */}
        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardContent className="p-4 space-y-4">
              <div>
                <Label htmlFor="custom-topic">Enter your topic</Label>
                <Input
                  id="custom-topic"
                  placeholder="e.g., Discussing hobbies, Planning a party, Job interview..."
                  value={customTopic}
                  onChange={(e) => setCustomTopic(e.target.value)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Configuration */}
      {(selectedTopic || customTopic) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Conversation Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <Label>Difficulty Level</Label>
                <Select value={difficulty} onValueChange={(value: any) => setDifficulty(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Number of Speakers</Label>
                <Select value={speakers} onValueChange={setSpeakers}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2">2 People</SelectItem>
                    <SelectItem value="3">3 People</SelectItem>
                    <SelectItem value="4">4 People</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Conversation Length: {conversationLength[0]} segments</Label>
                <Slider
                  value={conversationLength}
                  onValueChange={setConversationLength}
                  max={15}
                  min={5}
                  step={1}
                  className="mt-2"
                />
              </div>
            </div>

            {focusCharacters.length > 0 && (
              <div>
                <Label>Focus Characters (based on your learning history)</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {focusCharacters.slice(0, 8).map((char, index) => (
                    <Badge key={index} variant="outline" className="text-sm">
                      {char}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button 
          onClick={handleStartConversation}
          disabled={!selectedTopic && !customTopic}
          className="flex-1"
        >
          Start Conversation
        </Button>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TopicSelector;
